import { config } from 'dotenv';
import dotenvExpand from 'dotenv-expand';
import fs from 'fs';
import { Configuration } from './configuration.interface';
dotenvExpand(config());

const int = (val: string | undefined, num: number): number =>
  val ? (isNaN(parseInt(val)) ? num : parseInt(val)) : num;
const bool = (val: string | undefined, bool: boolean): boolean =>
  val == null ? bool : val == 'true';

function configFunction(): Configuration {
  const privateKey = process.env['PRIVATE_KEY_PEM'] ?? fs.readFileSync('./cert/private_key.pem');
  const publicKey = process.env['PUBLIC_KEY_PEM'] ?? fs.readFileSync('./cert/public_key.pem');
  const wikijsPublicKey =
    process.env['WIKIJS_PUBLIC_KEY_PEM'] ?? fs.readFileSync('./cert/wikijs_public_key.pem');

  const configuration: Configuration = {
    backendEnv: process.env['BACKEND_ENV'] ?? '',
    maxRemarksCount: int(process.env['MAX_REMARKS_COUNT'], 100),
    hktEmailSuffix: process.env['HKT_EMAIL_SUFFIX'] ?? 'pccw.com',
    frontendUrl: process.env['FRONTEND_URL'] ?? 'http://localhost:3000',
    frontendDomain: process.env['FRONTEND_DOMAIN'] ?? 'localhost',
    meta: {
      appName: process.env['APP_NAME'] ?? 'Staart',
      domainVerificationFile: process.env['DOMAIN_VERIFICATION_FILE'] ?? 'staart-verify.txt',
    },
    rateLimit: {
      points: int(process.env['RATE_LIMIT_MAX'], 100),
      duration: int(process.env['RATE_LIMIT_TIME'], 60),
      sendEmailUrl: process.env['RATE_LIMIT_EMAIL_NOTIFICATION_API_URL'] ?? '',
      rateLimitEX: int(process.env['RATE_LIMIT_EXPIRE_TIME'], 3600),
      rateLimitTimeOut: int(process.env['RATE_LIMIT_TIME_OUT'], 30000),
    },
    caching: {
      geolocationLruSize: int(process.env['GEOLOCATION_LRU_SIZE'], 100),
      apiKeyLruSize: int(process.env['API_KEY_LRU_SIZE'], 100),
      apiKeyTtl: int(process.env['API_KEY_TTL'], 3600),
    },
    security: {
      saltRounds: int(process.env['SALT_ROUNDS'], 10),
      jwtSecret: process.env['JWT_SECRET'] ?? 'staart',
      totpWindowPast: int(process.env['TOTP_WINDOW_PAST'], 1),
      totpWindowFuture: int(process.env['TOTP_WINDOW_FUTURE'], 0),
      mfaTokenExpiry: process.env['MFA_TOKEN_EXPIRY'] ?? '10m',
      mergeUsersTokenExpiry: process.env['MERGE_USERS_TOKEN_EXPIRY'] ?? '30m',
      accessTokenExpiry: process.env['ACCESS_TOKEN_EXPIRY'] ?? '15m',
      refreshTokenExpiry: process.env['REFRESH_TOKEN_EXPIRY'] ?? '1d',
      passwordPwnedCheck: bool(process.env['PASSWORD_PWNED_CHECK'], true),
      unusedRefreshTokenExpiryDays: int(process.env['DELETE_EXPIRED_SESSIONS'], 30),
      inactiveUserDeleteDays: int(process.env['INACTIVE_USER_DELETE_DAYS'], 30),
      privateKey: privateKey,
      publicKey: publicKey,
    },
    elasticSearch: {
      nodes: process.env['ELASTICSEARCH_NODES'].split(','),
      retries: int(process.env['ELASTICSEARCH_FAIL_RETRIES'], 3),
      auth: process.env['ELASTICSEARCH_AUTH_USERNAME']
        ? {
            username: process.env['ELASTICSEARCH_AUTH_USERNAME'],
            password: process.env['ELASTICSEARCH_AUTH_PASSWORD'],
          }
        : process.env['ELASTICSEARCH_AUTH_API_KEY']
          ? process.env['ELASTICSEARCH_AUTH_API_KEY_ID']
            ? {
                apiKey: {
                  api_key: process.env['ELASTICSEARCH_AUTH_API_KEY'],
                  id: process.env['ELASTICSEARCH_AUTH_API_KEY_ID'],
                },
              }
            : { apiKey: process.env['ELASTICSEARCH_AUTH_API_KEY'] }
          : undefined,
      aws: {
        accessKeyId: process.env['ELASTICSEARCH_AWS_ACCESS_KEY_ID'] ?? '',
        secretAccessKey: process.env['ELASTICSEARCH_AWS_SECRET_ACCESS_KEY'] ?? '',
        region: process.env['ELASTICSEARCH_AWS_REGION'] ?? '',
      },
      ca: process.env['ELASTIC_CA'],
    },
    email: {
      name: process.env['EMAIL_NAME'] ?? 'Staart',
      from: process.env['EMAIL_FROM'] ?? '',
      retries: int(process.env['EMAIL_FAIL_RETRIES'], 3),
      ses: {
        accessKeyId: process.env['EMAIL_SES_ACCESS_KEY_ID'] ?? '',
        secretAccessKey: process.env['EMAIL_SES_SECRET_ACCESS_KEY'] ?? '',
        region: process.env['EMAIL_SES_REGION'] ?? '',
      },
      transport: {
        host: process.env['EMAIL_HOST'] ?? '',
        port: int(process.env['EMAIL_PORT'], 587),
        secure: bool(process.env['EMAIL_SECURE'], false),
        auth: {
          user: process.env['EMAIL_USER'] ?? '',
          pass: process.env['EMAIL_PASSWORD'] ?? '',
        },
      },
    },
    webhooks: {
      retries: int(process.env['WEBHOOK_FAIL_RETRIES'], 3),
    },
    sms: {
      retries: int(process.env['SMS_FAIL_RETRIES'], 3),
      twilioAccountSid: process.env['TWILIO_ACCOUNT_SID'] ?? '',
      twilioAuthToken: process.env['TWILIO_AUTH_TOKEN'] ?? '',
    },
    payments: {
      stripeApiKey: process.env['STRIPE_API_KEY'] ?? '',
      stripeProductId: process.env['STRIPE_PRODUCT_ID'] ?? '',
      stripeEndpointSecret: process.env['STRIPE_ENDPOINT_SECRET'] ?? '',
      paymentMethodTypes: ['card'],
    },
    tracking: {
      mode: (process.env['TRACKING_MODE'] as Configuration['tracking']['mode']) ?? 'api-key',
      index: process.env['TRACKING_INDEX'] ?? 'staart-logs',
      deleteOldLogs: bool(process.env['TRACKING_DELETE_OLD_LOGS'], true),
      deleteOldLogsDays: int(process.env['TRACKING_DELETE_OLD_LOGS_DAYS'], 90),
    },
    slack: {
      token: process.env['SLACK_TOKEN'] ?? '',
      slackApiUrl: process.env['SLACK_API_URL'],
      rejectRateLimitedCalls: bool(process.env['SLACK_REJECT_RATE_LIMITED_CALLS'], false),
      retries: int(process.env['SLACK_FAIL_RETRIES'], 3),
    },
    s3: {
      accessKeyId: process.env['AWS_S3_ACCESS_KEY'] ?? '',
      secretAccessKey: process.env['AWS_S3_SECRET_KEY'] ?? '',
      region: process.env['AWS_S3_REGION'] ?? '',
      endpoint: process.env['AWS_S3_ENDPOINT'] ?? '',
      provider: process.env['FILE_STORE_PROVIDER'] ?? 's3',
      profilePictureBucket: process.env['AWS_S3_PROFILE_PICTURE_BUCKET'] ?? '',
      profilePictureCdnHostname: process.env['AWS_S3_PROFILE_PICTURE_CDN_HOST_NAME'] ?? '',
      staticFilesBuckets: {
        PROD: process.env['AWS_S3_STATIC_FILES_BUCKET_PROD'] ?? '',
        TEST: process.env['AWS_S3_STATIC_FILES_BUCKET_TEST'] ?? '',
      },
      reportFilesBuckets: process.env['AWS_S3_FILES_HISTORY_BUCKET'] ?? '',
      staticFilesMaxSize: parseInt(process.env['AWS_S3_STATIC_FILES_MAX_SIZE'] ?? '25000000'),
      kmsEncryptionKeyArn: process.env['AWS_S3_ENCRYPTION_KEY_ARN'] ?? '',
      piiReportBuckets: {
        PROD: process.env['AWS_S3_PII_REPORT_BUCKET_PROD'] ?? '',
        TEST: process.env['AWS_S3_PII_REPORT_BUCKET_TEST'] ?? '',
      },
      dailyReportBuckets: {
        PROD: process.env['AWS_S3_SCAN_PROMPT_OUTPUT_DAILY_REPORT_BUCKET_PROD'] ?? '',
        TEST: process.env['AWS_S3_SCAN_PROMPT_OUTPUT_DAILY_REPORT_BUCKET_TEST'] ?? '',
      },
      publicFilesBuckets: process.env['AWS_S3_PUBLIC_FILES_BUCKET'] ?? '',
      staticFilesBucket: process.env['AWS_S3_STATIC_FILES_BUCKET'] ?? '',
    },
    gcs: {
      accessKey: process.env['VERTEX_AI_ACCOUNT_KEY'] ?? '',
      bucket: process.env['GCS_FILE_BUCKET'] ?? '',
    },
    queue: {
      accessKeyId: process.env['AWS_SQS_ACCESS_KEY'] ?? '',
      secretAccessKey: process.env['AWS_SQS_SECRET_KEY'] ?? '',
      region: process.env['AWS_SQS_REGION'] ?? '',
      provider: process.env['QUEUE_PROVIDER'] ?? 'sqs',
      fileQueueUrl: process.env['AWS_SQS_FILES_QUEUE_URL'] ?? '',
      dataProcessQueueUrl: {
        PROD: process.env['AWS_SQS_DATA_PROCESS_QUEUE_URL_PROD'] ?? '',
        TEST: process.env['AWS_SQS_DATA_PROCESS_QUEUE_URL_TEST'] ?? '',
      },
    },
    cloudinary: {
      cloudName: process.env['CLOUDINARY_CLOUD_NAME'] ?? '',
      apiKey: process.env['CLOUDINARY_API_KEY'] ?? '',
      apiSecret: process.env['CLOUDINARY_API_SECRET'] ?? '',
    },
    github: {
      auth: process.env['GITHUB_AUTH'],
      userAgent: process.env['GITHUB_USER_AGENT'],
    },
    googleMaps: {
      apiKey: process.env['GOOGLE_MAPS_API_KEY'],
    },
    gravatar: {
      enabled: bool(process.env['PASSWORD_PWNED_CHECK'], true),
    },
    llm: {
      backend: process.env['OPENAI_BACKEND_URL'],
      backend_timeout: 120000,
      backend_api_key: process.env['OPENAI_BACKEND_API_KEY'],
    },
    ssoHkt: {
      userInfo: process.env['SSO_HKT_URL'] + process.env['SSO_HKT_USER_INFO_ENDPOINT'],
      healthCheck: process.env['SSO_HKT_URL'] + process.env['SSO_HKT_HEALTH_CHECK_ENDPOINT'],
    },
    ssoAzure: {
      userInfo: process.env['SSO_Azure_User_INFO'] ?? 'https://graph.microsoft.com/v1.0/me',
    },
    secrets: {
      embeddings: process.env['SECRET_EMBEDDINGS'] ?? '',
      aesKey: process.env['SECRET_AES_KEY'] ?? 'imageToken',
    },
    gravitee: {
      orgId: process.env['GRAVITEE_ORG_ID'],
      envId: process.env['GRAVITEE_ENV_ID'],
      graviteeHost: process.env['GRAVITEE_HOST'],
      graviteeApiKey: process.env['GRAVITEE_API_KEY'],
      botApiId: process.env['BOT_API_ID'],
      apiPathPattern: process.env['API_PATH_PATTERN'],
    },
    aiResource: {
      callbackKey: process.env['RESOURCE_CALLBACK_KEY'],
      genResQueue: {
        live: process.env['AWS_SQS_GEN_RES_LIVE_QUEUE_URL'],
        test: process.env['AWS_SQS_GEN_RES_TEST_QUEUE_URL'],
      },
    },
    kYCVerification: {
      expiryDay: parseInt(process.env['VERIFICATION_CODE_EXPIRY_DAY']) ?? 1,
    },
    wikijs: {
      host: process.env['WIKIJS_HOST'],
      apiKey: process.env['WIKIJS_API_KEY'],
      guestGroupId: process.env['WIKIJS_GUEST_GROUP_ID']
        ? parseInt(process.env['WIKIJS_GUEST_GROUP_ID'])
        : 2,
      guestUserId: process.env['WIKIJS_GUEST_USER_ID']
        ? parseInt(process.env['WIKIJS_GUEST_USER_ID'])
        : 2,
      ssoProviderKey: process.env['WIKIJS_SSO_PROVIDER_KEY'],
      defaultPassword: process.env['WIKIJS_DEFAULT_PASSWORD'] ?? 'abcD!234',
      token: {
        publicKey: wikijsPublicKey,
        issuer: process.env['WIKIJS_TOKEN_ISSUER'],
      },
      genKBChatHostContext: process.env['WIKIJS_GENKB_CHAT_HOST_CONTEXT'],
    },
    flow: {
      backend: process.env['FLOWISE_BACKEND_URL'],
      backendTimeout: 120000,
      api_log: bool(process.env['FLOWISE_API_LOG'], false),
      api_log_include_header: bool(process.env['FLOWISE_API_LOG_INCLUDE_HEADER'], false),
      apiKeyHeader: process.env['FLOWISE_API_KEY_HEADER'],
      apiKey: process.env['FLOWISE_API_KEY'],
      enableChatDebug: bool(process.env['FLOW_DEBUG'], true),
    },
    cronJob: {
      apiKey: process.env['CRON_JOB_API_KEY'],
    },
    changeManagement: {
      approvalTransactionTimeout: process.env['CHANGE_MANAGEMENT_APPROVAL_TRANSACTION_TIMEOUT']
        ? parseInt(process.env['CHANGE_MANAGEMENT_APPROVAL_TRANSACTION_TIMEOUT'])
        : 30_000,
    },
    redis: {
      url: process.env['REDIS_URL'],
      tls: bool(process.env['REDIS_TLS'], false),
      bullMqUrl: process.env['BULL_MQ_REDIS_URL'],
    },
    postman: {
      apiKey: process.env['POSTMAN_MONITOR_API_KEY'],
      groupId: Number(process.env['POSTMAN_MONITOR_GROUP_ID']),
      scopes: process.env['POSTMAN_MONITOR_SCOPES']
        ? process.env['POSTMAN_MONITOR_SCOPES'].split(',')
        : [],
    },
    llmGuard: {
      backendPrefix: process.env['LLMGUARD_BACKEND_PREFIX_URL'],
      promptPrefix: process.env['LLMGUARD_PROMPT_PREFIX_URL'],
      fullScanPiiVersion: parseInt(process.env['FULL_SCAN_PII_VERSION']),
    },
    chatWithData: {
      clientUrl: process.env['CHAT_WITH_DATA_URL'],
      authKey: process.env['CHAT_WITH_DATA_AUTH_KEY'],
      timeout: parseInt(process.env['CHAT_WITH_DATA_REQUEST_TIMEOUT']) ?? 60000,
    },
    internalChat: {
      authKey: process.env['INTERNAL_CHAT_AUTH_KEY'] ?? '',
    },
    insightGenerator: {
      host: process.env['INSIGHT_GENERATOR_URL'],
      timeout: parseInt(process.env['INSIGHT_GENERATOR_TIMEOUT']) || 60000,
    },
    jobQueue: {
      backendPrefix: process.env['JOB_QUEUE_PREFIX_URL'],
      reScanPiiCallbackUrl: process.env['RE_SCAN_PII_CALLBACK_URL'],
      authKey: process.env?.['JOB_QUEUE_AUTH_KEY'] ?? 'job-queue',
    },
    splitFileSize: parseInt(process.env['DISTRITUBE_QUEUE_BY_FILE_SIZE']),
    defaultPriorityLimit: parseInt(process.env['DEFAULT_PRIORITY_LIMIT']),

    autoTest: {
      authKey: process.env['BOT_BUILDER_AUTO_TEST_AUTH_KEY'] ?? '11',
      clientUrl: process.env['BOT_BUILDER_AUTO_TEST_CLIENT_URL'] ?? 'http://localhost:3005',
      timeout: parseInt(process.env['BOT_BUILDER_AUTO_TEST_CLIENT_TIMEOUT'] ?? '30000'),
    },
    scanMalware: {
      backendPrefix: process.env['SCAN_MALWARE_PREFIX_URL'],
      callbackUrl: process.env['SCAN_MALWARE_CALLBACK_URL'],
      reScanCallbackUrl: process.env['RE_SCAN_MALWARE_CALLBACK_URL'],
      chatFileCallbackUrl: process.env['SCAN_CHAT_FILE_MALWARE_CALLBACK_URL'],
    },
    notification: {
      clientUrl: process.env['BOT_BUILDER_NOTIFICATION_CLIENT_URL'] ?? 'http://localhost:3005',
      timeout: parseInt(process.env['BOT_BUILDER_NOTIFICATION_CLIENT_TIMEOUT'] ?? '30000'),
      authKey: process.env['BOT_BUILDER_NOTIFICATION_AUTH_KEY'] ?? '',
    },
    lastUsedGroups: {
      maxNum: parseInt(process.env['LAST_USED_GROUPS_MAX_NUMBER']) ?? 5,
    },
    litellm: {
      baseUrl: process.env['LITE_LLM_API_BASE'] ?? '',
      apiKey: process.env['LITE_LLM_API_KEY'] ?? '',
    },
    n8n: {
      baseUrl: process.env['N8N_BASE_URL'] ?? '',
    },
    batchProcessor: {
      baseUrl: process.env['BATCH_PROCESSOR_BASE_URL'],
      apiKey: process.env['BATCH_PROCESSOR_API_KEY'],
    },
    llmEngines: {
      configHide: bool(process.env['LLM_ENGINES_CONFIG_HIDE'], true),
    },
    appCenterKeycloak: {
      baseUrl: process.env['SSO_BASE_URL'] ?? 'https://dev.bot-builder.pccw.com/gbot-auth',
      realm: process.env['SSO_REALM'] ?? 'HKT',
      adminUsername: process.env['SSO_ADMIN_USERNAME'] ?? 'admin',
      adminPassword: process.env['SSO_ADMIN_PASSWORD'] ?? 'admin',
      groupRolesMappingKey: process.env['SSO_GROUP_ROLE_MAPPING_KEY'] ?? 'groupId_role',
      clientSecret: process.env['SSO_BACKEND_CLIENT_SECRET'] ?? '123',
      clientId: process.env['SSO_BACKEND_CLIENT_ID'] ?? 'gbot',
      issuer: process.env['SSO_ISSUE'] ?? 'gbot',
    },
    documentAi: {
      authKey: process.env['DOCUMENT_AI_AUTH_KEY'] ?? '',
    },	
    fileConversion: {
      url: process.env['FILE_CONVERSION_URL'] ?? 'http://bot-builder-file-converter/file-converter',
      callbackUrl:
        process.env['FILE_CONVERSION_CALLBACK_URL'] ??
        'http://bot-builder-backend/v1/file-conversion/callback',
      callbackSecretKeyName:
        process.env['FILE_CONVERSION_CALLBACK_SECRET_KEY_NAME'] ?? 'BACKEND_SECRET_EMBEDDINGS',
      apiKey: process.env['FILE_CONVERSION_API_KEY'] ?? 'file-conversion-key',
    },
  };
  return configuration;
}
export default configFunction;
