import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { ChangeManagementAdminController } from './change-management-admin.controller';
import { ChangeManagementGroupController } from './change-management-group.controller';
import { ChangeManagementService } from './change-management.service';
import { FlowsModule } from '../flows/flows.module';
import { ApiResourceModule } from '../api-resource/api-resource.module';
import { ConfigModule } from '@nestjs/config';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { GroupSettingsModule } from '../group-settings/group-settings.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    ApiKeysModule,
    LLMModelsModule,
    FlowsModule,
    ApiResourceModule,
    BotSecurityModule,
    GroupSettingsModule,
  ],
  controllers: [ChangeManagementAdminController, ChangeManagementGroupController],
  providers: [ChangeManagementService],
  exports: [ChangeManagementService],
})
export class ChangeManagementModule {}
