import { Body, Controller, Get, Param, ParseIntPipe, Post, Put, Query, Res } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AiResource } from '@prisma/client';
import { Response } from 'express';
import { CursorPipe } from 'src/pipes/cursor.pipe';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { Scopes } from '../auth/scope.decorator';
import { AiResourcesService } from './ai-resources.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Readable } from 'stream';

@Controller('groups/:groupId/ai-resources')
@ApiBearerAuth('bearer-auth')
@ApiTags('AI Resources')
export class AiResourcesController {
  constructor(
    private aiResourcesService: AiResourcesService,
    private configService: ConfigService,
  ) {}

  @Get()
  @Scopes('group-{groupId}:read-ai-resource')
  async getAiResources(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
  ): Promise<{ list: AiResource[]; count: number }> {
    const list = await this.aiResourcesService.getAiResourcesForGroup(groupId, skip, take, where);
    const count = await this.aiResourcesService.getAiResourcesCountForGroup(groupId, where);

    return {
      list,
      count,
    };
  }

  //download (call s3)
  @Get(':id/files/:filename')
  @Scopes('group-{groupId}:read-ai-resource')
  @AuditLog('download-ai-resource')
  async downloadAiResource(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Param('filename') filename: string,
    @Res() response: Response,
  ) {
    const fileResponse = await this.aiResourcesService.downloadAiResource(groupId, id, filename);
    (fileResponse.Body as Readable).on('error', function () {
        response.status(404).json({ statusCode: 404, message: 'Not Found' });
      })
      .pipe(response);
  }

  //generate (call sqs)
  @Post()
  @Scopes('group-{groupId}:write-ai-resource')
  generateAiResources(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() request: any,
  ): Promise<AiResource> {
    return this.aiResourcesService.generateAiResources(groupId, request);
  }

  // callback
  @Put(':id')
  @AuditLog('resource-generation-callback')
  @Scopes('resource-generation-callback')
  resourceGenerationCallback(
    @Param('id', ParseIntPipe) id: number,
    @Body() callback: any,
  ): Promise<AiResource> {
    return this.aiResourcesService.resourceGenerationCallback(id, callback);
  }
}
