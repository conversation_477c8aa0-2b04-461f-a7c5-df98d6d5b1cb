import { Test, TestingModule } from '@nestjs/testing';
import { ScopeService } from './scope.service';
import { PrismaClient, ResourceSubsciberType } from '@prisma/client';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';

const moduleMocker = new ModuleMocker(global);

describe('ScopeService', () => {
  let scopeService: ScopeService;
  let prismaService: DeepMockProxy<{
    // this is needed to resolve the issue with circular types definition
    // https://github.com/prisma/prisma/issues/10203
    [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
  }>;

  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      // this is needed to resolve the issue with circular types definition
      // https://github.com/prisma/prisma/issues/10203
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScopeService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    scopeService = module.get(ScopeService);
  });

  describe('getAdditionalPermissions', () => {
    it('should return additional permission keys for a user', async () => {
      const resources = [
        { id: 1, resourceKey: 'llm-engine-gpt-35-turbo-16k' },
        { id: 2, resourceKey: 'llm-engine-gpt-4' },
      ];
      prismaService.resource.findMany.mockResolvedValue(resources as any[]);
      const defaultFeatureFlag = {
        metaData: {
          'llm-engine-gpt-35-turbo-16k': 'llm-engine-gpt-35-turbo-16k-plan-1',
          'llm-engine-gpt-4': 'llm-engine-gpt-4-plan-1',
        },
        isEnabled: true,
      };
      prismaService.featureFlag.findUnique.mockResolvedValue(defaultFeatureFlag as any);
      prismaService.planSubscription.count.mockResolvedValue(0);
      prismaService.plan.findFirst.mockResolvedValue({ id: 1 } as any);
      prismaService.plan.findUnique.mockResolvedValue({ id: 1 } as any);
      const planPermissions = [{ permission: { permissionKey: '{groupId}-enable-gpt-4' } }];
      const planPermissions2 = [{ permission: { permissionKey: '{groupId}-enable-gpt-3.5' } }];

      prismaService.planPermission.findMany
        .mockResolvedValueOnce(planPermissions as any[])
        .mockResolvedValue(planPermissions2 as any[]);
      const result = await scopeService.getResourcePlanPermissions(
        10111111,
        ResourceSubsciberType.BOT,
      );
      expect(result).toEqual(['10111111-enable-gpt-4', '10111111-enable-gpt-3.5']);
    });
  });
});
