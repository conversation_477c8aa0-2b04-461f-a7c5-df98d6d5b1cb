import { IsString, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class CallbackPayloadDto {
  @IsString()
  jobId: string;

  @IsString()
  status: 'SUCCESS' | 'FAILED';

  @IsString()
  docId: string;

  @IsNumber()
  groupId: number;

  @IsString()
  @IsOptional()
  modelId?: string;

  @IsString()
  fileType: 'modelFile' | 'chatFile';

  @IsNumber()
  fileId: number;

  @IsString()
  @IsOptional()
  errorMessage?: string;
}
