import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { Module, forwardRef } from '@nestjs/common';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { ConfigModule } from '@nestjs/config';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { FlowsModule } from '../flows/flows.module';
import { ApiResourceModule } from '../api-resource/api-resource.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { GroupSettingsService } from './group-settings.service';
import { ChangeManagementModule } from '../change-management/change-management.module';
import { GroupSettingsController } from './group-settings.controller';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    ApiKeysModule,
    LLMModelsModule,
    FlowsModule,
    ApiResourceModule,
    BotSecurityModule,
    forwardRef(() => ChangeManagementModule),
  ],
  controllers: [GroupSettingsController],
  providers: [GroupSettingsService],
  exports: [GroupSettingsService],
})
export class GroupSettingsModule {}
