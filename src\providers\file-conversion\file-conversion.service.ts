import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import FormData from 'form-data';

export interface CallbackAuth {
  type: 'S-API-KEY';
  secretKey: string;
}

export interface RequestConversionPayload {
  fileType: string;
  userId: string;
  callbackUrl?: string;
  callbackAuth?: CallbackAuth;
  callbackPayload?: Record<string, unknown>;
}

@Injectable()
export class FileConversionService {
  private logger = new Logger(FileConversionService.name);

  constructor(private readonly configService: ConfigService) {}

  async requestConversion(
    payload: RequestConversionPayload,
    fileBuffer: Buffer,
    filename: string,
  ): Promise<void> {
    const { fileType, userId, callbackUrl, callbackAuth, callbackPayload } = payload;
    const conversionServiceUrl = this.configService.get<string>('fileConversion.url');
    this.logger.debug(
      `[FileConversionService.requestConversion] conversionServiceUrl: ${conversionServiceUrl}, payload: ${JSON.stringify(
        payload,
      )}`,
    );

    if (!conversionServiceUrl) {
      this.logger.error('File conversion service URL is not configured.');
      return;
    }

    this.logger.log(`Requesting conversion for file: ${filename}`);

    try {
      const form = new FormData();
      form.append('file', fileBuffer, filename);
      form.append('fileType', fileType);
      form.append('userId', userId);

      if (callbackUrl) {
        form.append('callbackUrl', callbackUrl);
      }
      if (callbackAuth) {
        form.append('callbackAuth', JSON.stringify(callbackAuth));
      }
      if (callbackPayload) {
        form.append('callbackPayload', JSON.stringify(callbackPayload));
      }

      const conversionServiceApiKey = this.configService.get<string>('fileConversion.apiKey');
      await axios.post(conversionServiceUrl, form, {
        headers: {
          ...form.getHeaders(),
          'x-api-key': conversionServiceApiKey,
        },
        maxBodyLength: 50 * 1024 * 1024, // 50MB
        maxContentLength: 50 * 1024 * 1024, // 50MB
      });
    } catch (error) {
      this.logger.error(`Failed to request file conversion for ${filename}`, error);
      throw error;
    }
  }

  async getConvertedFileUrl(jobId: string): Promise<string> {
    const conversionServiceUrl = this.configService.get<string>('fileConversion.url');
    this.logger.debug(
      `[FileConversionService.getConvertedFileUrl] conversionServiceUrl: ${conversionServiceUrl}, jobId: ${jobId}`,
    );
    if (!conversionServiceUrl) {
      this.logger.error('File conversion service URL is not configured.');
      throw new Error('File conversion service URL is not configured.');
    }
    try {
      const conversionServiceApiKey = this.configService.get<string>('fileConversion.apiKey');
      const response = await axios.get<{ downloadUrl: string }>(
        `${conversionServiceUrl}/${jobId}/download-url`,
        {
          headers: { 'x-api-key': conversionServiceApiKey },
        },
      );
      return response.data.downloadUrl;
    } catch (error) {
      this.logger.error(`Failed to get converted file for ${jobId}`, error);
      throw error;
    }
  }
}
