export interface LLMModelCreateResponse {
  id: string;
  name: string;
  tone: string;
  startup_message: string;
  documents: any[];
  admins: string[];
  users: string[];
}

export type LLMModelKnowledgeBasePeer = {
  kb_id: number;
  token: string;
  description?: string;
  name?: string;
};

export type LLMModelGenKB = {
  kb_id: number;
  description?: string;
  name?: string;
}

export type LLMModelKnowledgeBase = {
  enable?: boolean;
  token?: string;
  peer_kbs?: LLMModelKnowledgeBasePeer[];
};

export type LLMModeResponse = {
  id: string;
  name: string;
  tone: string;
  startup_message: string;
  documents: string[];
  admins: string[];
  users: string[];
  knowledge_base?: LLMModelKnowledgeBase;
};

export type LLMModelFileUploadedResponse = {
  source: string;
  file_id: string;
  status: string;
};

export enum ROLE {
  USER = 'user',
  // SYSTEM = 'system',
  AI = 'assistant',
  FUNCTION = 'function',
  TOOL = 'tool',
}

export enum MODEL {
  GPT_35_TURBO = 'gpt-35-turbo',
  GPT_35_TURBO_0613 = 'gpt-35-turbo-0613',
  GPT_35_TURBO_16K = 'gpt-35-turbo-16k',
  GPT_4 = 'gpt-4',
  GPT_4_32K = 'gpt-4-32k',
  GPT_4_0125 = 'gpt-4-0125',
  GPT_4O = 'gpt-4o',
  GPT_4O_MINI = 'gpt-4o-mini',
  O1_PREVIEW = 'o1-preview',
  O1_MINI = 'o1-mini',
  DEEPSEEK_R1 = 'deepseek-r1',
  DEEPSEEK_V3 = 'deepseek-v3',
  QWQ_32B = 'qwq-32b',
  QWEN_MAX = 'qwen-max',
  QWEN_PLUS = 'qwen-plus',
  QWEN_TURBO = 'qwen-turbo',
  PHI_4 = 'phi-4',
  DALLE_3 = 'dalle-3',
  STABLE_DIFUSSION_TEXT_TO_IMG = 'sd-txt2img',
  SENSENOVA_XL = 'nova-ptc-xl-v1',
  SENSENOVA_XS = 'nova-ptc-xs-v1',
  SENSENOVA_YUE = 'nova-ptc-yue-xl-v1',
  SENSENOVA_SENSECHAT_5_CANTONESE = 'nova-sensechat-5-cantonese',
  SENSENOVA_SENSECHAT_5 = 'nova-sensechat-5',
  SENSENOVA_SENSECHAT_128K = 'nova-sensechat-128k',
  AZURE_SPEECH = 'azure-speech',
  VERTEX_CHAT_BISON = 'vertexai-chat-bison',
  VERTEX_CHAT_BISON_32K = 'vertexai-chat-bison-32k',
  VERTEX_CHAT_BISON_002 = 'vertexai-chat-bison-002',
  VERTEX_CODECHAT_BISON_LATEST = 'vertexai-codechat-bison-latest',
  VERTEX_CODECHAT_BISON_32K_LATEST = 'vertexai-codechat-bison-32k-latest',
  VERTEX_CODECHAT_BISON_002 = 'vertexai-codechat-bison-002',
  VERTEX_GEMINI_1_5_FLASH = 'vertexai-gemini-1.5-flash-preview-0514',
  VERTEX_GEMINI_1_5_PRO = 'vertexai-gemini-1.5-pro-preview-0409',
  VERTEX_GEMINI_1_5_FLASH_001 = 'vertexai-gemini-1.5-flash-001',
  VERTEX_GEMINI_1_5_PRO_001 = 'vertexai-gemini-1.5-pro-001',
  VERTEX_GEMINI_1_5_FLASH_002 = 'vertexai-gemini-1.5-flash-002',
  VERTEX_GEMINI_1_5_PRO_002 = 'vertexai-gemini-1.5-pro-002',
  // 2.0 models
  VERTEX_GEMINI_2_0_PRO_EXP = 'vertexai-gemini-2.0-pro',
  VERTEX_GEMINI_2_0_FLASH_LITE = 'vertexai-gemini-2.0-flash-lite',
  VERTEX_GEMINI_2_0_FLASH_LITE_PREVIEW = 'vertexai-gemini-2.0-flash-lite-preview',
  VERTEX_GEMINI_2_0_FLASH_THINKING_EXP = 'vertexai-gemini-2.0-flash-thinking-exp',
  VERTEX_GEMINI_2_0_FLASH_001 = 'vertexai-gemini-2.0-flash-001',
  VERTEX_GEMINI_2_5_PRO = 'vertexai-gemini-2.5-pro',
  VERTEX_IMAGEN_3_0_GENERATE_001 = 'vertexai-imagen-3.0-generate-001',
  VERTEX_IMAGEN_3_0_FAST_GENERATE_001 = 'vertexai-imagen-3.0-fast-generate-001',
}

/**
 * @description  CWD: chat with data (currently temp file just allow PLAYGROUND), CWF: chat with file (currently  temp file just allow PLAYGROUND),
 * RRR (just chat with user input), RTR(rtr is reserved)
 */
export enum CHAT_APPROACH {
  RRR = 'rrr',
  RTR = 'rtr',
  CWF = 'cwf',
  CWD = 'cwd',
}

export enum SOURCE {
  VECTOR_STORE = 'vector-store',
  UPLOAD = 'upload',
}

export type Usage = {
  promptTokens: number;
  completionTokens: number;
  totalCompletionTokens: number;
  embeddingTokens: number;
  imageUsage?: ImageUsage;
  audioUsage?: AudioUsage;
};

export type ImageUsage = {
  quality?: string;
  resolution?: string;
};

export type AudioUsage = {
  chars_count?: number;
  file_length?: number;
};

export type overall = {
  answer: string;
  message: LLMmessage;
  usage: Usage;
  content_points: ContentPoint[];
  file_expired_at?: Date | null;
  chatHistoryId?: number;
  showReference?: boolean;
  userPromptHistoryId?: number;
};

export type LLMmessage = {
  role: string;
  content: string;
};

export type ContentPoint = {
  id: number;
  filename?: string;
  file_id?: string;
  page?: string;
  data_source?: string;
  cited?: boolean;
  url?: string;
};

export type FilePoint = {
  data_source: string;
  file_id: string;
  filename: string;
  page: number;
};

export type ChatResponse = {
  answer?: string;
  data_points?: string[];
  thoughts?: string;
  usage?: Usage;
  file_points?: FilePoint[];
  file_expired_at?: Date | null;
  content_points?: ContentPoint[];
  output_scanners_result?: ScannnersOutputResultDTO;
  input_scanners_result?: ScannersInputResultDTO;
  input_scanners_result_is_valid?: boolean;
  output_scanners_result_is_valid?: boolean;
  image_usage?: ImageUsage;
  chatHistoryId?: number;
  showReference?: boolean;
};

export type ScannersInputResultDTO = {
  scanners: botSecurityScannerResultDTO[];
  sanitized_prompt: string;
  original_prompt: string;
};

export class botSecurityScannerResultDTO {
  name: string;
  risk_score: number;
  security_level: string;
}
export class ScannnersOutputResultDTO {
  scanners: botSecurityScannerResultDTO[];
  sanitized_output: string;
  original_output: string;
}

export type GenerateRequest = {
  async?: boolean;
  model: string;
  function: string;
  overrides: {
    text?: string;
    voice_name?: string;
    file?: string;
    s3_key?: string;
    recognition_language?: string;
    auto_detect_source_language?: boolean;
    stream?: boolean;
  };
  groupId?: number;
  userId?: number;
};

export type GenerateResponse = {
  file?: string;
  text?: string;
  data?: {
    text?: string;
    file?: string;
    url?: string;
  };
  usage: {
    file_length?: number;
    chars_count?: number;
  };
};

export type SttOverall = {
  data?: {
    text?: string;
    file?: string;
  };
  usage: {
    file_length?: number;
    chars_count?: number;
  };
};

export enum EMBEDDINGS_MODEL {
  TEXT_EMBEDDING_ADA_002 = 'azure-text-embedding-ada-002',
  TEXT_EMBEDDING_3_SMALL = 'azure-text-embedding-3-small',
  TEXT_EMBEDDING_3_LARGE = 'azure-text-embedding-3-large',
  TEXT_MULTILINGUAL_EMBEDDING_002 = 'vertexai-text-multilingual-embedding-002',
}

export type EmbeddingsData = {
  text_index: number;
  embeddings: number[];
};

export type EmbeddingsResponse = {
  model: string;
  data: EmbeddingsData[];
  usage: Usage;
};
