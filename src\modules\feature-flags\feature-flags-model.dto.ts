import { Prisma } from '@prisma/client';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsNumber, IsArray } from 'class-validator';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PatchLabelsDto } from '../labels/dto/patch-labels.dto';

class Tag {
  @IsNumber()
  id: number;

  @IsString()
  name: string;

  @IsString()
  color: string;

  @IsString()
  desc: string;

  @IsString()
  labelType: string;

  @IsNumber()
  entityLabelsId: number;

  @IsNumber()
  labelId: number;
}
export class FeatureFlagDTO {
  @IsString()
  @IsNotEmpty()
  key: string;

  @IsString()
  description: string;

  @IsString()
  value?: string;

  @Transform(({value}) => {
    try {
      return JSON.parse(value);
    } catch (err) {
      throw new ApiException(ErrorCode.INVALID_META_DATA);
    }
  })
  @IsOptional()
  metaData?: JsonValue;

  @IsBoolean()
  isEnabled: boolean;

  @IsBoolean()
  isForClientSide: boolean;

  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;

  @IsArray()
  @IsOptional()
  entityLabels?: PatchLabelsDto[];

  @IsArray()
  @IsOptional()
  tags?: Tag[];
}

export class CreateFeatureFlagDTO extends FeatureFlagDTO {
  @IsNumber()
  @IsNotEmpty()
  categoryId: number;
}

export declare type JsonValue = Prisma.JsonObject;

export class FeatureFlagSeedDTO extends FeatureFlagDTO {
  @IsString()
  @IsNotEmpty()
  categoryKey: string;
}

export class GetOverrideFeatureFlagsOrDefaultDTO {
  key: string;
  description: string;
  value?: string;
  metaData?: JsonValue;
  isEnabled: boolean;
}
