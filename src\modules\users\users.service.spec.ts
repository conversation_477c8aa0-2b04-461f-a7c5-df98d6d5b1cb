import { UsersService } from './users.service';
import { FeatureFlag, Prisma, PrismaClient, Role, RoleType, User } from '@prisma/client';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { WikijsService } from '../wikijs/wikijs.service';
import { MailService } from 'src/providers/mail/mail.service';
import { CreateUserDTO } from './users.dto';
import { UserRequest } from '../auth/auth.interface';
import randomColor from 'randomcolor';
const moduleMocker = new ModuleMocker(global);
jest.mock('randomcolor');
const mockedRandomColor = randomColor as jest.MockedFunction<typeof randomColor>;
describe('UsersService', () => {
  let usersService: UsersService;
  let prismaService: DeepMockProxy<{
    // this is needed to resolve the issue with circular types definition
    // https://github.com/prisma/prisma/issues/10203
    [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
  }>;
  let featureFlagService: DeepMockProxy<FeatureFlagService>;
  let tokensService: DeepMockProxy<TokensService>;
  let email: DeepMockProxy<MailService>;
  let wikijsService: DeepMockProxy<WikijsService>;
  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      // this is needed to resolve the issue with circular types definition
      // https://github.com/prisma/prisma/issues/10203
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    (prismaService as any).option = {
      findFirst: jest.fn(),
    };
    featureFlagService = mockDeep<FeatureFlagService>();
    tokensService = mockDeep<TokensService>();
    email = mockDeep<MailService>();
    wikijsService = mockDeep<WikijsService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
        {
          provide: FeatureFlagService,
          useValue: featureFlagService,
        },
        {
          provide: TokensService,
          useValue: tokensService,
        },
        {
          provide: MailService,
          useValue: email,
        },
        {
          provide: WikijsService,
          useValue: wikijsService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    usersService = module.get(UsersService);
    // Mock the private method checkBusinessUnitOption to avoid test failures
    jest.spyOn(usersService as any, 'checkBusinessUnitOption').mockResolvedValue(true);
  });

  describe('Delete User', () => {
    it('should return deleted user', async () => {
      const existedUser = {
        id: 1,
        name: 'user',
      };
      prismaService.user.delete.mockResolvedValue(existedUser as User);
      const res = await usersService.deleteUser(1);
      expect(res).toEqual(existedUser);
      expect(prismaService.user.delete).toBeCalledWith({ where: { id: 1 } });
    });
  });

  describe('createUser', () => {
    it('create HKT user success case', async () => {
      // Mock dependencies
      const req = {
        name: 'John Doe',
        email: '<EMAIL>',
        remarks: '',
        staffId: '12345',
        loginType: 'HKT',
        roleId: 1,
        businessUnit: '',
        ccc: '',
        department: '',
        ssoId: null,
      };
      const userReq = {
        user: { id: 1 },
      };

      const defaultProfilePic =
        'https://ui-avatars.com/api/?name=JO&background=ffffff&color=000000';
      const id = 10123456;
      const user = {
        id,
        name: req.name,
        roleId: req.roleId,
        businessUnit: req.businessUnit,
        ccc: req.ccc,
        department: req.department,
        staffId: req.staffId.toUpperCase(),
        ssoId: null,
        active: true,
        loginProviderUniqueKey: req.staffId.toUpperCase(),
        emails: [
          {
            email: req.email.toLowerCase(),
            emailSafe: req.email.toLowerCase(),
            isVerified: true,
          },
        ],
        loginType: req.loginType,
      };

      // Mock service methods
      (prismaService.$transaction as jest.Mock).mockImplementation(async (callback) => {
        await callback(prismaService);
        return user;
      });
      featureFlagService.getOne.mockResolvedValue({
        isEnabled: true,
        metaData: { value: ['pccw.com'] } as Prisma.JsonValue,
      } as FeatureFlag);
      // Mock email.send
      email.send.mockResolvedValue(undefined);
      prismaService.role.findUnique.mockResolvedValue({
        id: req.roleId,
        roleType: RoleType.SYSTEM_DEFAULT,
      } as Role);
      tokensService.generateRandomString.mockResolvedValue('123456');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      mockedRandomColor.mockReturnValue('ffffff' as any);
      // Call the createUser method
      const result = await usersService.createUser(req as CreateUserDTO, userReq as UserRequest);

      // Assertions
      expect(result).toEqual(user);
      expect(prismaService.user.count).toHaveBeenCalledWith({
        where: {
          emails: {
            some: { email: req.email.toLowerCase(), emailSafe: req.email.toLowerCase() },
          },
        },
      });
      expect(prismaService.role.findUnique).toHaveBeenCalledWith({
        where: {
          id: req.roleId,
        },
      });
      expect(prismaService.$transaction).toHaveBeenCalled();
      expect(prismaService.user.create).toHaveBeenCalledWith({
        data: {
          remarks: req.remarks,
          staffId: req.staffId.toUpperCase(),
          name: req.name,
          roleId: req.roleId,
          businessUnit: req.businessUnit,
          ccc: req.ccc,
          department: req.department,
          id,
          ssoId: req.ssoId,
          active: true,
          emails: {
            create: {
              email: req.email.toLowerCase(),
              emailSafe: req.email.toLowerCase(),
              isVerified: true,
            },
          },
          loginType: req.loginType,
          loginProviderUniqueKey: req.staffId.toUpperCase(),
        },
        include: { emails: true },
      });
      expect(tokensService.generateRandomString).toHaveBeenCalledWith(6, 'numeric');
      expect(wikijsService.handleCreateBotBuilderUser).toHaveBeenCalledWith(
        user.id,
        user.name,
        user.loginType,
        user.emails[0].emailSafe,
        null,
        user.ccc,
        user.staffId.toUpperCase(),
      );
    });

    it('should convert staffId to uppercase when creating a user', async () => {
      const req = {
        name: 'Jane Doe',
        email: '<EMAIL>',
        staffId: 'test-id',
        loginType: 'HKT',
        roleId: 1,
        businessUnit: 'Test BU',
        ccc: 'Test CCC',
        department: 'Test Dept',
        remarks: '',
        ssoId: '',
      } as CreateUserDTO;
      const userReq = { user: { id: 1 } } as UserRequest;

      const user = {
        id: 123,
        staffId: 'TEST-ID',
        loginProviderUniqueKey: 'TEST-ID',
        emails: [{ email: '<EMAIL>', emailSafe: '<EMAIL>' }],
      };

      (prismaService.$transaction as jest.Mock).mockImplementation(async (callback) => {
        await callback(prismaService);
        return user;
      });
      prismaService.role.findUnique.mockResolvedValue({
        id: 1,
        roleType: RoleType.SYSTEM_DEFAULT,
      } as Role);
      tokensService.generateRandomString.mockResolvedValue('123456');
      featureFlagService.getOne.mockResolvedValue({
        isEnabled: true,
        metaData: { value: ['pccw.com'] },
      } as any);

      await usersService.createUser(req, userReq);

      expect(prismaService.user.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            staffId: 'TEST-ID',
            loginProviderUniqueKey: 'TEST-ID',
          }),
        }),
      );
    });
  
  });

  describe('updateUserByEmailAndLoginType', () => {
    it('should convert staffId to uppercase when updating a user', async () => {
      const userDto = {
        email: '<EMAIL>',
        loginType: 'HKT',
        staffId: 'test-id',
        businessUnit: 'Test BU',
        ccc: 'Test CCC',
        department: 'Test Dept',
        name: 'Test Name',
        remarks: '',
        roleId: 1,
        ssoId: '',
      } as CreateUserDTO;

      const existingUser = {
        id: 1,
        staffId: 'OLD-ID',
        emails: [{ email: '<EMAIL>' }],
      };

      prismaService.user.findFirst.mockResolvedValue(existingUser as any);
      prismaService.user.update.mockResolvedValue(existingUser as any);

      await usersService.updateUserByEmailAndLoginType(userDto);

      expect(prismaService.user.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            staffId: 'TEST-ID',
          }),
        }),
      );
    });
  });
});
