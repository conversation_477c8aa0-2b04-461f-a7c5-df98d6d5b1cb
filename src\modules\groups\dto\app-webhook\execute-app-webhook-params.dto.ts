import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString, Matches } from 'class-validator';
import { Transform } from 'class-transformer';

export class ExecuteAppWebhookParamsDto {
  @ApiProperty({
    description: 'ID of the app',
    example: 'n8n',
    type: String,
  })
  app: string;

  @ApiProperty({
    description: 'ID of the group',
    example: 123,
    type: Number,
  })
  @Transform(({value}) => parseInt(value, 10))
  @IsInt()
  @IsNotEmpty()
  groupId: number;

  @ApiProperty({
    description:
      'Custom path for the App webhook, can be multi-segment (e.g., "my-workflow/trigger1"). This path is used by the backend to determine the actual target webhook URL to call.',
    example: 'my-workflow/trigger1',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  // Regex to allow alphanumeric, hyphens, underscores, and forward slashes.
  // Does not allow path to start or end with a slash, or have consecutive slashes.
  @Matches(/^(?!.*--)(?!.*__)(?!.*\/\/)[a-zA-Z0-9]+(?:[_\-/][a-zA-Z0-9]+)*$/, {
    message:
      'customPath can only contain alphanumeric characters, hyphens, underscores, and single forward slashes. It cannot start/end with or have consecutive special characters/slashes.',
  })
  customPath: string;
}
