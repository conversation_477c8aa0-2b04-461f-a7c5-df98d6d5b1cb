import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { FeatureFlag, PrismaClient, User } from '@prisma/client';
import { ApiException } from '../../errors/errors.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { AuthService } from './auth.service';
const moduleMocker = new ModuleMocker(global);

describe('AuthService', () => {
  let authService: AuthService;
  let featureFlagService: DeepMockProxy<FeatureFlagService>;
  let prismaService: DeepMockProxy<{ [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'> }>;
  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    featureFlagService = mockDeep<FeatureFlagService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    authService = module.get(AuthService);
  });

  describe('loginUserWithHktToken', () => {
    it('should throw error if hkt token is invalid', async () => {
      await expect(
        authService.loginUserWithHktToken('127.0.0.1', 'test', '', 'test', 'test'),
      ).rejects.toThrow(ApiException);
    });

    it('should throw error if hkt login is not enabled', async () => {
      featureFlagService.getOne.mockResolvedValue({ isEnabled: false } as FeatureFlag);
      await expect(
        authService.loginUserWithHktToken('127.0.0.1', 'test', '123', 'test', 'test'),
      ).rejects.toThrow(ApiException);
    });

    it('should throw error if hkt login is enabled', async () => {
      featureFlagService.getOne.mockResolvedValue({ isEnabled: true } as FeatureFlag);
      await expect(
        authService.loginUserWithHktToken('127.0.0.1', 'test', '123', 'test', 'test'),
      ).rejects.toThrow(ApiException);
    });

    it('should throw error if hkt email is not found', async () => {
      featureFlagService.getOne.mockResolvedValue({ isEnabled: true } as FeatureFlag);
      prismaService.user.findUnique.mockResolvedValue(null);
      prismaService.user.findFirst.mockResolvedValue({ id: 7777 } as unknown as User);
      await expect(
        authService.loginUserWithHktToken('127.0.0.1', 'test', '123', 'test', ''),
      ).rejects.toThrow(ApiException);
      expect(prismaService.user.findFirst).toHaveBeenCalledTimes(0);
    });
  });
});
