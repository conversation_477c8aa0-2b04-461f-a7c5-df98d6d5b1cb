import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { AppWhitelistService } from './app-whitelist.service';
import { BatchAppWhitelistDto } from './dto/batch-app-whitelist.dto';
import { CreateAppWhitelistDto } from './dto/create-app-whitelist.dto';
import { QueryAppWhitelistDto } from './dto/query-app-whitelist.dto';
import { Scopes } from '../auth/scope.decorator';
import { Readable } from 'stream';
import papa from 'papaparse';

@ApiTags('app-whitelist')
@ApiBearerAuth('bearer-auth')
@UsePipes(new ValidationPipe({ whitelist: true }))
@Controller('app-whitelist')
export class AppWhitelistController {
  constructor(private readonly appWhitelistService: AppWhitelistService) {}

  @Post()
  @Scopes('system:app-whitelist-control')
  @AuditLog('add-user-app-whitelist')
  create(@Body() createAppWhitelistDto: CreateAppWhitelistDto) {
    return this.appWhitelistService.create(createAppWhitelistDto);
  }

  @Get()
  @Scopes('system:app-whitelist-control')
  findAll(@Query() query: QueryAppWhitelistDto) {
    return this.appWhitelistService.findAll(query);
  }

  @Get('template')
  @Scopes('system:app-whitelist-control')
  async downloadTemplate(@Res() res: Response) {
    const data = await this.appWhitelistService.downloadTemplate();
    const csvString = papa.unparse(data, { header: true });
    const stream = Readable.from(csvString);
    stream.pipe(res);
  }

  @Get(':id')
  @Scopes('system:app-whitelist-control')
  findOne(@Param('id') id: string) {
    return this.appWhitelistService.findOne(+id);
  }

  @Delete(':id')
  @AuditLog('remove-user-app-whitelist')
  @Scopes('system:app-whitelist-control')
  remove(@Param('id') id: string) {
    return this.appWhitelistService.remove(+id);
  }

  @Post('batch-process')
  @AuditLog('batch-process-user-app-whitelist')
  @Scopes('system:app-whitelist-control')
  batchProcess(@Body() batchAppWhitelistDto: BatchAppWhitelistDto) {
    return this.appWhitelistService.batchProcess(batchAppWhitelistDto);
  }
}
