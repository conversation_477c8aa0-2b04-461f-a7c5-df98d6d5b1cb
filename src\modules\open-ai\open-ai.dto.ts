import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  <PERSON>NotEmpty,
  IsN<PERSON>ber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  ChatCompletionAudioParam,
  ChatCompletionCreateParams,
  ChatCompletionFunctionCallOption,
  ChatCompletionMessageParam,
  ChatCompletionModality,
  ChatCompletionPredictionContent,
  ChatCompletionStreamOptions,
  ChatCompletionTool,
  ChatCompletionToolChoiceOption,
  ReasoningEffort,
  Metadata,
  ResponseFormatJSONObject,
  ResponseFormatJSONSchema,
  ResponseFormatText,
  EmbeddingCreateParams,
} from 'openai/resources';
import { ChatCompletionCreateParamsBase } from 'openai/resources/chat/completions';
import {
  ResponseInput,
  ResponsePrompt,
  Tool,
  ToolChoiceFunction,
  ToolChoiceOptions,
  ToolChoiceTypes,
} from 'openai/resources/responses/responses';

export class ChatCompletionCreateParamsDto implements ChatCompletionCreateParamsBase {
  @IsArray()
  @IsNotEmpty()
  messages: ChatCompletionMessageParam[];

  @IsString()
  @IsNotEmpty()
  model: string;

  @IsOptional()
  audio?: ChatCompletionAudioParam;

  @IsOptional()
  frequency_penalty?: number;

  @IsOptional()
  function_call?: 'none' | 'auto' | ChatCompletionFunctionCallOption;

  @IsOptional()
  functions?: ChatCompletionCreateParams.Function[];

  @IsOptional()
  logit_bias?: Record<string, number>;

  @IsOptional()
  logprobs?: boolean;

  @IsOptional()
  max_completion_tokens?: number;

  @IsOptional()
  max_tokens?: number;

  @IsOptional()
  metadata?: Metadata;

  @IsOptional()
  modalities?: ChatCompletionModality[];

  @IsOptional()
  n?: number;

  @IsOptional()
  parallel_tool_calls?: boolean;

  @IsOptional()
  prediction?: ChatCompletionPredictionContent;

  @IsOptional()
  presence_penalty?: number;

  @IsOptional()
  reasoning_effort?: ReasoningEffort;

  @IsOptional()
  response_format?: ResponseFormatText | ResponseFormatJSONObject | ResponseFormatJSONSchema;

  @IsOptional()
  seed?: number;

  @IsOptional()
  service_tier?: 'default' | 'auto';

  @IsOptional()
  stop?: string | string[];

  @IsOptional()
  store?: boolean;

  @IsOptional()
  stream_options?: ChatCompletionStreamOptions;

  @IsOptional()
  temperature?: number;

  @IsOptional()
  tool_choice?: ChatCompletionToolChoiceOption;

  @IsOptional()
  tools?: ChatCompletionTool[];

  @IsOptional()
  top_logprobs?: number;

  @IsOptional()
  top_p?: number;

  @IsOptional()
  user?: string;

  @IsOptional()
  stream?: boolean;
}

export class EmbeddingCreateParamsDto implements EmbeddingCreateParams {
  @IsNotEmpty()
  // Input can be string, array of strings, array of token arrays, or array of tokens.
  // For simplicity in DTO, we might initially define it as string | string[]
  // and handle more complex types in the service if necessary.
  // Or use a more generic 'any' with runtime validation if NestJS decorators struggle with union types.
  // For now, let's use 'any' and rely on OpenAI's SDK for validation, or add custom validation.
  input: string | Array<string> | Array<number> | Array<Array<number>>;

  @IsString()
  @IsNotEmpty()
  model: string;

  @IsOptional()
  @IsString()
  encoding_format?: 'float' | 'base64';

  @IsOptional()
  dimensions?: number;

  @IsOptional()
  @IsString()
  user?: string;
}

export class CreateResponseDto {
  @IsOptional()
  @IsBoolean()
  background?: boolean;

  // @IsOptional()
  // @IsArray()
  // include?: Array<
  //   | 'file_search_call.results'
  //   | 'message.input_image.image_url'
  //   | 'computer_call_output.output.image_url'
  //   | 'reasoning.encrypted_content'
  //   | 'code_interpreter_call.outputs'
  // >;

  @IsOptional()
  input?: string | ResponseInput;

  @IsOptional()
  @IsString()
  instructions?: string | null;

  @IsOptional()
  @IsNumber()
  max_output_tokens?: number | null;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, string>;

  @IsString()
  model: string;

  @IsOptional()
  @IsBoolean()
  parallel_tool_calls?: boolean | null;

  @IsOptional()
  @IsString()
  previous_response_id?: string | null;

  @IsOptional()
  @IsObject()
  prompt?: ResponsePrompt;

  @IsOptional()
  @IsObject()
  reasoning?: object | null;

  @IsOptional()
  @IsString()
  service_tier?: 'default' | 'auto' | 'flex' | 'scale';

  @IsOptional()
  @IsBoolean()
  store = false;

  @IsOptional()
  @IsBoolean()
  stream?: boolean | null;

  @IsOptional()
  @IsNumber()
  temperature?: number | null;

  @IsOptional()
  @IsObject()
  text?: object;

  @IsOptional()
  tool_choice?: ToolChoiceOptions | ToolChoiceTypes | ToolChoiceFunction;

  @IsOptional()
  @IsArray()
  tools?: Tool[];

  @IsOptional()
  @IsNumber()
  top_p?: number | null;

  @IsOptional()
  @IsString()
  truncation?: 'auto' | 'disabled' | null;

  @IsOptional()
  @IsString()
  user?: string;
}

