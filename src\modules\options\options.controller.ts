import { OptionType } from 'src/modules/options/options.inteface';
import { Public } from '../auth/public.decorator';
import { Controller, Get, Logger, Query, Req } from '@nestjs/common';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OptionsService } from './options.service';
import { AppType, Prisma } from '@prisma/client';
import { ApiTags } from '@nestjs/swagger';
import { Scopes } from '../auth/scope.decorator';
import { UserRequest } from '../auth/auth.interface';

@Controller('options')
@ApiTags('Options')
export class OptionsController {
  private logger = new Logger(OptionsController.name);

  constructor(private readonly optionsService: OptionsService) {}

  // TODO: see if need to split APIs into BU, CCC, Department instead of Group and User
  //  https://theclub.atlassian.net/browse/AI-1238

  /**  Get Group Options */
  @Get('groups')
  @Public()
  async getGroupOptions(
    @Query('optionType') optionType: OptionType,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('contains') contains?: string,
    @Query('mode') mode?: Prisma.QueryMode,
  ) {
    const options = await this.optionsService.getGroupOptions(
      optionType,
      contains,
      mode,
      skip,
      take,
    );
    return { options };
  }

  /**  Get User Options */
  @Get('users')
  @Public()
  async getUserOptions(
    @Query('optionType') optionType: OptionType,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('contains') contains?: string,
    @Query('mode') mode?: Prisma.QueryMode,
  ) {
    const options = await this.optionsService.getUserOptions(
      optionType,
      contains,
      mode,
      skip,
      take,
    );
    return { options };
  }

  @Get('apps')
  @Scopes('user-*:read-app-options')
  async getCreateAppOption(@Query('optionType') optionType: AppType, @Req() req: UserRequest) {
    return this.optionsService.getCreateAppOption(optionType, req);
  }

  @Get('app-whitelist')
  @Scopes('system:app-whitelist-control')
  async getNeedWhitelistsAppOtiopns() {
    return this.optionsService.getNeedWhitelistAppsOptions();
  }
}
