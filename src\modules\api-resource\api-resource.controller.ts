import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Req,
  Res,
  UploadedFiles,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';

import { ConfigService } from '@nestjs/config';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ApiResource, ApiResourceType } from '@prisma/client';
import { Response } from 'express';
import { extname } from 'path';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ApiResourceService } from './api-resource.service';
import { CreateApiResourceDto } from './dto/create-api-resource.dto';
import { UpdateApiResourceDto } from './dto/update-api-resource.dto';
import { Readable } from 'stream';

@Controller('groups/:groupId/api-resources')
@ApiBearerAuth('bearer-auth')
@ApiTags('Connect API')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class ApiResourceController {
  private readonly logger = new Logger(ApiResourceController.name);
  private readonly allowedFileExt = ['.yaml', '.graphql'];
  constructor(
    private readonly apiResourceService: ApiResourceService,
    private readonly configService: ConfigService,
  ) {}

  @Post()
  @Scopes('group-{groupId}:write-api-resource')
  create(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createApiResourceDto: CreateApiResourceDto,
  ): Promise<ApiResource> {
    return this.apiResourceService.create(createApiResourceDto, groupId);
  }

  @Get()
  @Scopes('group-{groupId}:read-api-resource')
  async findOne(@Param('groupId', ParseIntPipe) groupId: number): Promise<ApiResource> {
    const apiResource = await this.apiResourceService.findByGroupId(groupId);
    if (apiResource) {
      return apiResource;
    }
    throw new ApiException(ErrorCode.API_RESOURCE_NOT_FOUND);
  }

  @Get(':id/upload-file')
  @Scopes('group-{groupId}:read-api-resource')
  @AuditLog('download-api-resource-doc')
  async downloadApiResourceDoc(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Res() response: Response,
  ): Promise<void> {
    
    const fileResponse = await this.apiResourceService.fetchApiResourceDocFileStream(groupId, id);
    (fileResponse.Body as Readable)
      .on('error', function () {
        response.status(404).json({ statusCode: 404, message: 'Not Found' });
      })
      .pipe(response);
  }

  @Patch(':id')
  @Scopes('group-{groupId}:write-api-resource')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() updateApiResourceDto: UpdateApiResourceDto,
  ): Promise<ApiResource> {
    return this.apiResourceService.update(id, groupId, updateApiResourceDto);
  }

  @Delete(':id')
  @Scopes('group-{groupId}:delete-api-resource')
  remove(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ): Promise<ApiResource> {
    return this.apiResourceService.remove(id, groupId);
  }

  @Delete(':id/upload-file')
  @Scopes('group-{groupId}:delete-api-resource-file')
  async removeFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResource> {
    return this.apiResourceService.deleteApiResourceDoc(groupId, id);
  }

  @Post(':id/upload-file')
  @Scopes('group-{groupId}:write-api-resource-file')
  @UseInterceptors(FilesInterceptor('file'))
  async uploadFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) apiResourceId: number,
    @Req() request: UserRequest,
    @UploadedFiles() files: Array<Express.Multer.File>,
  ): Promise<ApiResource> {
    // file format checking
    if (!(files && files.length)) {
      throw new ApiException(ErrorCode.API_RESOURCE_UPLOAD_FIlE_NOT_FOUND);
    }
    if (files[0].size > this.configService.get<number>('s3.staticFilesMaxSize')) {
      throw new ApiException(ErrorCode.FILE_TOO_LARGE);
    }
    const { apiResource } = await this.apiResourceService.verifyGroupAndResource(
      groupId,
      apiResourceId,
    );
    const file = files[0];
    const fileExtname = extname(file.originalname);
    if (
      (fileExtname == '.yaml' && apiResource.apiResourceType != ApiResourceType.RESTAPI) ||
      (fileExtname == '.graphql' && apiResource.apiResourceType != ApiResourceType.GRAPHQL) ||
      this.allowedFileExt.indexOf(fileExtname) === -1
    ) {
      throw new ApiException(ErrorCode.INVALID_UPLOAD_FILE_FORMAT);
    }
    const isUpdate = await this.apiResourceService.uploadApiDocumentFile(
      file,
      apiResource,
      groupId,
      request.user.id,
    );
    if (!isUpdate) {
      this.logger.error('upload failed by No response from backend');
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
    return isUpdate;
  }
}
