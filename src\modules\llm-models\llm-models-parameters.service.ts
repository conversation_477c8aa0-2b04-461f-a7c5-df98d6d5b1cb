import { Injectable, Logger } from '@nestjs/common';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { Prisma, Group, LLMModel } from '@prisma/client';
import { GROUP_MEMBERSHIP_SCOPE_KEY } from 'src/providers/redis/redis.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { LlmModelParamsDto } from './dto/llm-model-params.dto';

@Injectable()
export class LLMModelsParametersService implements PromotableService {
  private logger = new Logger(LLMModelsParametersService.name);

  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const lLMModel = await this.prisma.lLMModel.findFirst({
      where: {
        groupId,
      },
    });
    if (!lLMModel) throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
    return {
      llmParams: lLMModel?.parameters?.['llmParams'],
    };
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }

  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: any,
    operatorId: number,
  ): Promise<string> {
    const llmModel = await tx.lLMModel.findFirst({
      select: { id: true },
      where: { groupId: targetGroup.id },
    });
    if (!llmModel) throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
    await this.promoteUpdate(tx, targetGroup, llmModel.id.toString(), entityData, operatorId);
    return llmModel.id.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: LLMModel,
    operatorId: number,
  ): Promise<void> {
    const dstLlmModel = await tx.lLMModel.findUniqueOrThrow({
      where: { groupId: targetGroup.id },
    });

    await tx.lLMModel.update({
      data: {
        lastPromotedAt: new Date(),
      },
      where: {
        id: dstLlmModel.id,
      },
    });

    // plug into original update function
    await this.update(dstLlmModel.modelId, operatorId, entityData, tx);
  }

  async update(modelId: string, userId: number, data: any, tx?: Prisma.TransactionClient) {
    const prismaTx = tx ? tx : this.prisma;
    try {
      const model = await this.prisma.lLMModel.findFirst({
        where: {
          modelId,
        },
        include: { group: true, lastModifiedBy: true },
      });

      if (!model) {
        throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      }
      const parameters = model.parameters || {};
      parameters['llmParams'] = data.llmParams;
      const updated = await prismaTx.lLMModel.update({
        where: {
          modelId,
        },
        include: {
          group: true,
        },
        data: {
          parameters,
          lastModifiedBy: { connect: { id: userId } },
        },
      });
      if (!updated) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }
      await this.redis.batchClearCache(
        GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', '*').replace(
          '{GROUP_ID}',
          model.groupId.toString(),
        ),
      );
      const resultData: LlmModelParamsDto = {
        id: updated.id,
        llmParams: updated?.parameters?.['llmParams'],
      };
      return resultData;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    throw new ApiException(ErrorCode.DATA_PROMOTION_DELETE_NOT_SUPPORTED);
  }

  async checkPromotedEntityValid(targetEntityId: string): Promise<object> {
    const llmmodel = await this.prisma.lLMModel.findUnique({
      where: { id: parseInt(targetEntityId) },
    });
    return {
      llmParams: llmmodel?.parameters?.['llmParams'],
    };
  }
}
