-- AlterTable
ALTER TABLE "App" ADD COLUMN     "whitelistControl" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "AppWhitelist" (
    "id" SERIAL NOT NULL,
    "appId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AppWhitelist_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AppWhitelist_appId_userId_key" ON "AppWhitelist"("appId", "userId");
