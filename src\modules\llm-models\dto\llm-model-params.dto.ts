import { Type } from 'class-transformer';
import { IsObject, IsOptional, ValidateNested } from 'class-validator';
import { LlmParamsDto } from 'src/modules/chat-sessions/dto/llm-params.dto';
import { StableDiffusionParamsDto } from 'src/modules/chat-sessions/dto/stable-diffusion-params.dto';

export class LlmModelParamsDto {
  @ValidateNested()
  @Type(() => LlmParamsDto)
  @IsObject()
  @IsOptional()
  llmParams?: LlmParamsDto;

  @ValidateNested()
  @Type(() => StableDiffusionParamsDto)
  @IsObject()
  @IsOptional()
  stableDiffusionParams?: StableDiffusionParamsDto;

  @IsOptional()
  id?: number;
}
