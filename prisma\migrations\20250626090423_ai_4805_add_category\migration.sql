-- CreateEnum
CREATE TYPE "CategoryType" AS ENUM ('FEATURE_FLAG');

-- AlterEnum
ALTER TYPE "LabelEntityType" ADD VALUE 'FEATURE_FLAG';

-- CreateTable
CREATE TABLE "Category" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "order" INTEGER,
    "parentId" INTEGER NOT NULL DEFAULT 0,
    "description" TEXT,
    "type" "CategoryType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Category_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EntityCategory" (
    "categoryId" INTEGER NOT NULL,
    "entityId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EntityCategory_pkey" PRIMARY KEY ("categoryId","entityId")
);

-- CreateIndex
CREATE INDEX "Category_parentId_idx" ON "Category"("parentId");

-- CreateIndex
CREATE INDEX "Category_type_idx" ON "Category"("type");

-- CreateIndex
CREATE UNIQUE INDEX "Category_name_type_parentId_key" ON "Category"("name", "type", "parentId");

-- CreateIndex
CREATE INDEX "EntityCategory_categoryId_idx" ON "EntityCategory"("categoryId");

-- CreateIndex
CREATE INDEX "EntityCategory_entityId_idx" ON "EntityCategory"("entityId");
