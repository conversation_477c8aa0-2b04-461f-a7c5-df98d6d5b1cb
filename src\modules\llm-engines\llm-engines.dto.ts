import { Prisma } from '@prisma/client';
import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsIn,
  IsJSON,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  MinLength,
  ValidateNested,
  IsDefined,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PatchLabelsDto } from '../labels/dto/patch-labels.dto';

export class CreateLlmEngineDto {
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  name: string;

  @IsString()
  @MinLength(3)
  @MaxLength(255)
  slug: string;

  @IsString()
  @MinLength(3)
  @MaxLength(255)
  reportSlug: string;

  @IsString()
  @IsIn(['TEST', 'PROD'])
  @IsOptional()
  env?: 'TEST' | 'PROD';

  @IsString()
  @IsIn(['OPENAI', 'AZURE', 'AWS', 'HUGGING_FACE', 'VERTEX_AI', 'SENSENOVA', 'ALIBABA'])
  @IsOptional()
  platform?: 'OPENAI' | 'AZURE' | 'AWS' | 'HUGGING_FACE' | 'VERTEX_AI' | 'SENSENOVA' | 'ALIBABA';

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsJSON()
  @IsOptional()
  config?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue;
}

export class UpdateLlmEngineDto {
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  @IsOptional()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  isClientDeprecated?: boolean;

  @IsBoolean()
  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string' && (value === 'false' || value === 'true')) {
      return value === 'true';
    }
    return value;
  })
  isRecommended?: boolean;

  @IsOptional()
  @IsNumber()
  @Transform((value) => {
    if (typeof value === 'string') {
      return Number(value);
    }
    return value;
  })
  sequence?: number;

  @IsString()
  @IsOptional()
  @Transform(({value}) => {
    if (value === 'undefined') {
      return undefined;
    }
    return value;
  })
  config?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue | undefined;

  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (error) {
        throw new Error('Invalid JSON string for entityLabels');
      }
    }
    return value;
  })
  entityLabels?: PatchLabelsDto[];

  @IsString()
  @IsOptional()
  iconPictureUrl?: string;

  @IsOptional()
  @IsString()
  learningMaterial?: string;
}

export class BatchUpdateDto {
  @IsArray()
  // @ValidateNested({ each: true }) // Removed to allow partial success/failure
  @Type(() => BatchUpdateLlmEngineDto)
  llmEngines: BatchUpdateLlmEngineDto[];
}

export class LabelInputDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  // Consider adding @IsIn for specific color values if needed
  color: string;
}

export class CategoryInputDto {
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class BatchUpdateLlmEngineDto {
  @IsString()
  @IsNotEmpty()
  slug: string; // Used to identify the LlmEngine

  @IsString()
  @MinLength(3)
  @MaxLength(255)
  name: string;

  @IsNumber()
  sequence: number;

  @IsBoolean()
  isActive: boolean;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  isRecommended: boolean;

  @IsBoolean()
  @IsOptional()
  isClientDeprecated?: boolean;

  @IsString()
  @IsOptional()
  learningMaterial?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LabelInputDto)
  @IsOptional()
  labels?: LabelInputDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CategoryInputDto)
  @IsOptional()
  categories?: CategoryInputDto[];
}
