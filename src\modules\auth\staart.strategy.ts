import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { timingSafeEqual } from 'crypto';
import { Request } from 'express';
import ipRange<PERSON>heck from 'ip-range-check';
import { Strategy } from 'passport-strategy';
import { getClientIp } from 'request-ip';
import { CronJobApiKeyScopes } from 'src/constants/cron-job';
import { RedisService } from 'src/providers/redis/redis.service';
import { SecretHashService } from '../../providers/secret-hash/secret-hash.service';
import { LOGIN_ACCESS_TOKEN } from '../../providers/tokens/tokens.constants';
import { TokensService } from '../../providers/tokens/tokens.service';
import { ApiKeysService } from '../api-keys/api-keys.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { UsersService } from '../users/users.service';
import { WikijsService } from '../wikijs/wikijs.service';
import { AUTH_PROVIDER_GENKB } from './auth.constants';
import { AccessTokenClaims, AccessTokenParsed, WikijsAccessTokenPayload } from './auth.interface';
import { AuthService } from './auth.service';
import { NotificationApiKeyScopes } from '../../constants/notification';
import { JobQueueApiKeyScopes } from 'src/constants/bull-mq-queue';
import { KeycloakService } from 'src/providers/keycloak/keycloak.service';
class StaartStrategyName extends Strategy {
  name = 'staart';
}
interface CustomAuth {
  headerKey: string;
  configKey: string;
  scopes: {
    generateScopeFunName?: string;
    scopes?: string[];
  };
}

@Injectable()
export class StaartStrategy extends PassportStrategy(StaartStrategyName) {
  private logger = new Logger(StaartStrategy.name);
  private customAuth: CustomAuth[] = [
    {
      headerKey: 'resource-generation-callback-key',
      configKey: 'aiResource.callbackKey',
      scopes: {
        scopes: ['resource-generation-callback'],
      },
    },
    {
      headerKey: 'cron-job-api-key',
      configKey: 'cronJob.apiKey',
      scopes: {
        scopes: Object.values(CronJobApiKeyScopes),
      },
    },
    {
      headerKey: 'postman-monitor-api-key',
      configKey: 'postman.apiKey',
      scopes: {
        generateScopeFunName: 'generatePostmanMonitorScope',
      },
    },
    {
      headerKey: 'bot-builder-internal-chat-key',
      configKey: 'internalChat.authKey',
      scopes: {
        scopes: [
          'get-feat-key',
          'group-*:read-playground-internal',
          'upload-chat-files-internal',
          'group-*:token-limit-checking',
          'update-scheduler-job-status-internal',
          'group-*:read-member-ship-internal',
        ],
      },
    },
    {
      headerKey: 'bot-builder-chat-with-data',
      configKey: 'chatWithData.authKey',
      scopes: {
        scopes: ['group-*:read-playground-internal', 'upload-chat-files-internal'],
      },
    },
    {
      headerKey: 'bot-builder-auto-test-key',
      configKey: 'autoTest.authKey',
      scopes: {
        scopes: [
          'group-*:read-flow-playground-internal',
          'group-*:read-playground-internal',
          'group-*:token-limit-checking',
          'group-*:read-member-ship-internal',
          'group-*:read-group-security-setting',
          'get-feat-key',
        ],
      },
    },
    {
      headerKey: 'notification-api-key',
      configKey: 'notification.authKey',
      scopes: {
        scopes: Object.values(NotificationApiKeyScopes),
      },
    },
    {
      headerKey: 'job-queue-api-key',
      configKey: 'jobQueue.authKey',
      scopes: {
        scopes: Object.values(JobQueueApiKeyScopes),
      },
    },
    {
      headerKey: 'document-ai-key',
      configKey: 'documentAi.authKey',
      scopes: {
        scopes: ['group-*:read-playground-internal', 'upload-chat-files-internal'],
      },
    },
  ];
  constructor(
    private authService: AuthService,
    private redis: RedisService,
    private apiKeyService: ApiKeysService,
    private tokensService: TokensService,
    private secretHashService: SecretHashService,
    private configService: ConfigService,
    private usersService: UsersService,
    private wikijsService: WikijsService,
    private featureFlagService: FeatureFlagService,
    private keycloakService: KeycloakService,
  ) {
    super();
  }

  private safeSuccess(result: AccessTokenParsed) {
    return this.success(result);
  }

  async authenticate(request: Request) {
    /** API key authorization */
    let authorizationKey = '';
    let secureApiKey;
    if (typeof request.query['api_key'] === 'string')
      authorizationKey = request.query['api_key'].replace('Bearer ', '');
    else if (typeof request.headers['x-api-key'] === 'string')
      authorizationKey = request.headers['x-api-key'].replace('Bearer ', '');
    else if (typeof request.headers['api-key'] === 'string')
      authorizationKey = request.headers['api-key'].replace('Bearer ', '');
    else if (request.headers.authorization)
      authorizationKey = request.headers.authorization.replace('Bearer ', '');
    else if (
      typeof request.headers['s-api-key'] === 'string' &&
      typeof request.headers['date'] === 'string'
    )
      // handle function call from other service, e.g: gpt-service
      secureApiKey = request.headers['s-api-key'];

    if (secureApiKey != null) {
      try {
        const messages = [request.method, request.path, request.headers.date];
        const message = messages.join('\n');
        if (this.secretHashService.validateSign(message, secureApiKey)) {
          return this.safeSuccess({
            type: 'api-key',
            id: 1,
            scopes: [
              `update-callback`,
              `read-model-price`,
              `group-*:embeddings`,
              `file-conversion-callback`,
            ],
          });
        }
      } catch (error) {
        this.logger.error(error);
        return this.fail('Referrer restrictions not met', 401);
      }
    }

    for (const customAuthInfo of this.customAuth) {
      if (typeof request.headers[customAuthInfo.headerKey] === 'string') {
        const configKeyBuffer = Buffer.from(
          this.configService.get<string>(customAuthInfo.configKey),
        );
        const headerKeyBuffer = Buffer.from(request.headers[customAuthInfo.headerKey] as string);
        try {
          if (
            configKeyBuffer.byteLength != headerKeyBuffer.byteLength &&
            !timingSafeEqual(configKeyBuffer, headerKeyBuffer)
          ) {
            return this.fail('Invalid token', 401);
          }
        } catch (err: any) {
          this.logger.error(err);
          return this.fail('Invalid token', 401);
        }
        if (customAuthInfo.scopes.scopes) {
          return this.safeSuccess({
            type: 'api-key',
            id: 1,
            scopes: customAuthInfo.scopes.scopes,
          });
        }
        return this[customAuthInfo.scopes.generateScopeFunName](request);
      }
    }

    // handle x-api-key which is called by external parties
    if (typeof authorizationKey === 'string') {
      if (authorizationKey.startsWith('Bearer '))
        authorizationKey = authorizationKey.replace('Bearer ', '');
      if (
        // If authentication is *not* a JWT
        !authorizationKey.match(/^[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*$/)
      )
        try {
          const apiKeyDetails = await this.apiKeyService.getApiKeyFromKey(authorizationKey);

          const enableApiKeyIpFilteringFeatureFalg = await this.featureFlagService.getOne(
            FeatureFlagKey.ENABLE_API_KEY_IP_FILTERING,
          );

          if (
            enableApiKeyIpFilteringFeatureFalg == null ||
            enableApiKeyIpFilteringFeatureFalg.isEnabled
          ) {
            if (
              !Array.isArray(apiKeyDetails.ipRestrictions) ||
              !apiKeyDetails.ipRestrictions.length ||
              !ipRangeCheck(getClientIp(request), apiKeyDetails.ipRestrictions as string[])
            ) {
              return this.fail('IP address restrictions not met', 401);
            }
          }

          return this.safeSuccess({
            type: 'api-key',
            id: apiKeyDetails.id,
            scopes: apiKeyDetails.scopes as string[],
            groupId: apiKeyDetails.groupId,
          });
        } catch (error) {
          this.logger.error(error, 'api key error');
          return this.fail('Invalid token', 401);
        }
    }

    /** Bearer JWT authorization */
    let bearerToken = request.query['token'] ?? request.headers.authorization;
    if (typeof bearerToken !== 'string') return this.fail('No token found', 401);
    if (bearerToken.startsWith('Bearer ')) bearerToken = bearerToken.replace('Bearer ', '');

    // handle request from genkb
    if (typeof request.headers['provider'] === 'string') {
      if (request.headers['provider'] == AUTH_PROVIDER_GENKB) {
        try {
          const wikijsPlayload =
            this.tokensService.verifyWikijs<WikijsAccessTokenPayload>(bearerToken);

          if (typeof request.params['groupId'] === 'string') {
            const groupId = Number(request.params['groupId']);
            if (groupId) {
              await this.wikijsService.isGroupInWikijs(groupId, wikijsPlayload.groups);
              const user = await this.usersService.findUserIdByEmail(wikijsPlayload.email);
              // only playground is allowed
              return this.safeSuccess({
                type: 'user',
                id: user.userId,
                scopes: [
                  `group-${request.params['groupId']}:read-playground`,
                  `group-${request.params['groupId']}:read-chat-session`,
                  `group-${request.params['groupId']}:read-llm-model-basic`,
                ],
              });
            }
          }
        } catch (error) {
          this.logger.error(error, 'api key error');
          return this.fail('Invalid token', 401);
        }
      }
    }
    try {
      const decodedToken = this.tokensService.decode<
        AccessTokenClaims & { iss?: string; sub?: string }
      >(bearerToken);

      if (!decodedToken) {
        this.logger.warn('Failed to decode bearer token.');
        return this.fail('Invalid token', 401);
      }
      const keycloakIssuer = this.configService.get<string>('appCenterKeycloak.issuer');
      if (decodedToken.iss === keycloakIssuer) {
        this.logger.log('Attempting Keycloak token validation flow.');
        const {
          isActive,
          email: keycloakEmail,
          userId: keycloakSubjectId,
          username: keycloakUsername,
        } = await this.keycloakService.validateAndGetUserEmailFromToken(bearerToken);

        if (isActive && keycloakEmail && keycloakSubjectId) {
          const groupId = request?.params?.['groupId']
            ? Number(request?.params?.['groupId'])
            : null;
          const { userId } = await this.usersService.findUserIdByEmail(keycloakEmail);

          const scopes = await this.authService.getScopes(userId, groupId);
          if (!scopes) {
            this.logger.error(
              `No scopes found for local user ${keycloakUsername} (Keycloak user ${keycloakEmail}) in group ${groupId}`,
            );
            return this.fail('Invalid token or insufficient permissions', 401);
          }
          return this.safeSuccess({
            type: 'user',
            scopes,
            id: userId,
            groupId: groupId,
          });
        } else {
          this.logger.warn(
            `Keycloak token validation failed or user details missing. Active: ${isActive}, Email: ${keycloakEmail}, Subject: ${keycloakSubjectId}`,
          );
          return this.fail('Invalid Keycloak token', 401);
        }
      } else {
        this.logger.log('Attempting Bot-Builder token validation flow.');
        const payload = this.tokensService.verify(
          LOGIN_ACCESS_TOKEN,
          bearerToken,
        ) as AccessTokenClaims;
        const scopes = await this.authService.getScopes(payload.id, payload.groupId);
        if (!scopes) {
          this.logger.error(`No scopes found on user - ${payload.id} for group ${payload.groupId}`);
          return this.fail('Invalid token', 401);
        }
        return this.safeSuccess({ type: 'user', scopes, ...payload });
      }
    } catch (error) {
      this.logger.error(error, 'Bearer token verification failed: ');
      return this.fail('Invalid token', 401);
    }
  }

  /**
   * @description use in the customAuth var, generate postman Monitor Scope
   * @param request
   * @returns
   */
  private generatePostmanMonitorScope(request: Request) {
    this.logger.log('AI-1510: Request exists postman-monitor-api-key');
    const groupId = String(this.configService.get<number>('postman.groupId'));
    const groupIdBuffer = Buffer.from(groupId);
    const requestGroupIdBuffer = Buffer.from(request.params['groupId']);

    if (
      groupIdBuffer.byteLength === requestGroupIdBuffer.byteLength &&
      timingSafeEqual(groupIdBuffer, requestGroupIdBuffer)
    ) {
      this.logger.log(`AI-1510: group ID matched ${groupId}`);
      const scopePrefix = `group-${groupId}`;
      const scopes = this.configService
        .get<string[]>('postman.scopes')
        .map((scope) => `${scopePrefix}:${scope}`);

      this.logger.log(`AI-1510: Adding scopes: ${scopes}`);

      return this.safeSuccess({
        type: 'api-key',
        id: null,
        scopes: scopes,
      });
    }
    this.logger.log(
      `AI-1510: group ID not matching: requestGroupId=${request.params['groupId']}, groupId=${groupId}`,
    );
    return this.fail('Invalid token', 401);
  }

  async validate(request: any): Promise<any> {
    return this.authenticate(request);
  }
}
