import { FeatureType } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';

export class SchedulerJobWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  name?: PrismaStringFilterDto;

  @IsOptional()
  @IsEnum(FeatureType)
  featureType?: FeatureType;

  @IsOptional()
  featureId?: number;
}

export class CreateSchedulerJobDto {
  @IsEnum(FeatureType)
  featureType: FeatureType;

  @IsNumber()
  featureId: number;

  @IsString()
  schedulerJobRRule: string;

  @IsString()
  name: string;
}

export class UpdateSchedulerJobDto {
  @IsString()
  name: string;
  @IsString()
  schedulerJobRRule: string;
}
