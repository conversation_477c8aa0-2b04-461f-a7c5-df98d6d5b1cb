import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  MessageAttributeValue,
  SendMessageBatchCommandInput,
  SendMessageCommandInput,
} from '@aws-sdk/client-sqs';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from 'src/config/configuration.interface';
import { v4 } from 'uuid';
import { QueueServiceInterface } from '../queue/queue.service.interface';
import { SchedulerJobDto } from './bullmq.dto';

@Injectable()
export class BullMqService implements QueueServiceInterface {
  private logger = new Logger(BullMqService.name);
  private client: AxiosInstance;
  constructor(private configService: ConfigService) {
    const config = this.configService.get<Configuration['jobQueue']>('jobQueue');

    this.client = axios.create({
      baseURL: config.backendPrefix,
    });

    this.client.interceptors.request.use((req) => {
      this.logger.debug(`send request :${JSON.stringify(req)}`);
      return req;
    });
  }

  public async sendMessageBatch(params: SendMessageBatchCommandInput): Promise<void> {
    try {
      const bullMqBatchBody = params.Entries.map((item) =>
        this.convertSendMessageRequestToBullMQ(item as unknown as SendMessageCommandInput),
      );
      await this.client.post(`/producer/${params.QueueUrl}/batchAddJobs`, {
        data: bullMqBatchBody,
      });
    } catch (err) {
      throw err;
    }
  }

  public async sendMessage(params: SendMessageCommandInput): Promise<void> {
    try {
      const bullMqRequestBody = this.convertSendMessageRequestToBullMQ(params);
      const data = await this.client.post(`/producer/${params.QueueUrl}/addJob`, bullMqRequestBody);
    } catch (err) {
      throw err;
    }
  }

  private convertSendMessageRequestToBullMQ(params: SendMessageCommandInput) {
    const delay = params.DelaySeconds ? { delay: params.DelaySeconds } : {};
    const deduplication = params.MessageDeduplicationId
      ? { deduplication: { id: params.MessageDeduplicationId } }
      : {};
    const bullMqRequestBody = {
      name: params.MessageGroupId ?? v4(),
      data: {
        body: params.MessageBody,
        messageAttributes: this.transformAttributes(params?.MessageAttributes),
      },
      opts: { ...deduplication, ...delay },
    };
    return bullMqRequestBody;
  }

  public transformAttributes(messageAttributes: Record<string, MessageAttributeValue>): {
    [key: string]: { [key: string]: any };
  } {
    const newMessageAttributes: { [key: string]: { [key: string]: any } } = {};

    for (const key in messageAttributes) {
      const attr = messageAttributes[key];
      newMessageAttributes[key] = {};

      for (const k1 in attr) {
        const v1 = attr[k1];
        const newKey = k1.charAt(0).toLowerCase() + k1.slice(1);
        newMessageAttributes[key][newKey] = v1;
      }
    }
    return newMessageAttributes;
  }

  public async createSchedulerJob(schedulerJobDto: SchedulerJobDto) {
    const response = await this.client.post(`/scheduler-job`, { ...schedulerJobDto });
    return response.data;
  }

  public async updateSchedulerJob(schedulerJobDto: SchedulerJobDto) {
    const response = await this.client.patch(`/scheduler-job/${schedulerJobDto.schedulerJobId}`, {
      ...schedulerJobDto,
    });
    return response.data;
  }

  public async removeSchedulerJob(queueName: string, schedulerJobId: string) {
    const response = await this.client.delete(`/scheduler-job/${queueName}/${schedulerJobId}`);
    return response.data;
  }
}
