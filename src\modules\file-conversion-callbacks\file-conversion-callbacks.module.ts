import { Module } from '@nestjs/common';
import { FileConversionCallbackController } from './file-conversion-callbacks.controller';
import { LLMModelsModule } from '../llm-models/llm-models.module';
import { FileConversionModule as FileConversionProviderModule } from 'src/providers/file-conversion/file-conversion.module';

@Module({
  imports: [LLMModelsModule, FileConversionProviderModule],
  controllers: [FileConversionCallbackController],
})
export class FileConversionCallbackModule {}
