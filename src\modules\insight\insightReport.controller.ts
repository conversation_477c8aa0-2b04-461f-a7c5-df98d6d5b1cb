import { <PERSON>, Controller, Get, Param, ParseIntPipe, <PERSON>, Req, Res } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { InsightReportService } from './insightReport.service';
import { Scopes } from '../auth/scope.decorator';
import { Request, Response } from 'express';
import { UpdateInsightReportDto } from './insight.dto';
@Controller('insights')
export class InsightReportController {
  logger = new Logger(InsightReportController.name);
  constructor(private insightReportService: InsightReportService) {}

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/reports')
  async getInsightReport(@Param('groupId', ParseIntPipe) groupId: number, @Req() req: Request) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightReportService.getInsightReports(groupId, queryStr);
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/reports/case-options')
  async getReportCaseOptions(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.insightReportService.getInsightReportCaseOptions(groupId);
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id')
  async getInsightReportById(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightReportService.getInsightReportById(id, groupId);
  }

  @Scopes('group-{groupId}:write-insight-report')
  @Patch('/:groupId/report/:id')
  async updateInsightReportById(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: UpdateInsightReportDto,
  ) {
    return await this.insightReportService.updateInsightReportById(id, groupId, data);
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/history-version')
  async getReportHistoryVersion(
    @Param('id', ParseIntPipe) insightReportId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: Request,
  ) {
    const queryStr = req.url.split('?')?.[1] ?? '';

    return await this.insightReportService.getInsightReportHistoryVersion(
      groupId,
      insightReportId,
      queryStr,
    );
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/tokens/article-summarization')
  async getInsightReportArticleSummarizationTokenUsage(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightReportService.getInsightReportArticleSummarizationTokenUsage(
      id,
      groupId,
    );
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/articles')
  async getInsightReportArticles(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: Request,
  ) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightReportService.getInsightReportDetailListById(groupId, id, queryStr);
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/status')
  async getArticleSummarizationStatus(
    @Param('id', ParseIntPipe) reportId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightReportService.getInsightReportStatusById(reportId, groupId);
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/tokens/overall-summarization')
  async getInsightReportOverallSummarizationTokenUsage(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightReportService.getInsightReportOverallSummarizationTokenUsage(
      id,
      groupId,
    );
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/article-report-file')
  async getInsightArticleReportFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightReportId: number,
    @Res() res: Response,
  ) {
    const file = await this.insightReportService.getInsightArticleReportFile(
      insightReportId,
      groupId,
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="insightReport-articles-${insightReportId}.xlsx"`,
    );
    file.pipe(res);
  }

  @Scopes('group-{groupId}:read-insight-report')
  @Get('/:groupId/report/:id/overall-report-file')
  async getInsightOverallReportFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightReportId: number,
    @Res() res: Response,
  ) {
    const file = await this.insightReportService.getInsightOverallReportFile(
      groupId,
      insightReportId,
    );
    res.setHeader('Content-Type', 'application/msword');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="insightReport-overall-${insightReportId}.docx"`,
    );
    file.pipe(res);
  }
}
