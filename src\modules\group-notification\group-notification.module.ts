import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { NotificationBackendModule } from '../../providers/notification-backend/notification-backend.module';
import { RedisModule } from '../../providers/redis/redis.module';
import { MailModule } from '../../providers/mail/mail.module';
import { GroupNotificationController } from './group-notification.controller';
import { GroupNotificationService } from './group-notification.service';
import { ConfigModule } from '@nestjs/config';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';

@Module({
  imports: [
    PrismaModule,
    RedisModule,
    NotificationBackendModule,
    MailModule,
    ConfigModule,
    FeatureFlagModule,
  ],
  controllers: [GroupNotificationController],
  providers: [GroupNotificationService],
  exports: [GroupNotificationService],
})
export class GroupNotificationModule {}
