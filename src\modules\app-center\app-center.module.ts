import { forwardRef, Module } from '@nestjs/common';
import { AppCenterService } from './app-center.service';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { KeycloakModule } from '../../providers/keycloak/keycloak.module';
import { ConfigModule } from '@nestjs/config';
import { MembershipsModule } from '../memberships/memberships.module';
import { SessionsModule } from '../sessions/sessions.module';
import { AppCenterController } from './app-center.controller';

@Module({
  imports: [
    PrismaModule,
    KeycloakModule,
    ConfigModule,
    forwardRef(() => MembershipsModule),
    SessionsModule,
  ],
  providers: [AppCenterService],
  exports: [AppCenterService],
  controllers: [AppCenterController],
})
export class AppCenterModule {}
