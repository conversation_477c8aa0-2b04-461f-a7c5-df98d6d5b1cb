import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  FeatureFlag,
  GroupType,
  KYCVerification,
  LoginType,
  Prisma,
  ResourceSubsciberType,
  RoleType,
  SummaryCallingType,
  SummaryKeyType,
  User,
  Email,
  Role,
  VerificationStatus,
  VerificationType,
} from '@prisma/client';
import { compare } from 'bcryptjs';
import { addDays, isBefore } from 'date-fns';
import { GROUP_MEMBERSHIP_SCOPE_KEY, USER_SCOPE_KEY } from 'src/providers/redis/redis.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { safeEmail } from '../../helpers/safe-email';
import { MailService } from '../../providers/mail/mail.service';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { S3Service } from '../../providers/s3/s3.service';
import { MERGE_ACCOUNTS_TOKEN } from '../../providers/tokens/tokens.constants';
import { TokensService } from '../../providers/tokens/tokens.service';
import { UserRequest } from '../auth/auth.interface';
import { AuthService } from '../auth/auth.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { PlansService } from '../plans/plans.service';
import { WikijsService } from '../wikijs/wikijs.service';
import {
  ActivateAccountDTO,
  ActivateUserDTO,
  BatchCreateOrUpdateUserRecordDto,
  BatchUploadTemplate,
  ChangePasswordDto,
  CreateUserDTO,
  PublicUserWhereDto,
  ResendActivateCodeDTO,
  UpdateUserDto,
  UpdateUserProfleDto,
} from './users.dto';
import { ResizeImageService } from '../../providers/resize-image/resize-image.service';
import { isValidEmail } from 'src/helpers/string';

@Injectable()
export class UsersService {
  logger = new Logger(UsersService.name);
  constructor(
    private redis: RedisService,
    private prisma: PrismaService,
    private auth: AuthService,
    private email: MailService,
    private configService: ConfigService,
    private tokensService: TokensService,
    private s3Service: S3Service,
    private wikijsService: WikijsService,
    private featureFlagService: FeatureFlagService,
    private plansService: PlansService,
    private resizeImageService: ResizeImageService,
  ) {}

  async getBatchUploadFileTemplate(): Promise<BatchUploadTemplate[]> {
    const isAllowDeleteUser = (
      await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        undefined,
        FeatureFlagKey.ENABLE_TRUE_DELETE_USER,
      )
    )?.isEnabled;
    const isAllowLocalLogin = (
      await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        undefined,
        FeatureFlagKey.ENABLE_USERNAME_PASSWORD_LOGIN,
      )
    )?.isEnabled;

    const isAllowHktSSOLogin = (
      await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        undefined,
        FeatureFlagKey.ENABLE_HKT_SSO_LOGIN,
      )
    )?.isEnabled;
    return [
      ...(isAllowHktSSOLogin
        ? [
            {
              email: '<EMAIL>',
              name: 'Test1',
              role: 'BOT_CREATOR',
              staffId: '02005772',
              loginType: 'HKT',
              remarks: 'It is a remark',
              action: 'add',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
            {
              email: '<EMAIL>',
              name: 'Test1 New',
              role: 'BOT_CREATOR',
              staffId: '02005772',
              loginType: 'HKT',
              remarks: 'It is a new remark',
              action: 'update',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
            {
              email: '<EMAIL>',
              name: 'Test1 New',
              role: 'BOT_CREATOR',
              staffId: '02005772',
              loginType: 'HKT',
              remarks: 'It is a new remark',
              action: 'deactivate',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
            {
              email: '<EMAIL>',
              name: 'Test1 New',
              role: 'BOT_CREATOR',
              staffId: '02005772',
              loginType: 'HKT',
              remarks: 'It is a new remark',
              action: 'activate',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
          ]
        : []),
      ...(isAllowDeleteUser
        ? [
            {
              email: '<EMAIL>',
              name: 'Test2',
              role: 'USER',
              staffId: '02005773',
              loginType: 'HKT',
              remarks: 'It is a remark',
              action: 'remove',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
          ]
        : []),
      ...(isAllowLocalLogin
        ? [
            {
              email: '<EMAIL>',
              name: 'Test1',
              role: 'BOT_CREATOR',
              staffId: '02005774',
              loginType: 'LOCAL',
              remarks: 'It is a remark',
              action: 'add',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
          ]
        : []),
      ...(isAllowLocalLogin
        ? [
            {
              email: '<EMAIL>',
              name: 'Test1 New',
              role: 'BOT_CREATOR',
              staffId: '02005774',
              loginType: 'LOCAL',
              remarks: 'It is a new remark',
              action: 'update',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
          ]
        : []),
      ...(isAllowDeleteUser && isAllowLocalLogin
        ? [
            {
              email: '<EMAIL>',
              name: 'Test2',
              role: 'USER',
              staffId: '02005775',
              loginType: 'LOCAL',
              remarks: 'It is a remark',
              action: 'remove',
              department: 'Auxiliary Services',
              ccc: 'C001 - TELECOM GROUP CO REV-TSP',
              businessUnit: 'Commercial Group',
            },
          ]
        : []),
    ];
  }

  async getUser(id: number) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: { emails: true, userRole: true },
    });
    if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    return user;
  }

  async getUserCount(where?: Prisma.UserWhereInput) {
    try {
      const count = await this.prisma.user.count({ where });
      return count;
    } catch (err) {
      this.logger.error(err, 'failed to get user count');
      throw err;
    }
  }

  async getUserCompleteness(userId: number): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { emails: true, userRole: true },
    });
    if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    const completeness = [user.businessUnit, user.ccc, user.department, user.name].every(
      (value) => !!value?.trim(),
    );
    return completeness;
  }

  async getUsers(params: {
    dateFrom?: string;
    dateTo?: string;
    skip?: number;
    take?: number;
    cursor?: Prisma.UserWhereUniqueInput;
    where?: PublicUserWhereDto;
    orderBy?: Prisma.UserOrderByWithRelationInput;
  }) {
    const { dateFrom, dateTo, skip, take, cursor, where, orderBy } = params;
    try {
      const users = await this.prisma.user.findMany({
        skip,
        take,
        cursor,
        where,
        orderBy,
        include: { emails: true, userRole: true },
      });
      // get the bot usage
      const result = await Promise.all(
        users.map(async (user) => {
          const rangedBotUsages = await this.prisma.summary.findMany({
            where: {
              group: {
                NOT: {
                  groupType: GroupType.FLOW,
                },
              },
              callingBy: user.id,
              callingType: SummaryCallingType.USER_PLAYGROUND,
              key: SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL,
              ...(dateFrom && dateTo
                ? { startDate: { gte: new Date(dateFrom), lte: new Date(dateTo) } }
                : {}),
            },
          });
          const rangedNumOfToken = rangedBotUsages.reduce((acc, botUsage) => {
            acc += botUsage.value;
            return acc;
          }, 0);

          const totalBotUsages = await this.prisma.summaryAll.findMany({
            where: {
              group: {
                NOT: {
                  groupType: GroupType.FLOW,
                },
              },
              callingBy: user.id,
              callingType: SummaryCallingType.USER_PLAYGROUND,
              key: SummaryKeyType.TOTAL_COMPLETION_TOKENS_TOTAL,
            },
          });
          const totalNumOfToken = totalBotUsages.reduce((acc, botUsage) => {
            acc += botUsage.value.toNumber();
            return acc;
          }, 0);
          const isAllowSelectingPlan = (await this.getUserResourcePlansCount(user.id)) > 0;
          return {
            ...user,
            rangedNumOfToken,
            totalNumOfToken,
            isAllowSelectingPlan,
          };
        }),
      );
      return result;
    } catch (error) {
      this.logger.error(error, 'Failed to get users list');
      return [];
    }
  }

  async createUser(req: CreateUserDTO, userReq: UserRequest) {
    if (req.staffId) {
      req.staffId = req.staffId.toUpperCase();
    }
    try {
      if (req.name.trim() === '' || req.email.trim() === '') {
        throw new Error();
      }
      // remove bu checking as user can free text in ui
      // await this.checkBusinessUnitOption(req.businessUnit.trim());

      if (req.remarks && req.remarks.length > this.configService.get<number>('maxRemarksCount')) {
        this.logger.error(req.staffId, 'Invalid Staff ID');
        throw new ApiException(ErrorCode.INVALID_REMARKS_LENGTH);
      }
      if (req.staffId && !new RegExp('^[-._A-Za-z0-9]{1,21}$', 'g').test(req.staffId)) {
        this.logger.error(req.staffId, 'Invalid Staff ID');
        throw new ApiException(ErrorCode.INVALID_STAFF_ID);
      }

      if (req.loginType.trim() === 'HKT') {
        //check user email domain is belong domain whitelist or not, if not , then can't create user.
        const emailDomainFeatureFlag: FeatureFlag = await this.featureFlagService.getOne(
          'ACCOUNT_MANAGEMENT.CONFIG_SSO_EMAIL_WHITELIST',
        );
        // checking HKT Staff Id
        if (!req.staffId) {
          this.logger.error(`Failed to create HKT account  - ${req.name} due to missing staff id `);
          throw new ApiException(ErrorCode.MISSING_STAFF_ID);
        }

        const isProviderIdExist = await this.prisma.user.findFirst({
          where: {
            loginProviderUniqueKey: {
              equals: req.staffId,
              mode: 'insensitive',
            },
            loginType: 'HKT',
          },
        });
        if (isProviderIdExist) {
          this.logger.error(
            `Failed to create HKT account by - ${req.staffId} due to the staff id has been used`,
          );
          throw new ApiException(ErrorCode.USED_STAFF_ID);
        }
        if (emailDomainFeatureFlag?.isEnabled) {
          //check email format
          const email = req.email;
          if (!isValidEmail(email)) {
            throw new ApiException(ErrorCode.INVALID_EMAIL_FORMAT);
          }

          const emailDomain = email.split('@')[1];
          const metaData = emailDomainFeatureFlag.metaData;
          //get email domain whitelist
          const metadateArray = metaData?.['value'] ? metaData['value'] : [];

          let pass = false;

          //check user email domain in whitelist or not
          for (const i in metadateArray) {
            const domain = metadateArray[i];

            if (emailDomain === domain) {
              pass = true;
            }
          }

          if (!pass) {
            throw new ApiException(ErrorCode.INVALID_EMAIL_DOMAIN);
          }
        }
      }

      if (req.loginType === LoginType.LOCAL) {
        if (
          !(await this.featureFlagService.getOne(FeatureFlagKey.ENABLE_USERNAME_PASSWORD_LOGIN))
            ?.isEnabled
        ) {
          throw new ApiException(ErrorCode.INVALID_LOGIN_TYPE);
        }
      }

      const res = await this.prisma.$transaction(async (tx) => {
        const existedUserCount = await this.prisma.user.count({
          where: {
            emails: {
              some: { email: req.email.toLowerCase(), emailSafe: req.email.toLowerCase() },
            },
          },
        });
        if (existedUserCount > 0) {
          throw new ApiException(ErrorCode.USER_ALREADY_EXIST);
        }
        const role = await this.prisma.role.findUnique({
          where: {
            id: req.roleId,
          },
        });
        if (!role || role.roleType !== RoleType.SYSTEM_DEFAULT) {
          throw new ApiException(ErrorCode.INVALID_USER_ROLE);
        }
        const id = Number(`10${await this.tokensService.generateRandomString(6, 'numeric')}`);

        const user = await tx.user.create({
          data: {
            remarks: req?.remarks ?? '',
            staffId: req?.staffId ?? '',
            name: req.name,
            roleId: req.roleId,
            businessUnit: req?.businessUnit ?? '',
            ccc: req?.ccc ?? '',
            department: req?.department ?? '',
            id,
            ssoId: req.ssoId?.trim() === '' ? null : req.ssoId,
            active: true,
            emails: {
              create: {
                email: req.email.toLowerCase(),
                emailSafe: req.email.toLowerCase(),
                isVerified: req.loginType === LoginType.LOCAL ? false : true,
              },
            },
            loginType: req.loginType,
            loginProviderUniqueKey: req.loginType === LoginType.LOCAL ? null : req.staffId,
          },
          include: { emails: true },
        });
        // create verification code for local user
        if (req.loginType === LoginType.LOCAL) {
          const expiryDay = this.configService.get<number>('kYCVerification.expiryDay');
          const verificationCode = await this.tokensService.generateRandomString(8, 'numeric');
          await tx.kYCVerification.create({
            data: {
              requestorId: userReq.user.id,
              userId: user.id,
              type: VerificationType.ACTIVATE_USER,
              expiredAt: addDays(new Date(), expiryDay),
              code: verificationCode,
            },
          });
          const frontendUrl = this.configService.get<string>('frontendUrl');
          await this.email.send({
            to: req.email,
            template: 'auth/activate-account',
            data: {
              name: req.name,
              link: `${frontendUrl}/auth/activate/${verificationCode}`,
              days: expiryDay,
            },
          });
          return user;
        }
        return user;
      });
      // HKT(or null) no need verification, so create wikijs user here
      if (res.loginType === LoginType.HKT || res.loginType == null) {
        await this.wikijsService.handleCreateBotBuilderUser(
          res.id,
          res.name,
          res.loginType,
          res.emails[0].emailSafe,
          null,
          res.ccc,
          res.staffId,
        );
      }
      return res;
    } catch (err) {
      this.logger.log(err, 'failed to create user');
      if (err instanceof ApiException) {
        throw err;
      } else {
        throw new ApiException(ErrorCode.CREATE_USER_FAILED);
      }
    }
  }

  async updateUserProfile(
    id: number,
    data: UpdateUserProfleDto,
    files: Array<Express.Multer.File>,
  ): Promise<Expose<User>> {
    if (data.staffId) {
      data.staffId = data.staffId.toUpperCase();
    }
    const existingUser = await this.prisma.user.findUnique({ where: { id } });
    if (!existingUser) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    await this.updateUserAvatar(id, files, data);
    const updatedUser = await this.prisma.user.update({
      data: {
        name: data.name,
        staffId: data.staffId,
        businessUnit: data.businessUnit,
        department: data.department,
        ccc: data.ccc,
        profilePictureS3Path: data.profilePictureS3Path,
      },
      where: { id },
      include: { emails: true, userRole: true },
    });

    if (updatedUser.wikijsId) {
      const wikijsPayload: { name?: string; staffId?: string } = {};
      if (data.name !== existingUser.name) {
        wikijsPayload.name = updatedUser.name;
      }
      if (data.staffId !== undefined && data.staffId !== existingUser.staffId) {
        wikijsPayload.staffId = updatedUser.staffId;
      }
      if (Object.keys(wikijsPayload).length > 0) {
        const wikijsUpdateSucceeded = await this._handleWikijsUserUpdateAttempt(
          id, // userIdForLogging
          updatedUser.wikijsId,
          wikijsPayload,
        );

        if (!wikijsUpdateSucceeded) {
          this.logger.warn(
            `Rolling back user profile update for user ${id} due to Wiki.js update failure.`,
          );
          await this.prisma.user.update({
            where: { id },
            data: {
              name: existingUser.name,
              staffId: existingUser.staffId,
              businessUnit: existingUser.businessUnit,
              department: existingUser.department,
              ccc: existingUser.ccc,
              profilePictureS3Path: existingUser.profilePictureS3Path,
            },
          });
          this.logger.log(`User profile for user ${id} rolled back successfully.`);
          throw new ApiException(ErrorCode.UPDATE_USER_IN_GEN_KB_FAILED);
        }
      }
    }
    return updatedUser;
  }

  async updateUser(id: number, data: UpdateUserDto): Promise<Expose<User>> {
    if (data.remarks && data.remarks.length > this.configService.get<number>('maxRemarksCount')) {
      throw new ApiException(ErrorCode.INVALID_REMARKS_LENGTH);
    }

    const originalUser = await this.prisma.user.findUnique({
      where: { id },
      include: { emails: true, userRole: true },
    });
    if (!originalUser) throw new ApiException(ErrorCode.USER_NOT_FOUND);

    if (data.roleId) {
      const role = await this.prisma.role.findUnique({
        where: { id: data.roleId },
      });
      if (!role || role.roleType !== RoleType.SYSTEM_DEFAULT) {
        throw new ApiException(ErrorCode.INVALID_USER_ROLE);
      }
    }

    const { email } = data; // Email is handled separately
    const userUpdatePayload: Prisma.UserUncheckedUpdateInput = {
      name: data.name,
      active: data.active,
      roleId: data.roleId,
      ssoId: data.ssoId?.trim() === '' ? null : data.ssoId,
      // Explicitly include optional fields from DTO if they are provided
      ...(data.businessUnit !== undefined && { businessUnit: data.businessUnit }),
      ...(data.department !== undefined && { department: data.department }),
      ...(data.ccc !== undefined && { ccc: data.ccc }),
      ...(data.remarks !== undefined && { remarks: data.remarks }),
      // lastActiveDate and loginProviderUniqueKey are not taken from DTO here;
      // they are handled by specific logic below if data.active changes.
    };
    // userUpdatePayload.email is not set here as it's handled by separate transaction logic

    if (data.active === true && originalUser.active === false) {
      userUpdatePayload.lastActiveDate = new Date();
      if (originalUser.loginType === 'HKT' && !originalUser.loginProviderUniqueKey) {
        userUpdatePayload.loginProviderUniqueKey = originalUser.staffId;
      }
    }

    let updatedUserInTx: User & { emails: Email[]; userRole: Role };

    try {
      updatedUserInTx = await this.prisma.$transaction(async (tx) => {
        // Check if an email update is intended
        const newEmailLower = email && email.toLowerCase();

        if (newEmailLower && newEmailLower !== originalUser.emails[0].email) {
          if (!isValidEmail(email)) {
            this.logger.error(`Invalid email format - ${email}, user id - ${id}`);
            throw new ApiException(ErrorCode.INVALID_EMAIL_FORMAT); // Rolls back transaction
          }
          try {
            await tx.email.updateMany({
              where: { userId: id },
              data: {
                email: newEmailLower,
                emailSafe: newEmailLower,
              },
            });
          } catch (err) {
            this.logger.error(
              `Failed to update email in transaction (likely non-unique) - ${email}, user id - ${id}`,
            );
            throw new ApiException(ErrorCode.UPDATE_USER_EMAIL_FAILED); // Rolls back transaction
          }
        }

        const userAfterUpdate = await tx.user.update({
          data: userUpdatePayload,
          where: { id },
          include: { emails: true, userRole: true },
        });
        return userAfterUpdate;
      });

      if (data.active === false && originalUser.active === true) {
        await this.prisma.session.deleteMany({ where: { userId: id } });
        await this.redis.batchClearCache(
          USER_SCOPE_KEY.replace('{USER_ID}', id.toString()).replace('{GROUP_ID}', '*'),
        );
        await this.redis.batchClearCache(
          GROUP_MEMBERSHIP_SCOPE_KEY.replace('{GROUP_ID}', '*').replace('{USER_ID}', id.toString()),
        );
      }

      await this.redis.batchClearCache(
        USER_SCOPE_KEY.replace('{USER_ID}', updatedUserInTx.id.toString()),
      );

      if (updatedUserInTx.wikijsId) {
        let wikijsUpdateSucceeded = true; // Assume success if no update is needed
        const wikijsUpdateData: { email?: string; name?: string; staffId?: string } = {};

        if (updatedUserInTx.emails[0].email !== originalUser.emails[0].email) {
          wikijsUpdateData.email = updatedUserInTx.emails[0].email;
        }
        if (data.name && data.name !== originalUser.name) {
          wikijsUpdateData.name = data.name;
        }

        if (Object.keys(wikijsUpdateData).length > 0) {
          wikijsUpdateSucceeded = await this._handleWikijsUserUpdateAttempt(
            id, // userIdForLogging
            updatedUserInTx.wikijsId,
            wikijsUpdateData,
          );
        }

        if (!wikijsUpdateSucceeded) {
          this.logger.warn(
            `Rolling back user update for user ${id} due to Wiki.js update failure.`,
          );
          const userRollbackData: Prisma.UserUncheckedUpdateInput = {
            name: originalUser.name,
            roleId: originalUser.roleId,
            ssoId: originalUser.ssoId,
            active: originalUser.active,
            remarks: originalUser.remarks,
            lastActiveDate: originalUser.lastActiveDate,
            loginProviderUniqueKey: originalUser.loginProviderUniqueKey,
            businessUnit: originalUser.businessUnit,
            ccc: originalUser.ccc,
            department: originalUser.department,
            // staffId is not modified by userUpdatePayload in this function,
            // so it doesn't need to be explicitly rolled back.
          };

          await this.prisma.user.update({
            where: { id },
            data: userRollbackData,
          });

          if (email?.toLowerCase() !== originalUser.emails[0].email) {
            await this.prisma.email.updateMany({
              where: { userId: id },
              data: {
                email: originalUser.emails[0].email,
                emailSafe: originalUser.emails[0].email,
              },
            });
          }
          throw new ApiException(ErrorCode.UPDATE_USER_IN_GEN_KB_FAILED);
        }
      }
      return this.prisma.expose<User>(updatedUserInTx);
    } catch (err) {
      this.logger.error(err, `Failed to update user by user id - ${id}`);
      if (err instanceof ApiException) {
        throw err;
      }
      throw new ApiException(ErrorCode.UPDATE_USER_FAILED);
    }
  }

  async updateUserByEmailAndLoginType(user: CreateUserDTO): Promise<Expose<User>> {
    if (user.staffId) {
      user.staffId = user.staffId.toUpperCase();
    }
    const { email, loginType, name, remarks, roleId, staffId, businessUnit, ccc, department } =
      user;
    if (email.trim() === '' || loginType.trim() === '') {
      throw new BadRequestException('Invalid user input. Please try to fix and re-upload again.');
    }
    // remove bu checking as user can free text in ui
    // await this.checkBusinessUnitOption(businessUnit.trim());
    const existedUser = await this.prisma.user.findFirst({
      where: { loginType, emails: { some: { email } } },
      include: { emails: true, userRole: true }, // Include all fields for potential rollback
    });
    if (!existedUser) {
      throw new ApiException(ErrorCode.USER_NOT_FOUND);
    }

    // Store original data for potential rollback
    const originalUserData = { ...existedUser };

    const updatePayload: Prisma.UserUncheckedUpdateInput = {
      name,
      remarks,
      roleId,
      staffId,
      businessUnit,
      ccc,
      department,
    };

    const updatedUser = await this.prisma.user.update({
      where: { id: existedUser.id },
      data: updatePayload,
      include: { emails: true, userRole: true },
    });

    await this.redis.batchClearCache(
      USER_SCOPE_KEY.replace('{USER_ID}', existedUser.id.toString()),
    );

    if (updatedUser.wikijsId) {
      let wikijsUpdateSucceeded = true; // Assume success if no update is needed
      const wikijsUpdateData: { name?: string; staffId?: string } = {};

      if (user.name && user.name !== originalUserData.name) {
        wikijsUpdateData.name = user.name;
      }
      if (user.staffId && user.staffId !== originalUserData.staffId) {
        wikijsUpdateData.staffId = user.staffId;
      }
      // Email is the primary key for this function, so it's not expected to change for Wiki.js.

      if (Object.keys(wikijsUpdateData).length > 0) {
        wikijsUpdateSucceeded = await this._handleWikijsUserUpdateAttempt(
          existedUser.id, // userIdForLogging
          updatedUser.wikijsId,
          wikijsUpdateData,
        );
      }

      if (!wikijsUpdateSucceeded) {
        this.logger.warn(
          `Rolling back user update for user ${existedUser.id} (by email/loginType) due to Wiki.js update failure.`,
        );
        const rollbackData: Prisma.UserUncheckedUpdateInput = {
          name: originalUserData.name,
          remarks: originalUserData.remarks,
          roleId: originalUserData.roleId,
          staffId: originalUserData.staffId,
          businessUnit: originalUserData.businessUnit,
          ccc: originalUserData.ccc,
          department: originalUserData.department,
        };
        await this.prisma.user.update({
          where: { id: existedUser.id },
          data: rollbackData,
        });
        throw new ApiException(ErrorCode.UPDATE_USER_IN_GEN_KB_FAILED);
      }
    }
    return this.prisma.expose<User>(updatedUser);
  }

  private async _handleWikijsUserUpdateAttempt(
    userIdForLogging: number,
    wikijsId: number,
    payload: {
      email?: string;
      name?: string;
      staffId?: string;
      [key: string]: string | undefined;
    },
  ): Promise<boolean> {
    try {
      const response = await this.wikijsService.updateWikijsUser(wikijsId, payload);
      if (response?.users?.update?.responseResult?.succeeded === true) {
        return true;
      } else {
        this.logger.error(
          `Failed to update user ${userIdForLogging} (wikijsId: ${wikijsId}) in Wiki.js. Response: ${JSON.stringify(
            response,
          )}`,
        );
        return false;
      }
    } catch (wikijsError) {
      this.logger.error(
        wikijsError,
        `Error during Wiki.js update for user ${userIdForLogging} (wikijsId: ${wikijsId}). Payload: ${JSON.stringify(
          payload,
        )}`,
      );
      return false;
    }
  }

  async activateUserByEmailAndLoginType(user: ActivateAccountDTO): Promise<Expose<User>> {
    const { email, loginType, active, staffId } = user;
    try {
      if (loginType === 'LOCAL') {
        if (email.trim() === '' || loginType.trim() === '') {
          throw new BadRequestException(
            'Invalid user input. Please try to fix and re-upload again.',
          );
        }
        const existedUser = await this.prisma.user.findFirst({
          where: { loginType, emails: { some: { email } } },
        });
        if (!existedUser) {
          throw new ApiException(ErrorCode.USER_NOT_FOUND);
        }

        const updatedUser = await this.prisma.user.update({
          where: { id: existedUser.id },
          data: { active, lastActiveDate: new Date() },
        });
        return this.prisma.expose<User>(updatedUser);
      } else {
        const existedUser = await this.prisma.user.findFirst({
          where: {
            OR: [
              { loginProviderUniqueKey: { equals: staffId, mode: 'insensitive' } },
              { emails: { some: { email } } },
            ],
          },
        });
        if (!existedUser) {
          throw new ApiException(ErrorCode.USER_NOT_FOUND);
        }

        const updatedUser = await this.prisma.user.update({
          where: { id: existedUser.id },
          data: {
            active,
            lastActiveDate: new Date(),
            ...(existedUser.loginProviderUniqueKey ? {} : { loginProviderUniqueKey: staffId }),
          },
        });
        return updatedUser;
      }
    } catch (err) {
      this.logger.error(err, `failed to activate user - ${user.email}`);
      throw new ApiException(ErrorCode.ACTIVATE_USER_FAILED);
    }
  }

  async deleteUser(id: number): Promise<Expose<User>> {
    const user = await this.prisma.user.delete({ where: { id } });
    return user;
  }

  async deleteUserByEmailAndNameAndLoginType(
    email: string,
    name: string,
    loginType: LoginType,
  ): Promise<Expose<User>> {
    if (name.trim() === '' || email.trim() === '' || loginType.trim() === '') {
      throw new BadRequestException('Invalid input');
    }
    const existedUser = await this.prisma.user.findFirst({
      where: { name, loginType, emails: { some: { email } } },
    });
    if (!existedUser) {
      throw new ApiException(ErrorCode.USER_NOT_FOUND);
    }
    const deletedUser = await this.prisma.user.delete({ where: { id: existedUser.id } });
    return this.prisma.expose<User>(deletedUser);
  }

  async requestMerge(userId: number, email: string): Promise<{ queued: true }> {
    const emailSafe = safeEmail(email);
    const user = await this.prisma.user.findFirst({
      where: { emails: { some: { emailSafe } } },
      include: { prefersEmail: true },
    });
    if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    if (user.id === userId) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    const minutes = this.configService.get<string>('security.mergeUsersTokenExpiry') ?? '5m';

    this.email
      .send({
        to: `"${user.name}" <${user.prefersEmail.email}>`,
        template: 'users/merge-request',
        data: {
          name: user.name,
          minutes,
          link: `${this.configService.get<string>(
            'frontendUrl',
          )}/auth/link/merge-accounts?token=${this.tokensService.signJwt(
            MERGE_ACCOUNTS_TOKEN,
            { baseUserId: userId, mergeUserId: user.id },
            minutes,
          )}`,
        },
      })
      .catch((error: Error) => {
        this.logger.error(`email.send  ${error.message}`, error.stack);
      });
    return { queued: true };
  }
  async validateActivateCode(
    activateCode: string,
    verificationType: VerificationType,
  ): Promise<boolean> {
    const kYCVerification = await this.prisma.kYCVerification.findFirst({
      where: {
        code: activateCode,
        type: verificationType,
      },
    });
    if (
      !kYCVerification ||
      isBefore(kYCVerification.expiredAt, new Date()) ||
      kYCVerification.status === VerificationStatus.VERIFY_SUCCESS
    ) {
      return false;
    }
    const user = await this.getUser(kYCVerification.userId);
    if (!user) {
      return false;
    }
    return true;
  }

  async activateUser(data: ActivateUserDTO): Promise<KYCVerification> {
    const isValid = await this.validateActivateCode(data.code, VerificationType.ACTIVATE_USER);
    if (!isValid) {
      throw new ApiException(ErrorCode.INVALID_ACTIVATION_CODE);
    }
    if (data.password !== data.confirmedPassword) {
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }
    if (data.password.length < 8) {
      throw new ApiException(ErrorCode.INVALID_PASSWORD_LENGTH);
    }
    const hashedPassword = await this.auth.hashAndValidatePassword(data.password, true);
    let user = null;
    const kYCVerification = await this.prisma.$transaction(async (tx) => {
      const res = await tx.kYCVerification.update({
        data: {
          verifiedAt: new Date(),
          status: VerificationStatus.VERIFY_SUCCESS,
        },
        where: {
          code: data.code,
        },
      });
      user = await tx.user.update({
        data: {
          password: hashedPassword,
          emails: {
            updateMany: {
              data: {
                isVerified: true,
              },
              where: {},
            },
          },
        },
        include: { emails: true },
        where: { id: res.userId },
      });
      if (res.userId) {
        await this.wikijsService.updateWikiJSLocalPassword(user.emails[0].emailSafe, data.password);
      }

      return res;
    });
    // wikijs local users are created while verification
    // since the create api only creates verified users
    await this.wikijsService.handleCreateBotBuilderUser(
      user.id,
      user.name,
      user.loginType,
      user.emails[0].emailSafe,
      data.password,
      user.ccc,
      user.staffId,
    );
    return kYCVerification;
  }

  async resendActivateCode(userReq: UserRequest, data: ResendActivateCodeDTO) {
    const verificationCode = await this.tokensService.generateRandomString(8, 'numeric');
    const expiryDay = this.configService.get<number>('kYCVerification.expiryDay');
    try {
      const result = await this.prisma.kYCVerification.updateMany({
        data: {
          code: verificationCode,
          expiredAt: addDays(new Date(), expiryDay),
        },
        where: {
          requestorId: userReq.user.id,
          userId: data.userId,
          type: VerificationType.ACTIVATE_USER,
          status: VerificationStatus.PENDING,
        },
      });
      if (result.count === 0) {
        throw new Error();
      }
      const user = await this.getUser(data.userId);
      const frontendUrl = this.configService.get<string>('frontendUrl');
      await this.email.send({
        to: user.emails[0].emailSafe,
        template: 'auth/activate-account',
        data: {
          name: user.name,
          link: `${frontendUrl}/auth/activate/${verificationCode}`,
          days: expiryDay,
        },
      });
      return result;
    } catch (err) {
      this.logger.error('failed to resend activate code');
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }
  }

  async changePassword(data: ChangePasswordDto, userId: number) {
    if (data.password !== data.confirmedPassword) {
      throw new ApiException(ErrorCode.PASSWORD_NOT_MATCH);
    }
    if (data.password === data.originalPassword) {
      throw new ApiException(ErrorCode.OLD_NEW_PASSWORD_SAME);
    }
    if (data.password.length < 8) {
      throw new ApiException(ErrorCode.INVALID_PASSWORD_LENGTH);
    }
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (user.loginType !== LoginType.LOCAL) {
      throw new ApiException(ErrorCode.INVALID_LOGIN_TYPE);
    }
    const isMatched = await compare(data.originalPassword, user.password);
    if (!isMatched) {
      this.logger.error(`${user.name} is changing password ->> invalid original password`);
      throw new ApiException(ErrorCode.INCORRECT_ORIGINAL_PASSWORD);
    }
    const hashedPassword = await this.auth.hashAndValidatePassword(data.password, true);

    await this.wikijsService.syncWikiJsUserPassword(user.wikijsId, data.password);

    return await this.prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        password: hashedPassword,
      },
    });
  }

  async findUserIdByEmail(email: string) {
    const userId = await this.prisma.email.findFirst({
      where: { email: email },
      select: { userId: true },
    });
    if (!userId) throw new ApiException(ErrorCode.EMAIL_NOT_FOUND);
    return userId;
  }

  async getUserResourceCategory() {
    return await this.plansService.getResourceCategory(ResourceSubsciberType.USER);
  }

  async getUserResources(userId: number) {
    return await this.plansService.getResources(ResourceSubsciberType.USER, userId);
  }

  async getUserResourcePlans(userId: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { roleId: true },
    });
    if (!user) {
      throw new ApiException(ErrorCode.USER_NOT_FOUND);
    }
    return await this.plansService.getResourcePlans({
      subscriberId: userId,
      subscriberType: ResourceSubsciberType.USER,
      requiredPlanRoleId: user.roleId,
    });
  }

  async getUserResourcePlansCount(userId: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { roleId: true, id: true },
    });
    if (!user) {
      throw new ApiException(ErrorCode.USER_NOT_FOUND);
    }
    return this.plansService.getResourcePlansCount({
      requiredPlanRoleId: user.roleId,
      subscriberType: ResourceSubsciberType.USER,
      subscriberId: user.id,
    });
  }

  async subscribeUserResourcePlans(userId: number, subscribedPlanIds: number[]) {
    const user = await this.prisma.user.findUnique({
      select: { id: true, roleId: true },
      where: { id: userId },
    });
    if (!user) {
      throw new ApiException(ErrorCode.USER_NOT_FOUND);
    }

    return await this.plansService.subscribeResourcePlans({
      subscribedPlanIds,
      subscriberType: ResourceSubsciberType.USER,
      subscriberId: userId,
    });
  }

  async sendDeactivateEmailReminder(reminders) {
    try {
      for (const reminder of reminders) {
        const {
          email,
          deactivationDate,
          username,
          theDayBeforeDeactivation,
          dayOfDeactivateActiveUser,
          dateForNotLogin,
        } = reminder;

        if (!['dev', 'uat', 'prd'].includes(process.env['EMAIL_ENV'])) {
          throw new ApiException(ErrorCode.INVALID_ENV_VARIABLE);
        }

        let subject;
        switch (process.env['EMAIL_ENV']) {
          case 'dev': {
            subject = 'Login "DEV GenAI Platform" To Avoid Account Deactivation';
            break;
          }
          case 'uat': {
            subject = 'Login "UAT GenAI Platform" To Avoid Account Deactivation';
            break;
          }
          case 'prd':
            subject = 'Login "GenAI Platform" To Avoid Account Deactivation'; //As required, there is no need to include 'prd' in the subject
            break;
        }

        const template = 'users/user-deactivation-email-reminder-for-' + process.env['EMAIL_ENV'];

        const res = await this.email.send({
          to: email,
          subject: subject,
          template: template,
          data: {
            email,
            deactivationDate,
            username,
            theDayBeforeDeactivation,
            dayOfDeactivateActiveUser,
            dateForNotLogin,
          },
        });
        this.logger.log(`Sending email to ${email} successful: ${res}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send email: ${(error as any)?.message}`);
    }
  }

  async checkBusinessUnitOption(businessUnit: string) {
    if (businessUnit) {
      const existBusinessUnit = await this.prisma.businessUnit.findFirst({
        where: { name: businessUnit },
      });
      if (!existBusinessUnit) {
        this.logger.error('[UsersService][checkBusinessUnitOption] Business Unit not found.');
        throw new ApiException(ErrorCode.BUSINESS_UNIT_NOT_FOUND);
      }
    }
    return true;
  }

  public async getUsersByIds(ids: number[]) {
    return await this.prisma.user.findMany({
      where: {
        id: {
          in: ids,
        },
      },
    });
  }
  async updateUserAvatar(
    userId: number,
    files: Array<Express.Multer.File>,
    data: UpdateUserProfleDto,
  ) {
    if (files && files[0]) {
      // user upload avatar
      // check file size > 1MB
      if (files[0].size > 1 * 1024 * 1024) throw new ApiException(ErrorCode.FILE_TOO_LARGE);
      const fileExtension = files[0].originalname.split('.').pop();
      // check file extension in ['jpg', 'png']
      if (!['jpg', 'png'].includes(fileExtension.toLowerCase())) {
        throw new ApiException(ErrorCode.INVALID_FILE_EXTENSION);
      }
      if (!this.configService.get<string>(`s3.staticFilesBucket`))
        throw new InternalServerErrorException('Static file bucket not set');

      // resize image to 256x256
      const newImage = await this.resizeImageService.resizeImage(files[0].buffer, 256, 256);
      // upload image to s3
      const { Key } = await this.s3Service.uploadAndCacheControl(
        `user/avatar/picture-${userId}.${fileExtension}`,
        files[0].originalname,
        newImage,
        files[0].mimetype,
        userId,
        'max-age=600',
        this.configService.get<string>(`s3.staticFilesBucket`),
        false,
      );
      data.profilePictureS3Path = Key;
    } else if (!data.profilePictureS3Path) {
      // user remove avatar
      data.profilePictureS3Path = null;
    }
  }

  async getUserAvatar(avatarUserId: number) {
    if (!this.configService.get<string>(`s3.staticFilesBucket`))
      throw new InternalServerErrorException('Static file bucket not set');
    const user = await this.getUser(avatarUserId);
    if (!user.profilePictureS3Path) {
      throw new ApiException(ErrorCode.FIlE_NOT_FOUND);
    }
    try {
      return this.s3Service.getFileObject(
        this.configService.get<string>(`s3.staticFilesBucket`),
        user.profilePictureS3Path,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async clearUserCache(id: number) {
    try {
      await this.redis.batchClearCache(USER_SCOPE_KEY.replace('{USER_ID}', id.toString()));
      await this.redis.batchClearCache(
        GROUP_MEMBERSHIP_SCOPE_KEY.replace('{GROUP_ID}', '*').replace('{USER_ID}', id.toString()),
      );
      return {
        userId: id,
        success: true,
      };
    } catch (err) {
      this.logger.error(`Failed to clear user cache: ${(err as any)?.message}`);
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  public checkBatchCreateUserInput(req: BatchCreateOrUpdateUserRecordDto) {
    if (!req.name || req.name.trim().length < 3) {
      throw new ApiException(ErrorCode.INVALID_NAME_LENGTH_MIN_3);
    }
    if (!req.email || !isValidEmail(req.email)) {
      throw new ApiException(ErrorCode.INVALID_EMAIL_FORMAT);
    }
    if (!req.loginType || !Object.values(LoginType).includes(req.loginType)) {
      throw new ApiException(ErrorCode.INVALID_LOGIN_TYPE);
    }
  }
}
