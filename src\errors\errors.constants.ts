import { HttpException, HttpStatus } from '@nestjs/common';

export class ApiException extends HttpException {
  constructor(errorString: string, extra?: Record<string, unknown>) {
    const {
      statusCode,
      code,
      message,
      extra: updatedExtra,
    } = ApiException.parseErrorString(errorString, extra);
    super({ error: { code, message, extra: updatedExtra } }, statusCode);
  }

  static errorStringRegExp = /^(\d{3}-\d+-\d+): (.+)$/;

  static parseErrorString(
    errorString: string,
    extra?: Record<string, unknown>,
  ): {
    statusCode: number;
    code: string;
    message: string;
    extra: Record<string, unknown>;
  } {
    const matches = errorString.match(ApiException.errorStringRegExp);
    let code: string, message: string;
    if (matches) {
      [, code, message] = matches;
    } else {
      [, code, message] = ErrorCode.INVALID_ERROR_MESSAGE_FORMAT.match(
        ApiException.errorStringRegExp,
      );
      extra = { original: errorString, ...extra };
    }

    let statusCode = Number(code.substring(0, 3));
    statusCode = Object.values(HttpStatus).includes(statusCode)
      ? statusCode
      : HttpStatus.INTERNAL_SERVER_ERROR;
    return { statusCode, code, message, extra };
  }
}

// format: '<HTTP status code>-<JIRA ticket number>-<auto-incrementing number>: <error message>.'
export const enum ErrorCode {
  NO_TOKEN_PROVIDED = '400-0-1: No token provided.',
  DOMAIN_NOT_VERIFIED = '400-0-2: Domain not verified.',
  MFA_PHONE_NOT_FOUND = '400-0-3: Phone number not found.',
  MFA_PHONE_OR_TOKEN_REQUIRED = '400-0-4: Phone number or token is required.',
  MFA_NOT_ENABLED = '400-0-5: Multi-factor authentication is not enabled.',
  NO_EMAILS = '400-0-6: User has no email attached to it.',
  CURRENT_PASSWORD_REQUIRED = '400-0-7: Current password is required.',
  COMPROMISED_PASSWORD = '400-0-8: This password has been compromised in a data breach.',
  CANNOT_DELETE_SOLE_MEMBER = '400-0-9: Cannot remove the only member.',
  CANNOT_DELETE_SOLE_OWNER = '400-0-10: Cannot remove the only owner.',
  ORDER_BY_ASC_DESC = '400-0-11: Invalid sorting order.',
  ORDER_BY_FORMAT = '400-0-12: Invalid ordering format.',
  WHERE_PIPE_FORMAT = '400-0-13: Invalid query format.',
  OPTIONAL_INT_PIPE_NUMBER = '400-0-14: $key should be a number.',
  CURSOR_PIPE_FORMAT = '400-0-15: Invalid cursor format.',
  EMAIL_DELETE_PRIMARY = '400-0-16: Cannot delete primary email.',
  INVALID_DOMAIN = '400-0-17: Invalid domain.',
  SELECT_INCLUDE_PIPE_FORMAT = '400-0-18: Invalid query format.',
  MEMBERSHIP_ALREADY_EXIST = '400-0-19: Membership is already existed.',
  FILE_TOO_LARGE = '400-0-20: Uploaded file is too large.',
  FILE_NUM_EXCEED = '400-3007-1: Uploaded file number exceeded',
  FAIL_TO_UPDATE_BACKEND_MODEL = '400-0-21: Fail to update remote model.',
  FAIL_TO_UPDATE_DB_LLM_MODEL = '400-0-22: Fail to update LLM model in DB.',
  CREATE_LLM_MODEL_FAILED = '400-0-23: Fail to create remote model.',
  UPLOAD_FILE_TO_MODEL_FAILED = '400-0-24: Fail to upload file to a model.',
  DELETE_FILE_TO_MODEL_FAILED = '400-0-25: Fail to delete file to a model.',
  FAIL_TO_CREATE_API_KEY = '400-0-26: Fail to create API Key in DB.',
  USER_ALREADY_EXIST = '400-0-27: User is already existed.',
  INVALID_ACTIVATION_CODE = '400-0-28: Invalid activation code.',
  FAIL_TO_FORGET_PASSWORD = '400-0-29: Failed to forget password.',
  KB_ALREADY_SYNCING = '400-0-30: KB is already syncing.',
  DATA_PROMOTION_REQUEST_STATUS_NOT_UNSUPPORTED = '400-0-31: Unsupported data promotion status.',
  DATA_PROMOTION_REQUEST_INVALID_ENV = '400-0-32: Invalid promotion environment.',
  DATA_PROMOTION_REQUEST_ALREADY_EXISTS = '400-0-33: Data promotion request already exsits.',
  DATA_PROMOTION_DELETE_NOT_SUPPORTED = '400-0-34: Promoting deleted data is not supported for this data type.',
  ENTITY_NOT_PROMOTED = '400-0-35: The entity is not promoted before.',
  ENTITY_ALREADY_DELETED = '400-0-36: The entity is already delete from the environment.',
  CHAT_HISTORY_NO_CONTEXT_TO_CLEAR = '400-631-1: No context to clear.',
  MODEL_FILE_DELETE_PROCESSING = '400-708-1: Deleting processing files is not allowed.',
  INVALID_FLOW_BOT_REQUEST = '400-483-1: Invalid flow bot request.',
  INVALID_FLOW_BOT_REQUEST_REQUEST = '400-483-2: Invalid request of flow bot request.',
  INVALID_PROMOTE_FLOW_REQUEST = '400-483-3: Invalid promote flow request.',
  MODEL_FILE_NOT_APPROVED = '400-713-1: The file is not approved to process yet.',
  INVALID_PROMOTE_FLOW_REQUEST_BOT_NOT_PROMOTED = '400-899-1: Invalid promote flow request: The connected bots should all be promoted.',
  INVALID_REPORT_TYPE = '400-713-2: Invalid report type',
  OPENAPI_FORMAT_ERROR = '400-906-1: The openapi yaml format error ',
  OPENAPI_VERSION_NOT_SUPPROT = '400-906-2: The version cannot be supported .Please Convert your OpenAPI spec version to 3.*.*',
  LOAD_YAML_FILE_FAILED = '400-906-3: load yaml file error.',
  FILE_UPLOAD_LIMIT_EXCEEDED = '400-868-1: File upload limit exceeded.',
  INVALID_FEATURE_FLAG_PARAMETER = '400-839-1: Invalid feature flag parameter.',
  GET_GROUP_OPTION_FAILED = '400-1092-1: Fail to get group option.',
  INVALID_GROUP_OPTION = '400-1092-2: Invalid group option.',
  GET_USER_OPTION_FAILED = '400-1092-3: Fail to get user option.',
  INVALID_USER_OPTION = '400-1092-2: Invalid user option.',
  GET_MEMBERSHIP_FAILED = '400-1092-3: Fail to get membership.',
  FILE_ENTITY_TYPE_INVALID = '400-1102-1: Invalid file entity type.',
  CONNECT_API_CUSTOM_PARAM_INVALID = '400-1114-1: The CustomParam Invalid, Please check the CustomParam Key {CustomParam}.',
  INVALID_PERMISSION = '400-1188-1: Invalid permission.',
  FEATURE_FLAG_TYPE_INVALID = '400-652-1: Invalid feature flag type.',
  CONNECT_API_FEATURE_FLAGS_HOST_MAP_NOT_EMPTY = "400-815-1: The Host Map can't be Empty. ",
  CONNECT_API_FEATURE_FLAGS_HOST_MAP_INVALID = '400-815-2: The Host Map Invalid. ',
  CONNECT_API_FEATURE_FLAGS_HOST_URL_DUPLICATE = "400-815-3: The Host Map Value can't duplicate. ",
  CONNECT_API_FEATURE_FLAGS_HOST_VALUE_KEY_INVALID = '400-815-4: The Hos Value key just allow `test` and `prod`. ',
  CONNECT_API_FEATURE_FLAGS_HOST_VALUE_EMPTY = "400-815-5: The Host Value can't be Empty. Please fill `test` and `prod` value. ",
  CONNECT_API_FEATURE_FLAGS_HOST_VALUE_NOT_URL = '400-815-6: The Host Value must be a url. ',
  CONNECT_API_FEATURE_FLAGS_HOST_LIST_NOT_EXISTS = '400-815-7: The Host list not exists. ',
  FEATURE_FLAGS_DUPLICATE = '400-815-8: The Feature Flag already exists. ',
  FEATURE_FLAGS_NOT_OVERRIDE = "400-1588-1: The Feature Flag can't be override. ",
  FEATURE_FLAG_METADATA_NOT_NULL = "400-1588-2: The metaData can't be null.",
  FEATURE_FLAG_METADATA_VALUE_NOT_NULL = "400-1588-3: The metaData value key can't be null.",
  FEATURE_FLAG_METADATA_VALUE_NOT_INT = '400-1588-4: The metaData value key must be number',
  API_RESOURCE_UPLOAD_FILE_YAML_FORMAT_ERROR = '400-1905-0: The open API file format error , {msg}',
  BOT_REVIEW_NOMINATION_EXISTS = '400-1797-0: The bot review nomination is already existed',
  BOT_REVIEW_NOMINATION_INVALID = '400-4248-0: The bot review nomination request is invalid',
  MONTHLY_TOKEN_NOT_GREATE_THEN_MAX_NUM = '400-2079-0: The Monthly Token Limit cannot be greater than {max}',
  RESOURCE_QUOTA_FAILED = '400-2344-1: Failed to meet resource quota',
  DEACTIVATE_PLAN_FAILED = '400-2344-2: It may be a default or disabled plan. Failed to deactivate plan',
  CHAT_APPROACH_INCORRECT = '400-1806-2: The approach Incorrect .',
  CHAT_WITH_DATA_FAILED_PARSE_DATA = '400-1806-3: Failed to parse file into dataframes.',
  CHAT_WITH_DATA_MODULE_NOT_SUPPORT_RESPONSE = '400-1806-4: Bad request. The model is not supported.',
  CREATE_INSIGHT_FAILED = '400-2545-1: Failed to create new insight',
  UPDATE_INSIGHT_FAILED = '400-2545-2: Failed to update insight',
  GET_INSIGHT_FAILED = '400-2545-1: Failed to get insight',
  TEST_CASE_QUESTION_NOT_NULL = "400-2306-0: test case config error the question can't be null",
  INVALID_CHAT_OVERRIDES = '400-2994-1: The overrides is invalid request type',
  INVALID_CHAT_MESSAGE_FORMAT = '400-1971-1: Invalid chat message, please provided user question.',
  LABELS_ALREADY_EXIST = '400-2991-2: The Labels already exists.',
  MODEL_NOT_ALLOWED_FOR_TAGGING = '400-3023-1: Selected model is not allowed for tagging.',
  INVALID_TAGS_INPUT = '400-3023-2: Invalid tags input for searching.',
  FILE_UPLOAD_PRE_DAY_LIMIT_EXCEEDED = `400-3373-1: Upload file: [{limit}/{limit}]. Due to limitations in computing power, we recently had to limit the number of file uploads per bot per day to ensure the platform's stability. Please start with a small number of documents to test it out.`,
  MODEL_PRICE_CREATE_FAILED = '400-3085-0: Failed to create model price',
  MODEL_PRICE_UPDATE_FAILED = '400-3085-1: Failed to update model price',
  LLM_ENGINE_IS_NOT_ACTIVE = '400-3553-1: The {engine} Model is inactive. please use other LLM for api call.',
  INVALID_CHAT_FILE_TO_EMBEDDING = '400-3398-1: File not allowed to be embedded.',
  EMBEDDING_CHAT_FILE_NUMBER_OVER_LIMIT = '400-3840-1: The chat with file over the limit {limit}.',
  INVALID_NOTIFICATION_CONTENT = '400-3876-2: Content can not empty.',
  INVALID_NOTIFICATION_TEMPLATE = '400-3876-3: Template Name can not empty.',
  INVAILD_BOT_ID = "400-4088-2: bad request , the bot can't create public chat session",
  INVAILD_PUBLIC_GROUP_TYPE = '400-4088-3: Only bot can preview the public ',
  INVAILD_PUBLIC_CHAT = '400-4088-4: Only public bot can use this chat api',
  INVALID_NAME_LENGTH_MIN_3 = '400-4333-1: name must be longer than or equal to 3 characters.',
  ACTIVATE_USER_FAILED = '400-4188-1: Failed to activate / deactivate user, please make sure that staff id is filled and user is existed',
  UPDATE_USER_FAILED = '400-4188-2: Failed to update user, the staff id have been used or filled information is incorrect.',
  UPDATE_USER_EMAIL_FAILED = '400-4431-1: Failed to update user, the user email have been used or filled information is incorrect.',
  CHAT_HISTORY_NO_ALLOWED_TO_SHARED = '400-4352-1: The Bot not allowed to Shared chat History. ',
  SHARE_CHAR_LINK_GEN_FAILED = '400-4352-2: Failed to generate share chat link.',
  UPLOAD_INSIGHT_INPUT_FILES_FAILED = '400-3386-1: Failed to upload insight input files.',
  LABEL_FIELDS_REQUIRED = '400-4452-1: The label fields must all null or all not null.',
  CATEGORY_FIELDS_REQUIRED = '400-4452-2: The category fields must all null or all not null.',
  BAD_REQUEST_FROM_BATCH_PROCESSOR = '400-4486-0: Bad Request From Batch Processor.',
  INVALID_FILE_EXT_FOR_BATCH_FILE = '400-4486-1: Invalid file ext for Batch File.',
  INVALID_CHAT_SESSION_TO_UPDATE = '400-4593-1: Invalid data source to update in this scope.',
  APP_NOT_CREATED = '400-4688-7: The app was not created. Please check the app status',
  INVALID_ENUM_VALUE = '400-4805-1: Invalid enum value provided for {paramName}, should be one of [{enumValues}].',
  UPDATE_USER_IN_GEN_KB_FAILED = '400-4662-1: Failed to update user in gen kb.',
  BOT_TOOLS_ALREADY_CREATED = '400-5059-1: The Bot tools already Created. ',

  UNAUTHORIZED_RESOURCE = '401-0-1: Insufficient permission.',
  INVALID_CREDENTIALS = '401-0-2: Invalid credentials.',
  INVALID_MFA_CODE = '401-0-3: Invalid one-time code.',
  INVALID_TOKEN = '401-0-4: Invalid token.',
  UNVERIFIED_LOCATION = '401-0-5: Location is not verified.',
  MFA_BACKUP_CODE_USED = '401-0-6: Backup code is already used.',
  INVALID_LLM_MODEL_GROUP = '401-0-7: Your user group cannot access this LLM Model.',
  INVAILD_SSO_TOKEN = '401-0-8: Invalid SSO token provided.',
  PASSWORD_NOT_MATCH = '401-1703-1: New Password & Confirmed Password not match.',
  INCORRECT_ORIGINAL_PASSWORD = '401-1703-2: Incorrect Original Password.',
  INACTIVE_USER = '401-2210-1: User is Inactive.',
  INVALID_LOGIN_STAFF_ID = '401-4188-0: Invalid staff id, {staffId} not found on user record',

  DATA_PROMOTION_REQUEST_SELF_APPROVAL = '403-0-1: Approving self-requested promotion.',
  DATA_PROMOTION_REQUEST_OTHERS_CANCELLATION = "403-0-2: Canceling others' data promotion request.",
  DATA_PROMOTION_REQUEST_UNAUTHORIZED_UPDATE = '403-0-3: Only requester and operator can update request.',
  API_RESOURCE_CREAT_SNAPSHOT_NOT_ALLOW = '403-645-1: Create the api resource snapshot not allow',

  USER_NOT_FOUND = '404-0-1: User not found.',
  GROUP_NOT_FOUND = '404-0-2: Group not found.',
  SESSION_NOT_FOUND = '404-0-3: Session not found.',
  EMAIL_NOT_FOUND = '404-0-4: Email not found.',
  API_KEY_NOT_FOUND = '404-0-5: API key not found.',
  APPROVED_SUBNET_NOT_FOUND = '404-0-6: Approved subnet not found.',
  DOMAIN_NOT_FOUND = '404-0-7: Domain not found.',
  MEMBERSHIP_NOT_FOUND = '404-0-8: Membership not found.',
  BILLING_NOT_FOUND = '404-0-9: Billing not found.',
  CUSTOMER_NOT_FOUND = '404-0-10: Customer not found.',
  INVOICE_NOT_FOUND = '404-0-11: Invoice not found.',
  SUBSCRIPTION_NOT_FOUND = '404-0-12: Subscription not found.',
  SOURCE_NOT_FOUND = '404-0-13: Source not found.',
  WEBHOOK_NOT_FOUND = '404-0-14: Webhook not found.',
  LLM_MODEL_CONFIG_NOT_FOUND = '404-0-15: LLM Model Config not found.',
  API_PLAN_NOT_FOUND = '404-0-16: API plan not found.',
  MESSAGE_TEMPLATE_NOT_FOUND = '404-0-17: Message Template not found.',
  I18N_NOT_FOUND = '404-0-18: I18N not found.',
  FEATURE_FLAG_NOT_FOUND = '404-0-19: Feature Flag not found.',
  LLM_ENGINE_NOT_FOUND = '404-0-20: LLM Engine not found.',
  ENTITY_SNAPSHOT_NOT_FOUND = '404-0-21: Entity snapshot not found.',
  DATA_PROMOTION_REQUEST_NOT_FOUND = '404-0-22: Data promotion request not found.',
  ENTITY_NOT_FOUND = '404-0-23: Entity not found.',
  ENTITY_SNAPSHOT_DELETE_NOT_FOUND = '404-0-24: The entity snapshot is not exists or already has promotion requests attached to it.',
  FIlE_NOT_FOUND = '404-0-25: File not found.',
  CHAT_SESSIOIN_NOT_FOUND = '404-631-1: Chat session not found.',
  FLOW_NOT_FOUND = '404-483-1: Flow not found.',
  FLOW_BOT_NOT_FOUND = '404-483-2: Flow bot not found.',
  FLOW_BOT_REQUEST_NOT_FOUND = '404-483-3: Flow bot request not found.',
  API_RESOURCE_NOT_FOUND = '404-645-1: API Resource not found.',
  API_RESOURCE_UPLOAD_FIlE_NOT_FOUND = '404-645-2: File not found.',
  KEYCLOAK_CLIENT_NOT_FOUND = '404-4688-4: Keycloak client not found.',
  CHAT_FILE_NOT_FOUND = '404-1200-2: Chat file not found.',
  LLM_MODEL_NOT_FOUND = '404-836-1: LLM Model not found.',
  GROUP_WIKIJS_NOT_FOUND = '404-836-2: Group Wikijs not found.',
  ALERT_HISTORY_NOT_FOUND = '404-1448-1: Alert History not found.',
  GROUP_RATE_LIMIT_NOT_FOUND = '404-1702-1: Group Rate Limit Quota not found.',
  RESOURCE_NOT_FOUND = '404-1679-1: Resource not found.',
  PLAN_NOT_FOUND = '404-1679-2: Resource Plan not found.',
  RESOURCE_QUOTA_NOT_FOUND = '404-1986-1: Resource Quota not found.',
  INVALID_PLAN = '404-1679-3: Invalid Resource Plan',
  INVALID_PLAN_DELETE = '404-2211-1: Default / Disabled resource plan cannot be deleted.',
  INVALID_PLAN_REQUEST = '404-2448-1: Invalid Plan Request',
  DEPARTMENT_NOT_FOUND = '404-2557-1: Department not found.',
  CCC_NOT_FOUND = '404-2557-2: CCC not found.',
  BUSINESS_UNIT_NOT_FOUND = '404-2557-3: Business Unit not found.',
  USER_GROUP_NOT_FOUND = '404-1973-1: User Group not found.',
  LABELS_NOT_FOUND = '404-2991-1: The Labels not found. ',
  BOT_SECURITY_TEMPLE_NOT_FOUND = '404-2707-1: Bot Security Temple not found.',
  BOOKMARK_ENTITY_NOT_FOUND = '404-3423-1: The entity not found.',
  BOOKMARK_NOT_FOUND = '404-3423-2: The bookmark not found.',
  GROUP_NOTIFICATION_NOT_FOUND = '404-3876-1: Group Notification not found.',
  GROUP_NOTIFICATION_NO_RECIPIENT = '404-3876-4: No recipient found for the group notification.',
  NOTIFICATION_TEMPLATE_NOT_FOUND = '404-3876-5: Notification Template not found.',
  PUBLIC_BOT_NOT_FOUND = '404-4008-1: Public Bot not found.',
  SHARE_CHAT_NOT_FOUND = '404-4352-3: Share Chat Not Found.',
  NOT_FOUND_FROM_BATCH_PROCESSOR = '404-4486-0: Not Found From Batch Processor.',
  CATEGORY_NOT_FOUND = '404-4805-1: The Category not found. ',
  INVALID_UPLOAD_ACTION = '404-4506-1: Invalid upload action.',
  MESSAGE_TEMPLATE_NAME_REQUIRED = '404-4506-2: Message template name is required.',
  MESSAGE_TEMPLATE_ID_REQUIRED = '404-4506-2: Message template Id is required.',
  INVALID_AUTO_SELECT_FILTER = '404-4630-1: Invalid auto select filter.',
  MESSAGE_TEMPLATE_DELETE_CONFLICT = '404-4694-1: This Message Template is using or be bookmark and cannot deleted directly.',
  MESSAGE_TEMPLATE_SLUG_ERROR = '404-4694-2: This Message Template Slug format is error.',
  MESSAGE_TEMPLATE_SLUG_EXIST = '404-4694-3: This Message Template Slug is exist, please change the slug.',
  CREATE_APP_TYPE_NOT_FOUND = '404-4688-4: App Type Not Found.',
  BOT_TOOLS_NOT_FOUND = '404-4688-5: Bot Tools Not Found.',
  APP_NOT_FOUND = '404-4688-6: App Not Found.',
  ENTITY_SNAPSHOT_EMPTY = '404-4893-1: The entity snapshot is empty.',
  APP_WHITELIST_NOT_FOUND = '404-4853-1: App whitelist Not Found.',
  SCHEDULER_JOB_CONFIG_ERROR = '404-5021-1: please check your Preset Date/Expected start time or regular trigger. ',
  CREATE_SCHEDULER_JOB_ERROR = '404-5021-2: error to create scheduler job, ',
  UPDATE_SCHEDULER_JOB_ERROR = '404-5021-3: error to update scheduler job. ',
  DELETE_SCHEDULER_JOB_ERROR = '404-5021-4: error to delete scheduler job. ',
  SCHEDULER_JOB_NOT_FOUND = '404-5021-5: scheduler job not found. ',
  INVALID_SCHEDULER_JOB_STATUS = '404-5021-6: invalid scheduler job status. ',
  CREATOR_ERROR = '404-5021-7: You are not the creator of this scheduler job, only can "Save and Generate. ',

  EMAIL_USER_CONFLICT = '409-0-1: User with this email already exists.',
  EMAIL_VERIFIED_CONFLICT = '409-0-2: This email is already verified.',
  BILLING_ACCOUNT_CREATED_CONFLICT = '409-0-3: Billing account is already created.',
  MFA_ENABLED_CONFLICT = '409-0-4: Multi-factor authentication is already enabled.',
  FILE_UPDATE_FAILED = '409-0-5: Failed to update file status, the file may not be found.',
  FILE_DOWNLOAD_FAILED = '409-0-6: Failed to download file.',
  LIST_FILE_DOWNLOAD_FAILED = '409-0-7: Failed to list log file.',
  CREATE_USER_FAILED = '409-0-8: Failed to create user.',
  INVALID_EMAIL_DOMAIN = '409-968-1: Email domain not in domain whitelist.',
  INVALID_EMAIL_FORMAT = '409-968-2: Invalid Email Format.',
  INVALID_USER_ROLE = '409-0-9: Invalid User Role.',
  INVALID_PASSWORD_LENGTH = '409-0-10: Invalid password, please make sure your password should be longer than 8 digits.',
  INVALID_REMARKS_LENGTH = '409-0-11: The maximum number of remark is exceeded.',
  INVALID_META_DATA = '409-0-12: Make sure that the meta data is in json format.',
  INVALID_STAFF_ID = '409-0-13: Invalid Staff ID, please make sure your Staff ID should not be longer than 20 digits and in [-._A-Za-z0-9] format',
  MISSING_STAFF_ID = '409-4188-1: Missing Staff ID',
  USED_STAFF_ID = '409-4188-2: Invalid Staff ID, The staff id have been used by other user',
  INVALID_LOGIN_TYPE = '409-0-14: Invalid Login Type.',
  OLD_NEW_PASSWORD_SAME = '409-0-15: The new password cannot be the same as original password.',
  GET_KB_STATUS_FAILED = '409-0-16: Failed to get GEN KB status.',
  CHANGE_KB_STATUS_FAILED = '409-0-17: Failed to change GEN KB status, please try again.',
  GET_KB_GROUP_DETAIL_FAILED = '409-0-18: Failed to get GEN KB group detail, please try again.',
  USER_NOT_EXIST = '409-0-19: User does not exist.',
  MEMBERSHIP_NOT_EXIST = '409-0-20: The membership of the user does not exist.',
  GET_KB_PAGE_NOT_EXIST = '409-0-21: The GEN KB page does not exist.',
  UPDATE_KB_PAGE_FAILED = '409-0-22: Failed to update GEN KB page.',
  API_GATEWAY_SERVICE_EXCEPTION = '409-0-23: Fail to update API Gateway.',
  ROLE_CONFLICT = '409-0-24: The role is already existed in the group.',
  INVALID_IP_FORMAT = '409-1713-1: Invalid Ip format',
  UPDATE_API_KEY_FAILED = '409-1632-1: Failed to update api-key.',
  CREATE_API_KEY_FAILED = '409-1632-2: Failed to create api-key.',
  INVALID_SYSTEM_TEXT = '409-1973-2: Invalid system text.',
  INVALID_STATUS_TO_DELETE = '409-1973-3: Already used by broadcast.',
  USER_GROUP_NAME_DUPLICATED = '409-3177-1: User Group Name already existed.',
  BOT_MAX_TAGS_EXCEEDED = '409-3023-0: Bot max tag exceeded.',
  FILE_MAX_TAGS_EXCEEDED = '409-3023-1: File max tag exceeded.',
  INVALID_STATUS_FROM_BATCH_PROCESSOR = '409-4486-0: Invalid batch status for request.',
  KB_PEER_NOT_FOUND = '409-4669-1: KB Peer not found.',
  ADD_KB_PEER_NOT_FAILED = '409-4669-2: Fail to add KB Peer.',
  CATEGORY_ENTITY_DATA_INVALID = '409-4805-1: Exist category entity data is invalid.',

  DEPRECATED_LLM_MODEL_CHAT_API = '410-2243-1: The LLM Model chat API is deprecated.',
  DEPRECATED_FLOW_CHAT_API = '410-2305-1: The Flow chat API is deprecated.',

  TO_BE_REMOVED = '410-1851-1: To be removed',

  INVALID_FILE_EXTENSION = '415-1200-1: Invalid file extension.',

  UPDATE_FILES_FAILED = '417-643-1 Failed to update files.',
  CREATE_FILES_FAILED = '417-645-1: Failed to create files.',

  INVALID_UPLOAD_FILE_FORMAT = '422-645-1: Invalid upload file format.',

  RATE_LIMIT_EXCEEDED = '429-0-0: Rate limit exceeded.',
  BOT_MONTHLY_TOKEN_USAGE_EXCEEDED = '429-994-1: Monthly token limit exceeded. Please upgrade your plan.',
  BOT_IMAGE_USAGE_EXCEEDED = '429-4641-1: Image usage limit exceeded. Please upgrade your plan.',
  DEFAULT = '500-0-0: Unknown error occurred.',
  INVALID_ERROR_MESSAGE_FORMAT = '500-0-1: Invalid error message format.',
  INTERNAL_SERVER_ERROR = '500-0-2: Internal Server Error.',
  PROMOTE_FLOW_FAILED = '500-483-1: Failed to promote flow.',
  PII_DETECTED = '500-1506-1: Pii Detected.',
  INVALID_ENV_VARIABLE = '500-2280-1: Invalid environment variable',
  INVALID_FILE_TAG_RESPONSE = '500-3023-0: Invalid file tag response from LLM.',
  APP_CENTER_REMOVE_RESOURCE_FAILED = '500-5048-1: app center remove resource failed.',

  KEYCLOAK_ADMIN_CLIENT_FAILED = '503-4688-0: Keycloak admin client authentication failed.',
  KEYCLOAK_TOKEN_EXCHANGE_FAILED = '503-4688-1: Keycloak token exchange failed.',
  REQUEST_FLOW_BACKEND_SERVICE_FAILED = '503-1083-1: Failed to request flow backend service.',
  REQUEST_CHAT_WITH_DATA_SERVICE_FAILED = '503-1806-1:  Failed to request chat with data service.',
  REQUEST_AUTO_TEST_SERVICE_FAILED = '503-2306-1: Failed to request auto test service.',
  REQUEST_NOTIFICATION_SERVICE_FAILED = '503-1973-4: Failed to request notification service.',
  KEYCLOAK_RESOURCE_CREATION_FAILED = '503-4688-2: Keycloak UMA resource creation failed.',
  KEYCLOAK_RESOURCE_DELETION_FAILED = '503-4688-3: Keycloak UMA resource deletion failed.',
}
