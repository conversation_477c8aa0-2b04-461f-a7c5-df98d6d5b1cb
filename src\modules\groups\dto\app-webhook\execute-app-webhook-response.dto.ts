import { ApiProperty } from '@nestjs/swagger';

export class ExecuteAppWebhookResponseDto {
  @ApiProperty({
    description: 'Indicates if the webhook execution was successfully initiated.',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'A message confirming the action.',
    example: 'App webhook execution initiated.',
  })
  message: string;

  @ApiProperty({
    description:
      'Placeholder for the actual response from the App webhook. This will be defined more concretely in later tasks.',
    type: 'object',
    additionalProperties: true,
    example: { appWebhookResponseData: 'some data' },
  })
  appWebhookResponse?: Record<string, unknown>;
}
