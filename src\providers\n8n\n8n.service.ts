import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosError } from 'axios';
import { Configuration } from 'src/config/configuration.interface'; // Assuming this interface is standard

@Injectable()
export class N8nService {
  private readonly logger = new Logger(N8nService.name);
  private readonly client: AxiosInstance;
  private readonly n8nBaseUrl: string;

  constructor(private readonly configService: ConfigService) {
    const n8nConfig = this.configService.get<Configuration['n8n']>('n8n');

    if (!n8nConfig?.baseUrl) {
      this.logger.error('CRITICAL: N8N_BASE_URL is not configured. N8nService cannot function.');
      throw new Error('Environment variable N8N_BASE_URL must be set for N8nService to function.');
    }
    this.n8nBaseUrl = n8nConfig.baseUrl;

    this.client = axios.create(); // Base URL will be prepended dynamically

    this.client.interceptors.request.use((req) => {
      this.logger.debug(`Sending request to N8N: ${req.method?.toUpperCase()} ${req.url}`);
      if (req.data) {
        this.logger.debug(`N8N Request data: ${JSON.stringify(req.data)}`);
      }
      return req;
    });

    this.client.interceptors.response.use(
      (res) => {
        this.logger.debug(`Received response from N8N: ${res.status} ${res.config.url}`);
        return res;
      },
      (error: AxiosError) => {
        this.logger.error(
          `Error response from N8N: ${error.response?.status} ${error.config?.url}`,
          error.stack,
        );
        if (error.response?.data) {
          this.logger.error(`N8N Error data: ${JSON.stringify(error.response.data)}`);
        }
        // Re-throw or handle specific errors
        return Promise.reject(error);
      },
    );
  }

  /**
   * Executes a specified N8N webhook.
   * @param groupId - The ID of the group (for logging/context, not directly used in N8N URL construction here)
   * @param customN8NPath - The custom path segment for the N8N webhook.
   * @param webhookInputs - The inputs to pass to the N8N webhook.
   * @returns Promise<unknown> - The response from the N8N webhook.
   */
  async execute(
    groupId: number, // For logging context
    customN8NPath: string,
    webhookInputs: Record<string, unknown>,
  ): Promise<unknown> {
    if (!this.n8nBaseUrl) {
      this.logger.error(
        `[${groupId}] Cannot execute N8N webhook for path "${customN8NPath}": N8N_BASE_URL is not configured.`,
      );
      throw new Error('N8N Service is not configured (missing base URL).');
    }

    // Construct the full N8N webhook URL
    // Assuming N8N webhooks are exposed directly under the base URL + custom path
    // e.g., https://n8n.example.com/webhook/my-workflow/trigger1
    // The Design Doc mentions "N8N Webhook Base Path (e.g., /webhook/ or /webhook-test/)"
    // This base path should ideally be part of n8nConfig.baseUrl or a separate config.
    // For now, let's assume customN8NPath is relative to n8nBaseUrl or includes any necessary prefix like 'webhook/'
    // If n8nBaseUrl = "https://n8n.example.com" and customN8NPath = "webhook/my-flow", target = "https://n8n.example.com/webhook/my-flow"
    // If n8nBaseUrl = "https://n8n.example.com/webhook/" and customN8NPath = "my-flow", target = "https://n8n.example.com/webhook/my-flow"

    const targetUrl = `${this.n8nBaseUrl.replace(/\/$/, '')}/${customN8NPath.replace(/^\//, '')}`;
    this.logger.log(`[${groupId}] Executing N8N webhook. Target URL: ${targetUrl}`);

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    try {
      this.logger.debug(
        `[${groupId}] Attempting to call N8N webhook at ${targetUrl} with inputs: ${JSON.stringify(
          webhookInputs,
        )}`,
      );

      const response = await this.client.post(targetUrl, webhookInputs, { headers });

      this.logger.log(
        `[${groupId}] N8N webhook call to ${targetUrl} successful. Status: ${response.status}`,
      );
      return response.data;
    } catch (error: unknown) {
      let errorMessage = 'An unknown error occurred while executing N8N webhook.';
      let errorStack: string | undefined;

      if (error instanceof Error) {
        errorMessage = error.message;
        errorStack = error.stack;
      }

      this.logger.error(
        `[${groupId}] Error executing N8N webhook for path "${customN8NPath}": ${errorMessage}`,
        errorStack,
      );

      // Transform AxiosError or other errors into a consistent error response or rethrow
      if (axios.isAxiosError(error)) {
        throw new Error(
          `N8N platform request failed: ${error.response?.status} ${
            error.response?.data?.message || error.response?.data || error.message
          }`,
        );
      }
      // If it's already an error, rethrow it, otherwise wrap it
      throw error instanceof Error ? error : new Error(errorMessage);
    }
  }
}
