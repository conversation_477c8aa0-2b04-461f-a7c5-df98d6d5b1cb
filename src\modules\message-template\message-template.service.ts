import { Injectable, Logger } from '@nestjs/common';
import {
  LabelType,
  MessageTemplate,
  Prisma,
  TemplateAccessLevel,
  UserBookmarkEntityType,
} from '@prisma/client';
import { isAfter, subWeeks } from 'date-fns';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';
import { LabelsService } from '../labels/labels.service';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import {
  BatchUploadDto,
  CreateMessageTemplateDto,
  UpdateMessageTemplateDto,
  BatchUploadMessageTemDto, // Added import for BatchUploadMessageTemDto
} from './message-template.dto';
import { GroupsService } from '../groups/groups.service';

@Injectable()
export class MessageTemplateService {
  private logger = new Logger(MessageTemplateService.name);
  constructor(
    private prisma: PrismaService,
    private readonly labelsService: LabelsService,
    private readonly llmEnginesService: LlmEnginesService,
    private groupService: GroupsService,
  ) {}

  async getMessageTemplate(id: number): Promise<Expose<MessageTemplate>> {
    const messageTemplate = await this.prisma.messageTemplate.findUnique({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: { id },
    });
    if (!messageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const messageTemplateFormatted =
      this.labelsService.formatLabelsAndCategoriesData(messageTemplate);
    return messageTemplateFormatted;
  }

  async getMessageTemplates(
    params: {
      skip?: number;
      take?: number;
      cursor?: Prisma.MessageTemplateWhereUniqueInput;
      where?: Prisma.MessageTemplateWhereInput;
      orderBy?:
        | Prisma.MessageTemplateOrderByWithRelationInput[]
        | Prisma.MessageTemplateOrderByWithRelationInput;
    },
    templateAccessLevel?: TemplateAccessLevel,
    groupId?: number,
    type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ): Promise<Expose<MessageTemplate>[]> {
    const { skip, take, cursor, where, orderBy } = params;
    let condition: Prisma.MessageTemplateWhereInput =
      this.labelsService.formatLabelsAndCategoriesFilter('MESSAGE_TEMPLATE', where);
    condition = this.getMessageCondition(condition, templateAccessLevel, groupId, type);

    const messageTemplates = await this.prisma.messageTemplate.findMany({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      skip,
      take,
      cursor,
      where: {
        ...condition,
        OR: [
          {
            group: {
              isDeprecated: false,
            },
          },
          {
            groupId: null,
          },
        ],
      },
      orderBy,
    });
    const groupIds = messageTemplates.map((item) => item.groupId).filter((item) => item);
    const groupNames =
      groupIds.length != 0
        ? await this.prisma.group.findMany({
            select: { name: true, id: true },
            where: { id: { in: groupIds } },
          })
        : [];
    return messageTemplates.map((messageTemplate) => ({
      ...messageTemplate,
      ...this.labelsService.formatLabelsAndCategoriesData(messageTemplate),
      groupName: messageTemplate.groupId
        ? groupNames.find((item) => item.id === messageTemplate.groupId)?.name ?? '(delete bot)'
        : undefined,
    }));
  }

  public getMessageCondition(
    condition: Prisma.MessageTemplateWhereInput,
    templateAccessLevel?: TemplateAccessLevel,
    groupId?: number,
    type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ) {
    if (type && type == 'SYSTEM') {
      condition.groupId = null;
    }
    if (type && type == 'GROUP') {
      condition.NOT = {
        groupId: null,
      };
      condition.group = {
        isDeprecated: false,
      };
    }
    if (templateAccessLevel) {
      condition.accessLevel = templateAccessLevel;
    }
    if (groupId) {
      condition.groupId = groupId;
    }
    return condition;
  }

  async getMessageTemplatesCount(
    where?: Prisma.MessageTemplateWhereInput,
    templateAccessLevel?: TemplateAccessLevel,
    groupId?: number,
    type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ): Promise<number> {
    let condition: Prisma.MessageTemplateWhereInput =
      this.labelsService.formatLabelsAndCategoriesFilter('MESSAGE_TEMPLATE', where);
    condition = this.getMessageCondition(condition, templateAccessLevel, groupId, type);

    const count = await this.prisma.messageTemplate.count({
      where: {
        ...condition,
        OR: [
          {
            group: {
              isDeprecated: false,
            },
          },
          {
            groupId: null,
          },
        ],
      },
    });
    return count;
  }

  async getGroupAndSystemMessageTemplates(
    groupId: number,
    where?: Prisma.MessageTemplateWhereInput & {
      labels?: { in: string[] };
      categories?: { in: string[] };
      isMostlyUsed?: boolean;
      isNew?: boolean;
    },
    orderBy?: Prisma.MessageTemplateOrderByWithRelationInput,
  ): Promise<Expose<MessageTemplate>[]> {
    const _condition = this.labelsService.formatLabelsAndCategoriesFilter(
      'MESSAGE_TEMPLATE',
      where,
    );
    const { where: condition, mostlyUsedMessageTemplateIds } =
      await this.getCalculateQueryCondition(_condition);
    let messageTemplates = [];
    if (orderBy) {
      messageTemplates = await this.prisma.messageTemplate.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: {
          OR: [
            {
              accessLevel: TemplateAccessLevel.SYSTEM,
              OR: [
                {
                  group: {
                    isDeprecated: false,
                  },
                },
                {
                  groupId: null,
                },
              ],
            },
            {
              groupId: groupId,
            },
            ...(condition?.OR ?? []),
          ],
          AND: { ...{ ...condition, OR: undefined }, active: true },
        },
        orderBy,
      });
    } else {
      const groupMessageTemplates = await this.prisma.messageTemplate.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: {
          groupId: groupId,
          ...condition,
          active: true,
        },
        orderBy: {
          groupSequence: 'asc',
        },
      });
      const systemMessageTemplates = await this.prisma.messageTemplate.findMany({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: {
          accessLevel: TemplateAccessLevel.SYSTEM,
          active: true,
          ...condition,
          OR: [
            {
              groupId: null,
            },
            {
              group: { isDeprecated: false },
            },
          ],
        },
        orderBy: {
          systemSequence: 'asc',
        },
      });
      const _messageTemplates = systemMessageTemplates.filter((item) => item.groupId != groupId);
      messageTemplates = [...groupMessageTemplates, ..._messageTemplates];
    }

    return messageTemplates.map((messageTemplate) => ({
      ...messageTemplate,
      isMostlyUsed: mostlyUsedMessageTemplateIds.includes(messageTemplate.id),
      isNew: isAfter(messageTemplate.createdAt, subWeeks(new Date(), 2)),
      ...this.labelsService.formatLabelsAndCategoriesData(messageTemplate),
    }));
  }

  async updateMessageTemplate(
    id: number,
    updateMessageTemplateDto: UpdateMessageTemplateDto,
    userReq: UserRequest,
  ): Promise<Expose<MessageTemplate>> {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const updateMessageTemplateInput = { ...updateMessageTemplateDto, entityLabels: undefined };

    const { needRemoveIds, createEntityLabels, createLabels } =
      this.labelsService.formatPatchLabelsDto(
        updateMessageTemplateDto as any,
        userReq,
        'MESSAGE_TEMPLATE',
        id,
      );
    const messageTemplate = await this.prisma.$transaction(async (tx) => {
      await this.labelsService.patchUpdateWithTransaction(
        { needRemoveIds, createEntityLabels, createLabels },
        tx,
        'MESSAGE_TEMPLATE',
        id,
      );
      const messageTemplate = await tx.messageTemplate.update({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: { id },
        data: updateMessageTemplateInput as Prisma.MessageTemplateUpdateInput,
      });
      const updatedResult = this.labelsService.formatLabelsAndCategoriesData(messageTemplate);
      return updatedResult;
    });
    return messageTemplate;
  }

  async shareMessageTemplate(id: number): Promise<Expose<MessageTemplate>> {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const messageTemplate = await this.prisma.messageTemplate.update({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: { id },
      data: {
        accessLevel: TemplateAccessLevel.SYSTEM,
      },
    });
    return this.prisma.expose<MessageTemplate>(messageTemplate);
  }

  async unshareMessageTemplate(id: number): Promise<Expose<MessageTemplate>> {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    const messageTemplate = await this.prisma.messageTemplate.update({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: { id },
      data: {
        accessLevel: TemplateAccessLevel.GROUP,
      },
    });
    const updatedResult = this.labelsService.formatLabelsAndCategoriesData(messageTemplate);
    return updatedResult;
  }

  public async getPublicMessageTemplate(
    where?: Prisma.MessageTemplateWhereInput & {
      labels?: { in: string[] };
      categories?: { in: string[] };
      isMostlyUsed?: boolean;
      isNew?: boolean;
    },
    skip?: number,
    take?: number,
    orderBy?:
      | Prisma.MessageTemplateOrderByWithRelationInput[]
      | Prisma.MessageTemplateOrderByWithRelationInput,
  ) {
    const _condition = this.labelsService.formatLabelsAndCategoriesFilter(
      'MESSAGE_TEMPLATE',
      where,
    );
    const { where: condition, mostlyUsedMessageTemplateIds } =
      await this.getCalculateQueryCondition(_condition);
    const publicSystemMessageTemplatesWhere = {
      ...condition,
      active: true,
      accessLevel: TemplateAccessLevel.SYSTEM,
      OR: [
        {
          group: {
            isDeprecated: false,
          },
        },
        {
          group: null,
        },
      ],
    };
    const systemMessageTemplates = await this.prisma.messageTemplate.findMany({
      include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
      where: publicSystemMessageTemplatesWhere,
      skip,
      take,
      ...(orderBy
        ? { orderBy }
        : {
            orderBy: {
              systemSequence: 'asc',
            },
          }),
    });
    const count = await this.prisma.messageTemplate.count({
      where: publicSystemMessageTemplatesWhere,
    });
    const messageTemplateIds = systemMessageTemplates.map((item) => item.id);
    const messageTemplateBookmarkCount = await this.prisma.userBookmark.groupBy({
      by: ['entityType', 'entityId'],
      where: {
        entityType: UserBookmarkEntityType.MESSAGE_TEMPLATE,
        entityId: { in: messageTemplateIds },
      },
      _count: { id: true },
    });
    const publicMessageTemplates = systemMessageTemplates.map((item) => ({
      ...this.labelsService.formatLabelsAndCategoriesData(item),
      isMostlyUsed: mostlyUsedMessageTemplateIds.includes(item.id),
      isNew: isAfter(item.createdAt, subWeeks(new Date(), 2)),
      bookmarkedNo:
        messageTemplateBookmarkCount.find((_item) => _item.entityId == item.id)?._count?.id ?? 0,
    }));
    return {
      list: publicMessageTemplates,
      count,
    };
  }

  async deleteMessageTemplate(id: number) {
    const dbMessageTemplate = await this.prisma.messageTemplate.findUnique({
      where: { id },
    });
    if (!dbMessageTemplate) throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
    // check used and bookmark
    if (dbMessageTemplate.used > 0) {
      throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_DELETE_CONFLICT);
    }
    const bookmarkCount = await this.prisma.userBookmark.count({
      where: {
        entityType: UserBookmarkEntityType.MESSAGE_TEMPLATE,
        entityId: id,
      },
    });
    if (bookmarkCount > 0) {
      throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_DELETE_CONFLICT);
    }
    await this.prisma.$transaction(async (tx) => {
      await this.labelsService.removeEntityLabelsWithEntity(tx, id, 'MESSAGE_TEMPLATE');
      await tx.messageTemplate.delete({ where: { id } });
    });
    return dbMessageTemplate;
  }

  async createMessageTemplate(
    params: CreateMessageTemplateDto,
    userReq: UserRequest,
  ): Promise<MessageTemplate> {
    if (params.slug) {
      // convert slug to lowercase
      params.slug = params.slug.toLowerCase();
    }
    // check slug format and exist
    if (!params.slug || !/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(params.slug)) {
      throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_SLUG_ERROR);
    }
    // check slug is exist
    const existingTemplate = await this.getMessageTemplateBySlugOrGroupId(
      params.slug,
      params.groupId ?? null,
    );
    if (existingTemplate) {
      throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_SLUG_EXIST);
    }
    const createMessageTemplateInput = { ...params, entityLabels: undefined };
    const messageTemplate = await this.prisma.$transaction(async (tx) => {
      const createdMessageTemplate = await tx.messageTemplate.create({
        data: {
          ...createMessageTemplateInput,
          createdAt: new Date(),
        },
      });
      const { needRemoveIds, createEntityLabels, createLabels } =
        this.labelsService.formatPatchLabelsDto(
          params as any,
          userReq,
          'MESSAGE_TEMPLATE',
          createdMessageTemplate.id,
        );
      await this.labelsService.patchUpdateWithTransaction(
        { needRemoveIds, createEntityLabels, createLabels },
        tx,
        'MESSAGE_TEMPLATE',
        createdMessageTemplate.id,
      );
      const messageTemplateInfo = await tx.messageTemplate.findUnique({
        include: { ...(this.labelsService.getLabelsPrismaQuery('MESSAGE_TEMPLATE') as any) },
        where: { id: createdMessageTemplate.id },
      });
      return this.labelsService.formatLabelsAndCategoriesData(messageTemplateInfo);
    });
    return messageTemplate;
  }

  /**
   * @description  convert the calculated from where new will convert [isNew]
   * @param where
   * @returns
   */
  private async getCalculateQueryCondition(where: any) {
    if (where.isNew) {
      where.createdAt = {
        gte: subWeeks(new Date(), 2),
      };
      delete where.isNew;
    }
    const mostlyUsedMessageTemplate = await this.prisma.messageTemplate.findMany({
      select: {
        id: true,
      },
      where: {
        active: true,
        accessLevel: 'SYSTEM',
      },
      orderBy: {
        used: 'desc',
      },
      take: 5,
    });
    const mostlyUsedMessageTemplateIds = mostlyUsedMessageTemplate.map((item) => item.id);
    if (where?.isMostlyUsed) {
      where.id = {
        in: mostlyUsedMessageTemplateIds,
      };
      delete where.isMostlyUsed;
    }
    let _where = this.llmEnginesService.orConditionCheck(where);
    if (_where?.OR) {
      const newOrWhere = [];
      for (const whereItem of _where?.OR as Array<any>) {
        if (whereItem?.['isNew']) {
          newOrWhere.push({
            createdAt: {
              gte: subWeeks(new Date(), 2),
            },
          });
          delete whereItem.isNew;
        } else if (whereItem?.['isMostlyUsed']) {
          newOrWhere.push({
            id: {
              in: mostlyUsedMessageTemplateIds,
            },
          });
          delete whereItem.isMostlyUsed;
        } else {
          newOrWhere.push(whereItem);
        }
      }
      _where = {
        ..._where,
        OR: newOrWhere,
      };
    }
    return { where: _where, mostlyUsedMessageTemplateIds };
  }

  public async exportMessageTemplates(
    type: 'GROUP' | 'SYSTEM' | 'ALL',
    groupId?: number,
  ): Promise<BatchUploadDto> {
    const templatesToExport = await this.getMessageTemplates(
      { orderBy: [{ id: 'asc' }] }, // Consistent order for export
      undefined, // No specific accessLevel filter
      groupId ? groupId : undefined, // Pass groupId if available
      type, // Pass type here
    );
    const messageTemplateList: BatchUploadDto = { deleteSlugs: [], messageTemplateList: [] };
    const exportableTemplates: BatchUploadMessageTemDto[] = templatesToExport.map((template) => {
      const dto: BatchUploadMessageTemDto = {
        ordering:
          template.accessLevel === TemplateAccessLevel.SYSTEM
            ? template.systemSequence
            : template.groupSequence,
        isActive: template.active,
        isRecommended: template.isRecommended,
        public: template?.['accessLevel'] === TemplateAccessLevel.SYSTEM,
        templateName: template.title,
        templateDescription: template.description ?? '',
        templateGuideDetails: template.templateGuideDetails ?? '',
        categories: template?.['categories'].map((c) => ({ name: c.name })) ?? [],
        labels: template?.['labels'].map((l) => ({ name: l.name, color: l.color })) ?? [],
        templateInstruction: template.botInstruction ?? '',
        slug: template.slug,
      };
      return dto;
    });
    messageTemplateList.messageTemplateList = exportableTemplates;
    return messageTemplateList;
  }

  async createBatch(data: BatchUploadDto, userReq: UserRequest, groupId?: number) {
    const { deleteSlugs, messageTemplateList } = data;
    const successList = { deleteSlugs: [], messageTemplateList: [] };
    const failureList = { deleteSlugs: [], messageTemplateList: [] };
    // delete
    for (const slug of deleteSlugs) {
      try {
        // find message template by slug/groupId
        const foundTemplate = await this.getMessageTemplateBySlugOrGroupId(
          slug.toLowerCase(),
          groupId ? groupId : null,
        );
        if (!foundTemplate) {
          throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NOT_FOUND);
        }
        // delete message template
        await this.deleteMessageTemplate(foundTemplate.id);
        successList.deleteSlugs.push(slug);
      } catch (err) {
        this.logger.error(err, `Failed to`);
        failureList.deleteSlugs.push({
          slug,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    // update/create
    for (const dto of messageTemplateList) {
      try {
        // if name == null, then throw
        if (!dto.templateName) {
          throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_NAME_REQUIRED);
        }
        if (dto.slug) {
          // convert slug to lowercase
          dto.slug = dto.slug.toLowerCase();
        }
        // check slug format,then throw
        if (!dto.slug || !/^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(dto.slug)) {
          throw new ApiException(ErrorCode.MESSAGE_TEMPLATE_SLUG_ERROR);
        }
        // disposal data
        const messageTemplate = {
          title: dto?.templateName,
          botInstruction: dto?.templateInstruction ?? '',
          isRecommended: dto?.isRecommended ?? undefined,
          active: dto.isActive ?? undefined,
          entityLabels: [],
          groupId: undefined,
          messageContent: undefined,
          accessLevel: undefined,
          templateGuideDetails: dto.templateGuideDetails ?? '',
          systemSequence: undefined,
          groupSequence: undefined,
          description: dto.templateDescription ?? '',
          slug: dto.slug,
        };
        // get all labels, labelType = LABELS/CATEGORIES
        if (dto.categories || dto.labels) {
          const labelsList = await this.prisma.labels.findMany({
            where: {
              labelType: {
                in: [LabelType.CATEGORIES, LabelType.LABELS],
              },
            },
          });
          const { categories, labels } = this.labelsService.formatLabelsAndCategoriesByCondition(
            dto.categories,
            dto.labels,
            labelsList,
          );
          messageTemplate.entityLabels.push(...categories, ...labels);
        }

        // find message template by slug/groupId
        const foundTemplate = await this.getMessageTemplateBySlugOrGroupId(
          dto.slug,
          groupId ? groupId : null,
        );
        // if no null, update message template
        if (foundTemplate) {
          // group message template
          if (groupId) {
            messageTemplate.groupId = groupId;
            // check groupId is exist
            await this.groupService.getGroup(messageTemplate.groupId, {});
            // unshare message template
            messageTemplate.accessLevel = TemplateAccessLevel.GROUP;
            if (dto.public) {
              // share message template
              messageTemplate.accessLevel = TemplateAccessLevel.SYSTEM;
            }
            messageTemplate.groupSequence = dto.ordering ?? undefined;
            await this.prisma.$transaction(async (tx) => {
              await tx.entityLabels.deleteMany({
                where: {
                  entityId: foundTemplate.id,
                  LabelEntityType: 'MESSAGE_TEMPLATE',
                },
              });
              const mt = await this.updateMessageTemplate(
                foundTemplate.id,
                messageTemplate,
                userReq,
              );
            });
          } else {
            // system message template
            messageTemplate.accessLevel = TemplateAccessLevel.SYSTEM;
            messageTemplate.systemSequence = dto.ordering ?? undefined;
            await this.prisma.$transaction(async (tx) => {
              await tx.entityLabels.deleteMany({
                where: {
                  entityId: foundTemplate.id,
                  LabelEntityType: 'MESSAGE_TEMPLATE',
                },
              });
              await this.updateMessageTemplate(foundTemplate.id, messageTemplate, userReq);
            });
          }
        } else {
          // if null, create message template
          // group message template
          if (groupId) {
            messageTemplate.groupId = groupId;
            // check groupId is exist
            await this.groupService.getGroup(messageTemplate.groupId, {});
            messageTemplate.accessLevel = TemplateAccessLevel.GROUP;
            messageTemplate.groupSequence = dto.ordering ?? undefined;
            const mt = await this.createMessageTemplate(messageTemplate, userReq);
            if (dto.public) {
              // share message template
              await this.shareMessageTemplate(mt.id);
            }
          } else {
            // system message template
            messageTemplate.accessLevel = TemplateAccessLevel.SYSTEM;
            messageTemplate.systemSequence = dto.ordering ?? undefined;
            await this.createMessageTemplate(messageTemplate, userReq);
          }
        }
        successList.messageTemplateList.push(dto);
      } catch (err) {
        this.logger.error(err, `Failed to`);
        failureList.messageTemplateList.push({
          ...dto,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  // @description Get message template by slug/ groupId
  async getMessageTemplateBySlugOrGroupId(slug: string, groupId?: number) {
    // find message template by slug/ groupId
    return await this.prisma.messageTemplate.findFirst({
      where: {
        slug,
        groupId,
      },
    });
  }
}
