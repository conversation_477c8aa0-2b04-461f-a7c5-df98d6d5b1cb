import { GroupSettingsService } from './group-settings.service';
import { Body, Controller, Get, Param, ParseIntPipe, Post, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Scopes } from '../auth/scope.decorator';
import { EntitySnapshotDto } from '../change-management/dto/entity-snapshot.dto';
import { UserRequest } from '../auth/auth.interface';
import { EntitySnapshot } from '@prisma/client';
import { CreateGroupSettingsSnapshotRequestDto } from './dto/create-entity-snapshot-request.dto';

@Controller('groups/:groupId/group-settings')
@ApiBearerAuth('bearer-auth')
@ApiTags('Group Settings')
export class GroupSettingsController {
  constructor(private readonly groupSettingsService: GroupSettingsService) {}

  @ApiOperation({ summary: 'Obtain the differences in group Settings for different environments' })
  @Get('snapshots/differences')
  @Scopes('group-{groupId}:write-entity-snapshot')
  async getGroupSettingsDifferences(@Param('groupId', ParseIntPipe) groupId: number) {
    return this.groupSettingsService.getGroupSettingsDifferences(groupId);
  }

  @ApiOperation({ summary: 'Create group settings snapshot' })
  @ApiResponse({ type: EntitySnapshotDto })
  @Post('settings/snapshots')
  @Scopes('group-{groupId}:write-entity-snapshot')
  async createGroupSettingsSnapshot(
    @Req() request: UserRequest,
    @Body() dto: CreateGroupSettingsSnapshotRequestDto,
  ): Promise<EntitySnapshot> {
    return this.groupSettingsService.createGroupSettingsSnapshot(
      dto.groupId,
      request.user.id,
      dto.name,
      dto.entityType,
      dto.snapshotType,
    );
  }
}
