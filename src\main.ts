import 'source-map-support/register';
// the import 'source-map-support/register'; must in line 1
import {
  BadRequestException,
  <PERSON><PERSON> as NestLogger,
  ValidationError,
  ValidationPipe,
} from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ExpressAdapter, NestExpressApplication } from '@nestjs/platform-express';
import 'elastic-apm-node/start';
import express from 'express';
import { promises } from 'fs';
import helmet from 'helmet';
import { Logger } from 'nestjs-pino';
import { join } from 'path';
import responseTime from 'response-time';
import { AppModule } from './app.module';
import { loadExternalConfig } from './external-config';
import { initializeSwaggerDoc } from './swagger-document';
import initializeTracing from './tracing';

const tracer = initializeTracing('bot-builder-backend');

async function bootstrap() {
  if (process.env['CONFIG_SERVICE_GET_CONFIG_URL']) {
    await loadExternalConfig();
  }
  const server = express();
  // Configure query parser options
  server.set('query parser', (queryString) => {
    const qs = require('qs');
    return qs.parse(queryString, {
      depth: 100, // Increase depth limit from default 5
      arrayLimit: 100, // Optional: increase array items limit
      parameterLimit: 2000, // Optional: increase parameter count limit
    });
  });
  const app = await NestFactory.create<NestExpressApplication>(
    AppModule /*, {
    logger:
      process.env.NODE_ENV === 'development'
        ? ['log', 'debug', 'error', 'verbose', 'warn']
        : ['error', 'warn'],
  }*/,
    new ExpressAdapter(server),
    { bufferLogs: true },
  );
  const contextPath = process.env['CONTEXT_PATH'] || 'v1';
  app.useLogger(app.get(Logger));

  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: (validationErrors: ValidationError[] = []) => {
        console.error(JSON.stringify(validationErrors));
        return new BadRequestException(validationErrors);
      },
      transform: true,
    }),
  );
  app.enableShutdownHooks();
  app.use(helmet());
  app.enableCors({
    origin: true,
    credentials: true,
    exposedHeaders: ['X-Trace-Id'],
  });
  app.useBodyParser('json', { limit: '10mb' });

  const pkg = JSON.parse(await promises.readFile(join('.', 'package.json'), 'utf8'));
  initializeSwaggerDoc(app);
  app.use(responseTime());
  app.use(express.static('static'));

  process.on('uncaughtException', async (error) => {
    console.error('Uncaught Exception:', error.message);
    const log = new NestLogger('uncaughtException');
    log.error(`uncaughtException ${error.message}`, error.stack);
    await app.close();
  });

  await app.listen(process.env['PORT'] ?? 3000);
}

// eslint-disable-next-line @typescript-eslint/no-floating-promises
bootstrap();
