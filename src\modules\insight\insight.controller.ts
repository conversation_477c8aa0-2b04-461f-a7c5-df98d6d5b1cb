import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Req,
} from '@nestjs/common';
import { InsightService } from './insight.service';
import {
  CreateInsightCaseBySourceSettingDto,
  InsightCaseHistoryArticleUpdateDto,
  navigateInsightCaseHistoryStepDto,
  SyncArticleFromPreviousCaseHistoryDto,
  UpdateInsightCaseBySummarizationSettingDto,
  UpdateInsightCaseHistoryBySourceSettingDto,
} from './insight.dto';
import { Scopes } from '../auth/scope.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Request } from 'express';

@Controller('insights')
export class InsightController {
  logger = new Logger(InsightController.name);
  constructor(private insightService: InsightService) {}

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases')
  async getCases(@Param('groupId', ParseIntPipe) groupId: number, @Req() req: Request) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightService.getInsightCases(groupId, queryStr);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases/search-engines')
  async getSettingSearchEngines(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.insightService.getInsightCaseSearchEngines(groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases/:id/default-llm')
  async getDefaultLLMEngines(@Param('groupId', ParseIntPipe) groupId: number) {
    const defaultLLM = await this.insightService.getInsightCaseDefaultLLMEngine(groupId);
    return { default: defaultLLM };
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/cases/:id/llm-engines')
  async getSettingLLMEngines(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.insightService.getInsightCaseLLMEngines(groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case/:id')
  async getSetting(
    @Param('id', ParseIntPipe) caseId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: Request,
  ) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightService.getInsightCaseById(caseId, groupId, queryStr);
  }

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/case')
  async createInsightCaseBySourceSetting(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createInsightCaseDto: CreateInsightCaseBySourceSettingDto,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;
    return await this.insightService.createInsightCaseBySourceSetting(
      userId,
      groupId,
      createInsightCaseDto,
    );
  }

  @Scopes('group-{groupId}:write-insight')
  @Patch('/:groupId/case/:id/source-setting')
  async updateInsightCase(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) insightId: number,
    @Body() updateInsightCaseDto: UpdateInsightCaseHistoryBySourceSettingDto,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;

    return await this.insightService.updateInsightCaseHistoryBySourceSetting(
      insightId,
      updateInsightCaseDto,
      groupId,
      userId,
    );
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/step')
  async getInsightCaseHistoryStep(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseHistoryStep(caseHistoryId, groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/source-setting')
  async getInsightCaseHistorySourceSetting(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseHistorySourceSetting(caseHistoryId, groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/classification-info')
  async getInsightCaseHistoryClassificationInfo(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseClassificationInfo(caseHistoryId, groupId);
  }

  @Scopes('group-{groupId}:write-insight', 'group-{groupId}:read-insight-advanced')
  @Patch('/:groupId/case-history/:caseHistoryId/step')
  async navigateInsightCaseHistoryStep(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Body() data: navigateInsightCaseHistoryStepDto,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;

    return await this.insightService.navigateInsightCaseHistoryStep(
      caseHistoryId,
      data,
      groupId,
      userId,
    );
  }

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/case-history/:caseHistoryId/article-extraction')
  async generateArticleFromSourceSetting(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;
    return await this.insightService.extractArticlesFromSourceSetting(
      caseHistoryId,
      groupId,
      userId,
    );
  }

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/case-history/:caseHistoryId/article-sync')
  async syncArticleFromPreviousCaseHistory(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Body() data: SyncArticleFromPreviousCaseHistoryDto,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.syncArticleFromPreviousCaseHistory(
      caseHistoryId,
      data.originalCaseHistoryId,
      groupId,
    );
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/article-extraction/tokens')
  async getInsightCaseHistoryArticleExtractionTokenUsage(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseArticleExtractionTokenUsage(
      caseHistoryId,
      groupId,
    );
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/article-extraction/status')
  async getInsightCaseTokenUsage(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseArticleExtractionStatus(caseHistoryId, groupId);
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/articles')
  async getInsightCaseHistoryArticles(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: Request,
  ) {
    const queryStr = req.url.split('?')?.[1] ?? '';

    return await this.insightService.getInsightCaseHistoryArticles(
      caseHistoryId,
      groupId,
      queryStr,
    );
  }

  @Scopes('group-{groupId}:read-insight')
  @Get('/:groupId/case-history/:caseHistoryId/classifications')
  async getInsightCaseHistoryClassifications(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightService.getInsightCaseHistoryClassifications(caseHistoryId, groupId);
  }

  @Scopes('group-{groupId}:write-insight')
  @Patch('/:groupId/case-history/:caseHistoryId/article/:id')
  async updateInsightCaseArticle(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Body() data: InsightCaseHistoryArticleUpdateDto,
  ) {
    return await this.insightService.updateInsightCaseHistoryArticle(
      data,
      id,
      groupId,
      caseHistoryId,
    );
  }

  @Scopes('group-{groupId}:read-insight-advanced')
  @Get('/:groupId/case-history/:caseHistoryId/summarization-setting')
  async getInsightCaseArticleSummarizationSetting(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
  ) {
    return await this.insightService.getInsightCaseHistorySummarizationSetting(
      groupId,
      caseHistoryId,
    );
  }

  @Scopes('group-{groupId}:write-insight')
  @Patch('/:groupId/case-history/:caseHistoryId/summarization-setting')
  async updateInsightCaseHistorySummarizationSetting(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() data: UpdateInsightCaseBySummarizationSettingDto,
    @Req() request: UserRequest,
  ) {
    const userId = request.user.id;
    return await this.insightService.updateInsightCaseHistorySummarizationSetting(
      data,
      caseHistoryId,
      groupId,
      userId,
    );
  }

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/case-history/:caseHistoryId/create-insight-report')
  async createInsightReportFromInsightCaseHistory(
    @Param('caseHistoryId', ParseIntPipe) caseHistoryId: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: UserRequest,
  ) {
    const userId = req.user.id;
    return await this.insightService.createInsightReportFromInsightCaseHistory(
      caseHistoryId,
      groupId,
      userId,
    );
  }
}
