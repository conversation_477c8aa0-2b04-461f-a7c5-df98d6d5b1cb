import { Body, Controller, Headers, HttpCode, Ip, Post, Query, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { VerificationType } from '@prisma/client';
import { TokenResponse, OutLookResponse } from './auth.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { RateLimit } from '../../decorators/rate-limit.decorator';
import { KeycloakTokenResponse } from '../../providers/keycloak/keycloak.service';
import { UsersService } from '../users/users.service';
import {
  ForgetPasswordDto,
  KeycloakExchangeCodeDto,
  LoginDto,
  RefreshTokenDto,
  ResetPasswordDto,
} from './auth.dto';
import { AuthService } from './auth.service';
import { Public } from './public.decorator';
import { Response } from 'express';

@Controller('auth')
@Public()
@ApiTags('Auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
  ) {}

  /** Login to an account */
  @Post('login')
  @HttpCode(200)
  @RateLimit(10)
  async login(
    @Body() data: LoginDto,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
  ): Promise<TokenResponse> {
    return this.authService.login(
      ip,
      userAgent,
      data.email,
      data.loginType,
      data.password,
      data.hktToken,
      data.azureToken,
      data.refreshToken,
    );
  }

  /** Login to an account */
  @Post('login-teams')
  @HttpCode(200)
  @RateLimit(10)
  async loginByTeamsBot(
    @Body() data: LoginDto,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
  ): Promise<TokenResponse> {
    return this.authService.loginByTeamsBot(ip, userAgent, data.email);
  }

  /** Keycloak Token Endpoint Facade (for Authorization Code Flow with PKCE) */
  @Post('token')
  @HttpCode(200)
  @RateLimit(10)
  async keycloakLogin(@Body() data: KeycloakExchangeCodeDto): Promise<KeycloakTokenResponse> {
    return this.authService.loginWithKeycloak(data);
  }

  /** 
  @Post('login/test')
  async loginTest(@Ip() ip: string, @Headers('User-Agent') userAgent: string) {
    return this.authService.loginTest(ip, userAgent);
  }
  */

  @Post('forget-password')
  async forgetPassword(@Body() data: ForgetPasswordDto) {
    await this.authService.forgetPassword(data.email);
    return { email: data.email };
  }

  @Post('validate-code')
  async validateCode(@Query('code') activateCode: string) {
    const isValid = await this.usersService.validateActivateCode(
      activateCode,
      VerificationType.RESET_PASSWORD,
    );
    return {
      isValid,
    };
  }

  @Post('reset-password')
  async resetPassword(@Body() data: ResetPasswordDto) {
    const isValid = await this.usersService.validateActivateCode(
      data.token,
      VerificationType.RESET_PASSWORD,
    );
    if (!isValid) {
      throw new ApiException(ErrorCode.INVALID_ACTIVATION_CODE);
    }
    return await this.authService.resetPassword(data);
  }

  /** Get a new access token using a refresh token */
  @Post('refresh')
  @HttpCode(200)
  @RateLimit(1)
  async refresh(@Ip() ip: string, @Body() data: RefreshTokenDto): Promise<TokenResponse> {
    return this.authService.refresh(data.token, data.groupId);
  }

  /** Logout from a session */
  @Post('logout')
  @HttpCode(200)
  @RateLimit(5)
  async logout(
    @Body('token') refreshToken: string,
    @Res({ passthrough: true }) response: Response,
  ): Promise<{ success: true }> {
    await this.authService.logout(refreshToken, response);
    return { success: true };
  }

  @Post('verify/email')
  @HttpCode(200)
  async verifyUserEmail(
    @Body('userEmail') userEmail: string,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
  ): Promise<OutLookResponse> {
    return await this.authService.userEmailExist(ip, userAgent, userEmail);
  }
}
