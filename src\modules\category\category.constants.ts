export const enum CategoryKey {
  SYSTEM = 'system',
  SYSTEM_ACCOUNT = 'account',
  SYSTEM_ALERT = 'system-alert',
  SYSTEM_ANNOUNCEMENT = 'announcement',
  SYSTEM_BILLING = 'billing',
  SYSTEM_COMMON = 'system-common',
  SYSTEM_DASHBOARD = 'system-dashboard',
  SYSTEM_GROUP_MANAGEMENT = 'group-management',
  SYSTEM_LOGIN = 'login',
  SYSTEM_PLAN_MANAGEMENT = 'plan-management',
  SYSTEM_REPORT = 'report',
  SYSTEM_SECURITY = 'system-security',

  BOT = 'bot',
  BOT_ALERT = 'bot-alert',
  BOT_API_KEY = 'api-key',
  BOT_CALL_CHAT = 'call-chat',
  BOT_CHAT_SETTING = 'chat-setting',
  BOT_CHAT_WITH_DATA = 'chat-with-data',
  BOT_CHAT_WITH_FILE = 'chat-with-file',
  BOT_CONNECT_API = 'connect-api',
  BOT_CUSTOM_ROLE = 'custom-role',
  BOT_DASHBOARD = 'bot-dashboard',
  BOT_GENKB = 'genkb',
  BOT_NOTIFICATION = 'notification',
  BOT_OUTLOOK_ADDIN = 'outlook-addin',
  BOT_RATING_COMMENT = 'rating-comment',
  BOT_SECURITY = 'bot-security',
  BOT_TEST_AUTOMATION = 'test-automation',
  BOT_UPLOAD_DATA = 'upload-data',

  INSIGHT_GENERATOR = 'insight-generator',
  INSIGHT_GENERATOR_COMMON = 'insight-generator-common',
  INSIGHT_GENERATOR_INSTRUCTION_TEMPLATE = 'instruction-template',
  INSIGHT_GENERATOR_INSIGHT_SETTINGS = 'insight-settings',
  INSIGHT_GENERATOR_NOTIFICATION = 'insight-generator-notification',

  FLOW = 'flow',
  FLOW_COMMON = 'flow-common',
  FLOW_PLAYGROUND = 'playground',
}
