import { PrismaService } from 'src/providers/prisma/prisma.service';
import { PlansService } from './plans.service';
import { Test, TestingModule } from '@nestjs/testing';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { PrismaClient, Resource, ResourceSubsciberType } from '@prisma/client';
import { LlmEnginesService } from '../llm-engines/llm-engines.service';
import { SubscribeResourcePlanDto } from './plans.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

const moduleMocker = new ModuleMocker(global);

describe('PlanService', () => {
  let plansService: PlansService;
  let prismaService: DeepMockProxy<{
    // this is needed to resolve the issue with circular types definition
    // https://github.com/prisma/prisma/issues/10203
    [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
  }>;
  let llmEnginesService: DeepMockProxy<LlmEnginesService>;
  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      // this is needed to resolve the issue with circular types definition
      // https://github.com/prisma/prisma/issues/10203
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    llmEnginesService = mockDeep<LlmEnginesService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlansService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
        { provide: LlmEnginesService, useValue: llmEnginesService },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    plansService = module.get(PlansService);
  });

  describe('getResources', () => {
    it('should return resources with isEnabled property', async () => {
      // Mocking
      const subscriberType = ResourceSubsciberType.BOT;
      const subscriberId = 1;
      const subscriberTypes: ResourceSubsciberType[] = [];
      subscriberTypes.push(ResourceSubsciberType.BOT);
      const resources = [
        {
          id: 1,
          subscriberTypes,
          resourceKey: 'llm-engine-gpt-35-turbo-16k',
          resourceName: 'ChatGPT 3.5 Turbo 16K',
          description: 'The quota of using ChatGPT 3.5 Turbo 16K',
          resourceCategoryId: 1,
          resourceEntityType: 'LLM_ENGINE',
          resourceEntityKey: 'gpt-35-turbo-16k',
          permissionId: 1,
          quotaRuleId: 1,
          quotaRule: {
            id: 1,
            quotaType: 'REQUEST',
            quotaCycle: 'MONTHLY',
          },
        },
      ];

      prismaService.resource.findMany.mockResolvedValue(resources as any);
      llmEnginesService.getPlanIsEnabled.mockResolvedValue(true);
      llmEnginesService.getResourceUsage.mockResolvedValue(null);

      // Act
      const result = await plansService.getResources(subscriberType, subscriberId);
      // Assert
      expect(prismaService.resource.findMany).toHaveBeenCalledWith({
        include: {
          quotaRule: true,
          permission: true,
        },
        where: {
          subscriberTypes: { has: subscriberType },
        },
        orderBy: {
          resourceName: 'asc',
        },
      });
      expect(llmEnginesService.getPlanIsEnabled).toHaveBeenCalledTimes(1);
      expect(llmEnginesService.getResourceUsage).toHaveBeenCalledTimes(1);
      expect(result).toEqual([
        {
          id: 1,
          subscriberTypes,
          resourceKey: 'llm-engine-gpt-35-turbo-16k',
          resourceName: 'ChatGPT 3.5 Turbo 16K',
          description: 'The quota of using ChatGPT 3.5 Turbo 16K',
          resourceCategoryId: 1,
          resourceEntityType: 'LLM_ENGINE',
          resourceEntityKey: 'gpt-35-turbo-16k',
          permissionId: 1,
          quotaRuleId: 1,
          isEnabled: true,
          planUsage: null,
          quotaRule: {
            id: 1,
            quotaType: 'REQUEST',
            quotaCycle: 'MONTHLY',
          },
        },
      ]);
    });
  });
  describe('getResourcePlan', () => {
    it('should return resource plan with isEnabled property', async () => {
      const mockPlans = [
        {
          id: 1,
          planKey: 'disable-llm-engine-gpt-35-turbo-16k-plan',
          planName: 'Disable',
          isDefault: false,
          resource: {
            resourceKey: 'llm-engine-gpt-35-turbo-16k',
            resourceEntityType: 'LLM_ENGINE',
            resourceEntityKey: 'gpt-35-turbo-16k',
          },
          description: 'To disable ChatGPT 3.5 Turbo 16K',
          planRoleIdsRequired: [],
          planQuotas: [],
        },
        {
          id: 2,
          planKey: 'llm-engine-gpt-35-turbo-16k-plan-1',
          planName: 'Plan 1',
          isDefault: true,
          resource: {
            resourceKey: 'llm-engine-gpt-35-turbo-16k',
            resourceEntityType: 'LLM_ENGINE',
            resourceEntityKey: 'gpt-35-turbo-16k',
          },
          description: 'Plan 1',
          planRoleIdsRequired: [],
          planQuotas: [],
        },
      ];
      prismaService.plan.findMany.mockResolvedValue(mockPlans as any);
      prismaService.planSubscription.count.mockResolvedValue(0);
      llmEnginesService.getPlanIsEnabled.mockResolvedValue(true);
      // Act
      const result = await plansService.getResourcePlans({
        subscriberType: ResourceSubsciberType.BOT,
        subscriberId: 1,
      });
      // Assert
      expect(prismaService.plan.findMany).toHaveBeenCalledTimes(1);
      expect(prismaService.planSubscription.count).toHaveBeenCalledTimes(2);
      expect(prismaService.planSubscription.findFirst).toHaveBeenCalledTimes(0);
      expect(llmEnginesService.getPlanIsEnabled).toHaveBeenCalledTimes(2);
      expect(result).toEqual([
        {
          id: 1,
          planKey: 'disable-llm-engine-gpt-35-turbo-16k-plan',
          planName: 'Disable',
          isDefault: false,
          resource: {
            resourceKey: 'llm-engine-gpt-35-turbo-16k',
            resourceEntityType: 'LLM_ENGINE',
            resourceEntityKey: 'gpt-35-turbo-16k',
          },
          description: 'To disable ChatGPT 3.5 Turbo 16K',
          planRoleIdsRequired: [],
          isSubscribed: false,
          isResourceEnabled: true,
          isCustomPlan: false,
          planQuotas: [],
        },
        {
          id: 2,
          planKey: 'llm-engine-gpt-35-turbo-16k-plan-1',
          planName: 'Plan 1',
          isDefault: true,
          resource: {
            resourceKey: 'llm-engine-gpt-35-turbo-16k',
            resourceEntityType: 'LLM_ENGINE',
            resourceEntityKey: 'gpt-35-turbo-16k',
          },
          description: 'Plan 1',
          planRoleIdsRequired: [],
          isSubscribed: true,
          isResourceEnabled: true,
          isCustomPlan: false,
          planQuotas: [],
        },
      ]);
    });
  });
  describe('subscribeResourcePlans', () => {
    it('should subscribe to resource plans', async () => {
      // Mocking
      const data: SubscribeResourcePlanDto = {
        subscribedPlanIds: [1, 2, 3],
        subscriberId: 1,
        subscriberType: ResourceSubsciberType.BOT,
      };
      const resources = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const plans = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const mockResult = [
        {
          planId: 1,
          subscriberEntityType: ResourceSubsciberType.BOT,
          subscriberEntityId: 1,
          subscribeStartDate: new Date(),
          subscribeEndDate: null,
        },
        {
          planId: 2,
          subscriberEntityType: ResourceSubsciberType.BOT,
          subscriberEntityId: 1,
          subscribeStartDate: new Date(),
          subscribeEndDate: null,
        },
        {
          planId: 3,
          subscriberEntityType: ResourceSubsciberType.BOT,
          subscriberEntityId: 1,
          subscribeStartDate: new Date(),
          subscribeEndDate: null,
        },
      ];
      prismaService.resource.findMany.mockResolvedValue(resources as any[]);
      prismaService.plan.findMany.mockResolvedValue(plans as any[]);

      (prismaService.$transaction as jest.Mock).mockImplementation(async (callback) => {
        await callback(prismaService);
        return mockResult;
      });
      jest.spyOn(plansService, 'validateSubmittedPlanIds').mockResolvedValue(true);

      prismaService.planSubscription.createMany.mockResolvedValue({ data: mockResult } as any);
      // Act
      const result = await plansService.subscribeResourcePlans(data);
      // Assert
      expect(result).toEqual(mockResult);
      expect(prismaService.resource.findMany).toHaveBeenCalledTimes(1);
      expect(prismaService.plan.findMany).toHaveBeenCalledTimes(1);
      expect(prismaService.planSubscription.createMany).toHaveBeenCalledTimes(1);
      expect(prismaService.planSubscription.updateMany).toHaveBeenCalledTimes(1);
    });

    it('should failed to subscribe due failure of validation', async () => {
      // Mocking
      const data: SubscribeResourcePlanDto = {
        subscribedPlanIds: [1, 2, 3],
        subscriberId: 1,
        subscriberType: ResourceSubsciberType.BOT,
      };
      const resources = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const plans = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const mockResult = [
        {
          planId: 1,
          subscriberEntityType: ResourceSubsciberType.BOT,
          subscriberEntityId: 1,
          subscribeStartDate: new Date(),
          subscribeEndDate: null,
        },
        {
          planId: 2,
          subscriberEntityType: ResourceSubsciberType.BOT,
          subscriberEntityId: 1,
          subscribeStartDate: new Date(),
          subscribeEndDate: null,
        },
        {
          planId: 3,
          subscriberEntityType: ResourceSubsciberType.BOT,
          subscriberEntityId: 1,
          subscribeStartDate: new Date(),
          subscribeEndDate: null,
        },
      ];
      prismaService.resource.findMany.mockResolvedValue(resources as any[]);
      prismaService.plan.findMany.mockResolvedValue(plans as any[]);

      (prismaService.$transaction as jest.Mock).mockImplementation(async (callback) => {
        await callback(prismaService);
        return mockResult;
      });
      jest.spyOn(plansService, 'validateSubmittedPlanIds').mockResolvedValue(false);

      prismaService.planSubscription.createMany.mockResolvedValue({ data: mockResult } as any);
      await expect(plansService.subscribeResourcePlans(data)).rejects.toEqual(
        new ApiException(ErrorCode.INVALID_PLAN),
      );
    });
  });

  describe('validateSubmittedPlans', () => {
    it('success case', async () => {
      prismaService.plan.findMany.mockResolvedValue([
        {
          id: 1,
          resourceId: 1,
          isActive: true,
        },
        {
          id: 2,
          resourceId: 2,
          isActive: true,
        },
      ] as any);
      const result = await plansService.validateSubmittedPlanIds([1, 2]);
      expect(result).toBe(true);
    });
    it('failure case', async () => {
      prismaService.plan.findMany.mockResolvedValue([
        {
          id: 1,
          resourceId: 1,
          isActive: true,
        },
        {
          id: 2,
          resourceId: 1,
          isActive: true,
        },
      ] as any);
      const result = await plansService.validateSubmittedPlanIds([1, 2]);
      expect(result).toBe(false);
    });
  });
});
