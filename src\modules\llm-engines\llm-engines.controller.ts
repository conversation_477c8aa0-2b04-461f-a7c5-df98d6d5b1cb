import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Logger,
  UsePipes,
  ValidationPipe,
  Req,
  HttpCode,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import { LlmEnginesService } from './llm-engines.service';
import { BatchUpdateDto, CreateLlmEngineDto, UpdateLlmEngineDto } from './llm-engines.dto';
import { Public } from '../auth/public.decorator';
import { Scopes } from '../auth/scope.decorator';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserRequest } from '../auth/auth.interface';
import { FileInterceptor } from '@nestjs/platform-express';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { validate } from 'class-validator';
import { Response } from 'express';
import { Readable } from 'stream';

@Controller('llm-engines')
@ApiBearerAuth('bearer-auth')
@ApiTags('LLM Engines')
export class LlmEnginesController {
  logger = new Logger(LlmEnginesController.name);
  constructor(private readonly llmEnginesService: LlmEnginesService) {}

  @Get()
  @Public()
  async findAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('includesInactive') includesInactive?: boolean,
  ) {
    const list = await this.llmEnginesService.findAll({
      includesInactive,
      skip,
      take,
      orderBy: {
        sequence: 'asc',
      },

      where,
    });
    const count = await this.llmEnginesService.count({ includesInactive, where });
    return {
      list,
      count,
    };
  }

  @Post()
  @Scopes('system:write-llm-engine')
  create(@Body() createLlmEngineDto: CreateLlmEngineDto) {
    return this.llmEnginesService.create(createLlmEngineDto);
  }

  @Patch('/:engineId')
  @Scopes('system:write-llm-engine')
  @UseInterceptors(FileInterceptor('file'))
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @UploadedFile() file: Express.Multer.File,
    @Body() data: UpdateLlmEngineDto,
    @Param('engineId', ParseIntPipe) engineId: number,
    @Req() request: UserRequest,
  ) {
    try {
      const icon = await this.llmEnginesService.uploadIcon(engineId, file, data);
      data.iconPictureUrl = icon.iconPictureUrl;
      return this.llmEnginesService.update(engineId, data, request);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  @Post('/:groupId/checkTokenLimit')
  @Scopes('group-*:token-limit-checking')
  @HttpCode(200)
  async checkTokenLimit(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() requestBody: { llmEngines: string[] },
  ) {
    await this.llmEnginesService.checkChatTokenLimit(groupId, requestBody.llmEngines);
  }

  @Patch('batch/update')
  @AuditLog('batch-update-llm-engine')
  @Scopes('system:write-llm-engine')
  async batchUpdate(@Req() req: UserRequest, @Body() batchData: BatchUpdateDto) {
    const { llmEngines } = batchData;
    const successList = [];
    const failureList = [];
    for (const llmEngine of llmEngines) {
      try {
        const errors = await validate(llmEngine);
        if (errors.length > 0) {
          const constraints = errors[0].constraints || {};
          const errorMsg = Object.values(constraints).join(', ');
          failureList.push({
            ...llmEngine,
            errMsg:
              errorMsg ?? 'Internal Server Error. Please try to fix input and re-upload again.',
          });
        } else {
          // Call the new service method that handles the full update for one engine, including labels/categories
          await this.llmEnginesService.applyBatchEngineUpdate(llmEngine, req);
          // On success, push the original input item to the successList
          successList.push(llmEngine);
        }
      } catch (err) {
        this.logger.error(err, `Failed to update llmEngine ${llmEngine.slug}`);
        failureList.push({
          ...llmEngine,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return {
      successList,
      failureList,
    };
  }

  @Get('batch/template')
  @Scopes('system:write-llm-engine')
  async getTemplate(@Res() res: Response) {
    const jsonData = await this.llmEnginesService.getLlmEnginesAsJsonTemplate();
    const jsonString = JSON.stringify(jsonData, null, 2);
    const stream = Readable.from(jsonString);
    stream.pipe(res);
  }

  @Get('platforms')
  @Public()
  async findAllPlatform() {
    const list = await this.llmEnginesService.findAllPlatform();
    return { list };
  }
}
