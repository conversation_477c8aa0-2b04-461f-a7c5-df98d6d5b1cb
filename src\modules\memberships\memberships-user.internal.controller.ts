import { Controller, Get, Logger, Query } from '@nestjs/common';
import { Public } from '../auth/public.decorator';
import { MembershipsService } from './memberships.service';
import { InitialUserRequestDto, InitialUserResponse } from './memberships.dto';
import { AppWhitelistService } from '../app-whitelist/app-whitelist.service';

@Controller('internal/users')
export class UserMembershipControllerInternalController {
  private readonly logger = new Logger(UserMembershipControllerInternalController.name);

  constructor(
    private readonly membershipsService: MembershipsService,
    private readonly appWhitelistService: AppWhitelistService,
  ) {}

  @Get('initial-info')
  @Public()
  async getInitialUserInfo(@Query() data: InitialUserRequestDto): Promise<InitialUserResponse> {
    const roles = await this.membershipsService.getGroupRolesByEmail(data.email);
    const whitelists = await this.appWhitelistService.getWhitelistsByEmail(data.email);
    this.logger.log(`Determined roles for ${data.email}: ${roles.join(', ')}`);
    return { status: 200, data: { roles, whitelists: whitelists } };
  }
}
