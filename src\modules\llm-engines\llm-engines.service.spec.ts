import { Test, TestingModule } from '@nestjs/testing';
import { Environment, LlmEngine, PrismaClient } from '@prisma/client';
import { MockFunctionMetadata, ModuleMocker } from 'jest-mock';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';
import { CreateLlmEngineDto, UpdateLlmEngineDto } from './llm-engines.dto';
import { LlmEnginesService } from './llm-engines.service';
import { LabelsService } from '../labels/labels.service';

const moduleMocker = new ModuleMocker(global);

describe('LlmEnginesService', () => {
  let llmEnginesService: LlmEnginesService;
  let prismaService: DeepMockProxy<{ [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'> }>;
  let labelsService: DeepMockProxy<LabelsService>;

  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    labelsService = mockDeep<LabelsService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmEnginesService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
        {
          provide: LabelsService,
          useValue: labelsService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    llmEnginesService = module.get(LlmEnginesService);
    labelsService.formatLabelsAndCategoriesFilter.mockReturnValue({});
    labelsService.getLabelsPrismaQuery.mockReturnValue({
      EntityLabels: {
        where: { LabelEntityType: 'LLM_ENGINE' },
        orderBy: { seq: 'asc' },
        include: {
          Labels: {
            select: {
              id: true,
              name: true,
              color: true,
              desc: true,
              labelType: true,
            },
          },
        },
      },
    });
    labelsService.formatLabelsAndCategoriesData.mockImplementation((d) => d);
  });

  describe('findAll', () => {
    it('should return llm Engine list', async () => {
      const llmEngineList = [
        {
          id: 1,
          name: 'ChatGPT 4 128K@0125',
          slug: 'gpt-4-0125',
        },
        {
          id: 2,
          name: 'Chat Bison 32K@Latest',
          slug: 'vertexai-chat-bison-32k',
        },
      ] as LlmEngine[];
      prismaService.llmEngine.findMany.mockResolvedValue(llmEngineList);
      jest.spyOn(llmEnginesService, 'getMostlyUsedLLMnEngines').mockResolvedValue([]);
      jest.spyOn(llmEnginesService, 'getLLMEnginesUsedNumbers').mockResolvedValue([]);
      const res = await llmEnginesService.findAll({});
      expect(res).toEqual(
        llmEngineList.map((i) => ({
          ...i,
          isMostlyUsed: false,
          isNew: false,
          used: 0,
          configHide: undefined,
        })),
      );
    });
  });

  describe('count', () => {
    it('should return llm Engine count', async () => {
      const llmEngineCount = 10;
      prismaService.llmEngine.count.mockResolvedValue(llmEngineCount);
      const res = await llmEnginesService.count({ includesInactive: true });
      expect(res).toEqual(llmEngineCount);
    });
  });

  describe('create', () => {
    it('should return llm Engine', async () => {
      const req = {
        name: 'test',
        slug: 'slug-test',
        reportSlug: 'reportSlug-test',
      } as CreateLlmEngineDto;

      const llmEngine = {
        id: 1,
        slug: 'gpt-4-0125',
      } as LlmEngine;
      prismaService.llmEngine.create.mockResolvedValue(llmEngine);
      const res = await llmEnginesService.create(req);
      expect(res).toEqual(llmEngine);
      expect(prismaService.llmEngine.create).toHaveBeenCalledWith({ data: req });
    });
  });

  describe('update', () => {
    it('should return llm Engine', async () => {
      const req = {
        name: 'test',
        slug: 'slug-test',
        reportSlug: 'reportSlug-test',
      } as UpdateLlmEngineDto;

      const userReq = {
        user: { id: 1 },
      };

      const llmEngine = {
        id: 1,
        slug: 'gpt-4-0125',
      } as LlmEngine;
      labelsService.formatPatchLabelsDto.mockReturnValue({
        needRemoveIds: [],
        createEntityLabels: [],
        createLabels: [],
      });
      (prismaService.$transaction as jest.Mock).mockImplementation(async (callback) => {
        return await callback(prismaService);
      });
      prismaService.llmEngine.update.mockResolvedValue(llmEngine);
      const res = await llmEnginesService.update(1, req, userReq as UserRequest);
      expect(res).toEqual(llmEngine);
      expect(prismaService.llmEngine.update).toHaveBeenCalledWith({
        include: expect.any(Object),
        where: { id: 1 },
        data: { ...req, entityLabels: undefined },
      });
    });
  });

  describe('findDefaultLltmEngine', () => {
    it('should return default llm Engine when the found one is inactive', async () => {
      const inactiveLlmEngine = {
        id: 1,
        slug: 'gpt-4-0125',
        isActive: false,
      } as LlmEngine;
      const defaultLlmEngine = {
        id: 2,
        slug: 'default-engine',
        isActive: true,
      } as LlmEngine;
      prismaService.llmEngine.findFirst
        .mockResolvedValueOnce(inactiveLlmEngine)
        .mockResolvedValueOnce(defaultLlmEngine);
      const res = await llmEnginesService.findDefaultLltmEngine('slug-test');
      expect(res).toEqual(defaultLlmEngine);
      expect(prismaService.llmEngine.findFirst).toHaveBeenCalledTimes(2);
      expect(prismaService.llmEngine.findFirst).toHaveBeenCalledWith({
        where: { slug: 'slug-test' },
      });
      expect(prismaService.llmEngine.findFirst).toHaveBeenCalledWith({
        where: {
          isActive: true,
        },
        orderBy: { sequence: 'asc' },
      });
    });
  });
});
