import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  DataPromotionRequest,
  DataPromotionRequestActionType,
  DataPromotionRequestStatus,
  EntitySnapshot,
  Environment,
  Prisma,
  PrismaClient,
  SnapshotEntityType,
  SystemName,
} from '@prisma/client';

import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ApiKeysService } from '../api-keys/api-keys.service';
import { ApiResourceService } from '../api-resource/api-resource.service';
import { FlowsService } from '../flows/flows.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { UpdateDataPromotionRequestDto } from './dto/update-data-promotion-request.dto';
import { EntityPromotionHandler } from './interfaces/entity-promotion-handler.interface';
import { EntitySnapshotStatus } from './dto/create-data-deletion-request.dto';
import { BotSecurityService } from '../bot-security/bot-security.service';
import { LLMModelsSettingService } from '../llm-models/llm-models-setting.service';
import { LLMModelsParametersService } from '../llm-models/llm-models-parameters.service';
import { GroupSettingsService } from '../group-settings/group-settings.service';

@Injectable()
export class ChangeManagementService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly apiKeysService: ApiKeysService,
    private readonly llmModelsService: LLMModelsService,
    private readonly flowsService: FlowsService,
    private readonly apiResourceService: ApiResourceService,
    private readonly botSecurityService: BotSecurityService,
    private readonly llmModelsSettingService: LLMModelsSettingService,
    private readonly llmModelsParametersService: LLMModelsParametersService,
    private readonly groupSettingsService: GroupSettingsService,
  ) {
    const defaultToDisplayEntityData = async (
      tx: Prisma.TransactionClient,
      entityData: any,
    ): Promise<object> => {
      // no changes
      return entityData;
    };

    // config promotable each snapshot entity type
    this.entityPromotionHandler = {
      API_KEY: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: apiKeysService,
      },
      LLM_MODEL: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: llmModelsService,
      },
      FLOW: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: flowsService,
      },
      API_RESOURCE: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: apiResourceService,
      },
      BOT_SECURITY: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: botSecurityService,
      },
      LLM_MODEL_SETTING: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: llmModelsSettingService,
      },
      GROUP_SETTINGS: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: groupSettingsService,
      },
      LLM_MODEL_PARAMETERS: {
        toDisplayEntityData: defaultToDisplayEntityData,
        service: llmModelsParametersService,
      },
    };
  }

  private readonly entityPromotionHandler: EntityPromotionHandler;

  private readonly userSelect: Prisma.UserSelect = {
    id: true,
    name: true,
  };

  private readonly groupSelect: Prisma.GroupSelect = {
    id: true,
    name: true,
    env: true,
    pairId: true,
  };

  // return transformed entityData for display purpose
  private async getDisplayEntityData(tx: Prisma.TransactionClient, snapshot: EntitySnapshot) {
    return await this.entityPromotionHandler[snapshot.entityType].toDisplayEntityData(
      tx,
      snapshot.entityData,
    );
  }

  async searchEntitySnapshots(
    latestOnly: boolean, // for only search the latest snapshot version
    skip: number,
    take: number,
    orderBy: Prisma.EntitySnapshotOrderByWithRelationInput,
    groupId?: number,
    entityType?: Prisma.EnumSnapshotEntityTypeFilter | SnapshotEntityType,
    entityId?: Prisma.StringFilter | string,
  ) {
    // to find latest snapshot only, distinct by entityType & entityId
    const distinct: Prisma.Enumerable<Prisma.EntitySnapshotScalarFieldEnum> = latestOnly
      ? ['entityType', 'entityId']
      : undefined;

    // where clause for both count & findMany query
    const where: Prisma.EntitySnapshotWhereInput = {
      entityType,
      entityId,
      groupId,
    };

    return this.prisma.$transaction(async (tx) => {
      let snapshotCount: number;
      if (distinct) {
        // prisma .count does not support distinct yet
        // https://github.com/prisma/prisma/issues/4228
        // expensive workaround:
        snapshotCount = (
          await tx.entitySnapshot.findMany({
            distinct,
            where,
          })
        ).length;
      } else {
        snapshotCount = await tx.entitySnapshot.count({
          where,
        });
      }

      if (snapshotCount === 0) return { list: [], count: snapshotCount };

      const snapshots = await tx.entitySnapshot.findMany({
        select: {
          // select related pending requests in the response (max 1 pending request per env)
          dataPromotionRequests: {
            where: {
              status: DataPromotionRequestStatus.PENDING,
            },
            include: {
              requester: {
                select: this.userSelect,
              },
              operator: {
                select: this.userSelect,
              },
            },
          },
          group: {
            select: this.groupSelect,
          },
          createdBy: {
            select: this.userSelect,
          },
          id: true,
          name: true,
          entityType: true,
          entityId: true,
          entityData: true,
          versionDate: true,
          groupId: true,
          createdByUserId: true,
        },
        distinct,
        skip,
        take,
        // if latestOnly, need to force order by versionDate desc
        orderBy: latestOnly
          ? {
              versionDate: 'desc',
            }
          : orderBy,
        where,
      });

      // concurrently transform entityData in snapshots to readable data
      const setDisplayEntityDataPromises = snapshots.map(async (snapshot) => {
        const latestRequest = await this.prisma.dataPromotionRequest.findFirst({
          where: {
            entitySnapshotId: snapshot.id,
          },
          orderBy: { requestedDate: 'desc' },
        });
        snapshot.entityData = await this.getDisplayEntityData(tx, snapshot);
        return {
          ...snapshot,
          status: this.getSnapshotStatusByLatestRequestStatus(latestRequest?.status ?? null),
        };
      });
      const list = await Promise.all(setDisplayEntityDataPromises);
      return { list, count: snapshotCount };
    });
  }

  private getSnapshotStatusByLatestRequestStatus = (
    requestStatus: DataPromotionRequestStatus | null,
  ) => {
    switch (requestStatus) {
      case DataPromotionRequestStatus.PENDING:
        return EntitySnapshotStatus.PENDING;
      case DataPromotionRequestStatus.APPROVED:
        return EntitySnapshotStatus.APPROVED;
      case DataPromotionRequestStatus.CANCELED:
        return EntitySnapshotStatus.CANCELED;
      case DataPromotionRequestStatus.REJECTED:
        return EntitySnapshotStatus.REJECTED;
      default:
        return EntitySnapshotStatus.TODO;
    }
  };

  async searchDataPromotionRequests(
    skip: number,
    take: number,
    orderBy: Prisma.DataPromotionRequestOrderByWithRelationInput,
    where?: Record<string, number | string>,
    groupId?: number,
  ) {
    // where clause for both count & findMany query
    const newWhere: Prisma.DataPromotionRequestWhereInput = {
      id: where?.['id'] as number,
      status: where?.['status'] as DataPromotionRequestStatus,
      actionType: where?.['actionType'] as DataPromotionRequestActionType,
      entitySnapshot: {
        name: where?.['snapshotName'] as string,
        entityType: where?.['entityType'] as SnapshotEntityType,
        entityId: where?.['entityId'] as string,
        groupId,
        group: {
          name: where?.['groupName'] as string,
        },
      },
    };
    return this.prisma.$transaction(async (tx) => {
      const dataPromotionRequestCount = await tx.dataPromotionRequest.count({
        where: newWhere,
      });
      if (dataPromotionRequestCount === 0) return { list: [], count: dataPromotionRequestCount };

      const dataPromotionRequests = await tx.dataPromotionRequest.findMany({
        skip,
        take,
        orderBy,
        where: newWhere,
        include: {
          entitySnapshot: {
            include: {
              group: {
                select: this.groupSelect,
              },
              createdBy: {
                select: this.userSelect,
              },
            },
          },
          requester: {
            select: this.userSelect,
          },
          operator: {
            select: this.userSelect,
          },
        },
      });

      // concurrently transform entityData in snapshot of all returning requests to readable data
      const setDisplayEntityDataPromises = dataPromotionRequests.map(
        async (dataPromotionRequest) => {
          dataPromotionRequest.entitySnapshot.entityData = await this.getDisplayEntityData(
            tx,
            dataPromotionRequest.entitySnapshot,
          );
        },
      );
      await Promise.all(setDisplayEntityDataPromises);

      return {
        list: dataPromotionRequests,
        count: dataPromotionRequestCount,
      };
    });
  }

  async findSnapshotByIdAndGroupId(id: number, groupId: number) {
    return this.prisma.$transaction(async (tx) => {
      const snapshot = await tx.entitySnapshot.findFirst({
        where: {
          id,
          groupId,
        },
        include: {
          _count: {
            select: {
              // for counting total number of related requests
              dataPromotionRequests: true,
            },
          },
          dataPromotionRequests: {
            where: {
              status: DataPromotionRequestStatus.PENDING,
            },
            include: {
              requester: {
                select: this.userSelect,
              },
              operator: {
                select: this.userSelect,
              },
            },
          },
          group: {
            select: this.groupSelect,
          },
          createdBy: {
            select: this.userSelect,
          },
        },
      });
      if (!snapshot) throw new ApiException(ErrorCode.ENTITY_SNAPSHOT_NOT_FOUND);
      snapshot.entityData = await this.getDisplayEntityData(tx, snapshot);
      const latestRequest = await this.prisma.dataPromotionRequest.findFirst({
        where: {
          entitySnapshotId: snapshot.id,
        },
        orderBy: { requestedDate: 'desc' },
      });
      return {
        ...snapshot,
        status: this.getSnapshotStatusByLatestRequestStatus(latestRequest?.status ?? null),
      };
    });
  }

  async findVersionsByEntityTypeAndEntityIdAndGroupId(
    entityType: Prisma.EnumSnapshotEntityTypeFilter | SnapshotEntityType,
    entityId: Prisma.StringFilter | string,
    groupId: number,
    skip?: number,
    take?: number,
    equals?: string,
    lt?: string,
    lte?: string,
    gt?: string,
    gte?: string,
  ) {
    const snapshots = await this.prisma.entitySnapshot.findMany({
      skip,
      take,
      select: {
        id: true,
        name: true,
        versionDate: true,
        createdBy: {
          select: this.userSelect,
        },
        dataPromotionRequests: {
          orderBy: { requestedDate: 'desc' },
          take: 1,
        },
      },
      where: {
        entityType,
        entityId,
        groupId,
        versionDate: {
          equals,
          lt,
          lte,
          gt,
          gte,
        },
      },
      orderBy: {
        versionDate: 'desc',
      },
    });
    const setDisplayEntityDataPromises = snapshots.map(async (snapshot) => {
      return {
        ...snapshot,
        status: this.getSnapshotStatusByLatestRequestStatus(
          snapshot.dataPromotionRequests?.[0]?.status ?? null,
        ),
      };
    });
    const list = await Promise.all(setDisplayEntityDataPromises);
    return list;
  }

  async createEntitySnapshot(
    groupId: number,
    createdByUserId: number,
    name: string,
    entityType: SnapshotEntityType,
    entityId: string,
  ) {
    const entityData = await this.entityPromotionHandler[
      entityType
    ].service.generateEntityDataForSnapshot(groupId, entityId);

    return await this.prisma.entitySnapshot.create({
      data: {
        group: {
          connect: {
            id: groupId,
          },
        },
        createdBy: {
          connect: {
            id: createdByUserId,
          },
        },
        name,
        entityType,
        entityId,
        entityData,
        versionDate: new Date(),
      },
    });
  }

  async deleteEntitySnapshot(groupId: number, id: number) {
    return this.prisma.$transaction(async (tx) => {
      const entitySnapshot = await tx.entitySnapshot.findFirst({
        where: {
          id,
          groupId,
          dataPromotionRequests: {
            none: {}, // cannot delete snapshot with promotion requests
          },
        },
      });
      if (!entitySnapshot) {
        // if no snapshot match, throw exception
        throw new ApiException(ErrorCode.ENTITY_SNAPSHOT_DELETE_NOT_FOUND);
      }
      await this.entityPromotionHandler[
        entitySnapshot.entityType
      ].service.deleteEntityDataForSnapshot(
        groupId,
        entitySnapshot.entityId,
        entitySnapshot.entityData,
      );
      return await tx.entitySnapshot.delete({ where: { id } });
    });
  }

  private async checkIsSameRequestAlreadyExistsAndThrow(
    tx: Prisma.TransactionClient,
    targetGroupId: number,
    actionType: DataPromotionRequestActionType,
    snapshotId?: number, // for PROMOTE actionType
    entityType?: SnapshotEntityType, // for DELETE actionType
    entityId?: string, // for DELETE actionType
  ) {
    const sameRequestCount = await tx.dataPromotionRequest.count({
      where: {
        entitySnapshot: {
          id: snapshotId,
          entityType,
          entityId,
        },
        targetGroupId,
        status: DataPromotionRequestStatus.PENDING,
        actionType,
      },
    });
    if (sameRequestCount !== 0) {
      // if same request already exists, throw exception
      throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_ALREADY_EXISTS);
    }
  }

  private async findLatestApprovedRequestByEnvAndEntityTypeAndEntityId(
    tx: Prisma.TransactionClient,
    targetEnv: Environment,
    entityType: SnapshotEntityType,
    entityId: string,
  ) {
    return await tx.dataPromotionRequest.findFirst({
      include: {
        entitySnapshot: {
          select: {
            id: true,
            group: {
              select: this.groupSelect,
            },
          },
        },
      },
      where: {
        status: DataPromotionRequestStatus.APPROVED,
        targetGroup: {
          env: targetEnv,
        },
        entitySnapshot: {
          entityType,
          entityId,
        },
      },
      orderBy: {
        operatedDate: 'desc',
      },
    });
  }

  async createDataPromotionRequest(
    groupId: number,
    requesterId: number,
    targetEnv: Environment,
    snapshotId: number,
    requesterComment?: string,
  ) {
    return this.prisma.$transaction(async (tx) => {
      const snapshot = await tx.entitySnapshot.findFirst({
        where: {
          id: snapshotId,
          groupId,
        },
        include: {
          group: {
            select: this.groupSelect,
          },
        },
      });
      if (!snapshot) {
        // if snapshot not exists, throw snapshot not found exception
        throw new ApiException(ErrorCode.ENTITY_SNAPSHOT_NOT_FOUND);
      }

      if (targetEnv === snapshot.group.env || snapshot.group.env === 'PROD') {
        // if promoting to same env or promoting form PROD, throw exception
        throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_INVALID_ENV);
      }

      // find the groupId to promote
      // assume the targetGroup wouldn't be hard deleted afterwards
      const targetGroup = await tx.group.findFirst({
        where: {
          pairId: snapshot.group.pairId,
          env: targetEnv,
        },
        select: {
          id: true,
        },
      });

      if (!targetGroup) {
        // if targetGroup not exists, throw exception
        throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
      }

      await this.checkIsSameRequestAlreadyExistsAndThrow(
        tx,
        targetGroup.id,
        DataPromotionRequestActionType.PROMOTE,
        snapshotId,
      );

      // create promotion request
      return await tx.dataPromotionRequest.create({
        data: {
          actionType: DataPromotionRequestActionType.PROMOTE,
          targetGroupId: targetGroup.id,
          entitySnapshotId: snapshotId,
          status: DataPromotionRequestStatus.PENDING,
          requesterId,
          requesterComment,
        },
      });
    });
  }

  async createDataDeletionRequest(
    groupId: number,
    requesterId: number,
    targetEnv: Environment,
    entityType: SnapshotEntityType,
    entityId: string,
    requesterComment?: string,
  ) {
    return this.prisma.$transaction(async (tx) => {
      const latestApprovedRequest =
        await this.findLatestApprovedRequestByEnvAndEntityTypeAndEntityId(
          tx,
          targetEnv,
          entityType,
          entityId,
        );

      if (!latestApprovedRequest) {
        // if no request, this entity is not promoted before, throw exception
        throw new ApiException(ErrorCode.ENTITY_NOT_PROMOTED);
      }
      if (groupId != latestApprovedRequest.entitySnapshot.group.id) {
        // if entity group id not equals to request groupId throw exception
        throw new ApiException(ErrorCode.ENTITY_NOT_FOUND);
      }
      if (latestApprovedRequest.actionType === 'DELETE') {
        // if entity is already deleted, throw exception
        throw new ApiException(ErrorCode.ENTITY_ALREADY_DELETED);
      }

      await this.checkIsSameRequestAlreadyExistsAndThrow(
        tx,
        latestApprovedRequest.targetGroupId,
        DataPromotionRequestActionType.DELETE,
        // DELETE action type doesn't care snapshotId, only check entityType & entityId
        undefined,
        entityType,
        entityId,
      );

      return await tx.dataPromotionRequest.create({
        data: {
          actionType: DataPromotionRequestActionType.DELETE,
          targetGroupId: latestApprovedRequest.targetGroupId,
          entitySnapshotId: latestApprovedRequest.entitySnapshot.id,
          status: DataPromotionRequestStatus.PENDING,
          requesterId,
          requesterComment,
        },
      });
    });
  }

  async approveDataPromotionRequest(
    operatorId: number,
    requestId: number,
    operatorComment?: string,
  ): Promise<DataPromotionRequest> {
    return this.prisma.$transaction(
      async (tx) => {
        const request = await tx.dataPromotionRequest.findFirst({
          where: { id: requestId, status: DataPromotionRequestStatus.PENDING },
          include: {
            targetGroup: true,
            entitySnapshot: true,
            requester: true,
          },
        });
        const requesterRole = await tx.role.findUnique({
          where: {
            id: request.requester.roleId,
          },
        });
        if (!request) {
          // if request not found, throw exception
          throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_NOT_FOUND);
        }

        if (request.requesterId === operatorId && requesterRole.systemName !== SystemName.SUDO) {
          // if approving own request but not SUDO, throw exception
          throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_SELF_APPROVAL);
        }

        // find the latest approved request of the same entityType, entityId and target env
        // and use the promotedEntityId & actionType to identify whether to
        // 1. create new entity: the entity is not promoted before or deleted after promotion
        // 2. update/delete existing entity: the entity is already promoted before & not deleted
        const latestApprovedRequest =
          await this.findLatestApprovedRequestByEnvAndEntityTypeAndEntityId(
            tx,
            request.targetGroup.env,
            request.entitySnapshot.entityType,
            request.entitySnapshot.entityId,
          );

        const operatedDate = new Date();

        const targetEntityId =
          latestApprovedRequest && latestApprovedRequest.actionType !== 'DELETE'
            ? (await this.entityPromotionHandler[
                request.entitySnapshot.entityType
              ].service.checkPromotedEntityValid(latestApprovedRequest.promotedEntityId))
              ? latestApprovedRequest.promotedEntityId
              : null
            : null;

        // approve the request
        const approvedRequest = await tx.dataPromotionRequest.update({
          data: {
            promotedEntityId: targetEntityId,
            status: DataPromotionRequestStatus.APPROVED,
            operatorId,
            operatedDate,
            operatorComment,
          },
          where: { id: requestId },
        });

        // assume promotedModelId is always under targetGroupId, no checking for ownership

        if (!targetEntityId) {
          // not promoted before, create
          const promotedEntityId = await this.entityPromotionHandler[
            request.entitySnapshot.entityType
          ].service.promoteCreate(
            tx,
            request.targetGroup,
            request.entitySnapshot.entityData,
            operatorId,
          );
          // update promotedEntityId for create case
          await tx.dataPromotionRequest.update({
            data: { promotedEntityId },
            where: { id: requestId },
          });
        } else if (request.actionType === 'DELETE') {
          // promoted before, actionType is DELETE, delete
          await this.entityPromotionHandler[
            request.entitySnapshot.entityType
          ].service.promoteDelete(tx, request.targetGroup, targetEntityId, operatorId);
        } else {
          // promoted before, actionType is not DELETE , update
          await this.entityPromotionHandler[
            request.entitySnapshot.entityType
          ].service.promoteUpdate(
            tx,
            request.targetGroup,
            targetEntityId,
            request.entitySnapshot.entityData,
            operatorId,
          );
        }

        return approvedRequest;
      },
      { timeout: this.configService.get<number>('changeManagement.approvalTransactionTimeout') },
    );
  }

  private async findRequestUserIdsByStatus(
    tx: Prisma.TransactionClient,
    requestId: number,
    status?: DataPromotionRequestStatus,
  ): Promise<{
    requesterId: number;
    operatorId: number;
  }> {
    const request = await tx.dataPromotionRequest.findFirst({
      where: { id: requestId, status },
      select: {
        requesterId: true,
        operatorId: true,
      },
    });

    if (!request) {
      // if request not found, throw exception
      throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_NOT_FOUND);
    }
    return { requesterId: request.requesterId, operatorId: request.operatorId };
  }

  async rejectDataPromotionRequest(
    operatorId: number,
    requestId: number,
    operatorComment?: string,
  ): Promise<DataPromotionRequest> {
    return this.prisma.$transaction(async (tx) => {
      const operatedDate = new Date();
      return await tx.dataPromotionRequest.update({
        where: { id: requestId },
        data: {
          status: DataPromotionRequestStatus.REJECTED,
          operatorId,
          operatorComment,
          operatedDate,
        },
      });
    });
  }

  async cancelDataPromotionRequest(
    requesterId: number,
    requestId: number,
    requesterComment?: string,
  ): Promise<DataPromotionRequest> {
    return this.prisma.$transaction(async (tx) => {
      const requestUserIds = await this.findRequestUserIdsByStatus(
        tx,
        requestId,
        DataPromotionRequestStatus.PENDING,
      );

      if (requestUserIds.requesterId !== requesterId) {
        // if canceling own request, throw exception
        throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_OTHERS_CANCELLATION);
      }

      return await tx.dataPromotionRequest.update({
        where: { id: requestId },
        data: {
          operatorId: requesterId,
          operatedDate: new Date(),
          status: DataPromotionRequestStatus.CANCELED,
          requesterComment,
        },
      });
    });
  }

  // for now, this function is only for updating comment
  async updateDataPromotionRequest(
    userId: number,
    requestId: number,
    dto: UpdateDataPromotionRequestDto,
  ): Promise<DataPromotionRequest> {
    return this.prisma.$transaction(async (tx) => {
      const requestUserIds = await this.findRequestUserIdsByStatus(tx, requestId);
      if (userId === requestUserIds.operatorId) {
        return await tx.dataPromotionRequest.update({
          where: { id: requestId },
          data: {
            operatorComment: dto.comment,
          },
        });
      }
      if (userId === requestUserIds.requesterId) {
        return await tx.dataPromotionRequest.update({
          where: { id: requestId },
          data: {
            requesterComment: dto.comment,
          },
        });
      }

      // if updating a request that does not belong to the user, throw exception
      throw new ApiException(ErrorCode.DATA_PROMOTION_REQUEST_UNAUTHORIZED_UPDATE);
    });
  }

  async getDataPromotionRequestAndEntitySnapshotByGroupId(groupId: number) {
    // get the latest approved snapshot for GROUP_SETTINGS
    return await this.prisma.dataPromotionRequest.findFirst({
      where: {
        status: DataPromotionRequestStatus.APPROVED,
        entitySnapshot: {
          entityType: SnapshotEntityType.GROUP_SETTINGS,
          groupId,
        },
      },
      include: {
        entitySnapshot: true,
      },
      orderBy: {
        operatedDate: 'desc',
      },
    });
  }
  async createGroupSettingsSnapshot(
    groupId: number,
    createdByUserId: number,
    name: string,
    entityType: SnapshotEntityType[],
    snapshotType: SnapshotEntityType,
  ) {
    const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
    const entityData = await this.entityPromotionHandler[
      snapshotType
    ].service.generateEntityDataForSnapshot(groupId, llmModel.id.toString(), entityType);
    if (Object.keys(entityData).length > 0) {
      return await this.prisma.entitySnapshot.create({
        data: {
          group: {
            connect: {
              id: groupId,
            },
          },
          createdBy: {
            connect: {
              id: createdByUserId,
            },
          },
          name,
          entityType: SnapshotEntityType.GROUP_SETTINGS,
          entityId: llmModel.id.toString(),
          entityData,
          versionDate: new Date(),
        },
      });
    }
  }

  getEntityPromotionHandler() {
    return this.entityPromotionHandler;
  }
}
