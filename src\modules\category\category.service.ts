import { Injectable, Logger } from '@nestjs/common';
import { CategoryType, Prisma } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Injectable()
export class CategoryService {
  private logger = new Logger(CategoryService.name);
  constructor(private readonly prisma: PrismaService) {}

  async getCategories(parentId?: number, categoryType?: CategoryType) {
    const where: Prisma.CategoryWhereInput = {
      parentId: parentId ?? 0,
    };

    if (categoryType) {
      where.type = categoryType;
    }

    return this.prisma.category.findMany({
      where,
      include: {
        children: {
          orderBy: {
            order: 'asc',
          },
        },
      },
      orderBy: {
        order: 'asc',
      },
    });
  }

  public getCategoriesPrismaQuery(categoryType: CategoryType) {
    return {
      categories: {
        where: {
          category: {
            type: categoryType,
          },
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
    };
  }
}
