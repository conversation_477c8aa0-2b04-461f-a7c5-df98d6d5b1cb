import { Environment, PrismaClient, RoleType, SystemName } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  const password = await hash('Abcd1234', 10);
  await prisma.$transaction(async (tx) => {
    await tx.featureFlag.update({
      where: { key: 'AUTH.ENABLE_USERNAME_PASSWORD_LOGIN' },
      data: { isEnabled: true },
    });
    const sudoRole = await tx.role.findFirst({
      select: { id: true },
      where: {
        groupId: 0,
        systemName: SystemName.SUDO,
        roleType: RoleType.SYSTEM_DEFAULT,
      },
    });

    const botCreatorRole = await tx.role.findFirst({
      select: { id: true },
      where: {
        groupId: 0,
        systemName: SystemName.BOT_CREATOR,
        roleType: RoleType.SYSTEM_DEFAULT,
      },
    });

    const accountMgmtRole = await tx.role.findFirst({
      select: { id: true },
      where: {
        groupId: 0,
        systemName: SystemName.ACCOUNT_MANAGEMENT,
        roleType: RoleType.SYSTEM_DEFAULT,
      },
    });

    const botReviewerRole = await tx.role.findFirst({
      select: { id: true },
      where: {
        groupId: 0,
        systemName: SystemName.BOT_REVIEWER,
        roleType: RoleType.SYSTEM_DEFAULT,
      },
    });

    await tx.user.create({
      data: {
        id: ********,
        name: 'Sudo',
        roleId: sudoRole?.id as number,
        password,
        active: true,
        loginType: 'LOCAL',
        emails: {
          create: {
            email: '<EMAIL>',
            emailSafe: '<EMAIL>',
            isVerified: true,
          },
        },
      },
    });
    await tx.user.create({
      data: {
        id: ********,
        name: 'Bot Creator',
        roleId: botCreatorRole?.id as number,
        password,
        active: true,
        loginType: 'LOCAL',
        emails: {
          create: {
            email: '<EMAIL>',
            emailSafe: '<EMAIL>',
            isVerified: true,
          },
        },
      },
    });
    await tx.user.create({
      data: {
        id: ********,
        name: 'Account Mgmt',
        roleId: accountMgmtRole?.id as number,
        password,
        active: true,
        loginType: 'LOCAL',
        emails: {
          create: {
            email: '<EMAIL>',
            emailSafe: '<EMAIL>',
            isVerified: true,
          },
        },
      },
    });

    await tx.user.create({
      data: {
        id: ********,
        name: 'Bot Reviewer',
        roleId: botReviewerRole?.id as number,
        password,
        active: true,
        loginType: 'LOCAL',
        emails: {
          create: {
            email: '<EMAIL>',
            emailSafe: '<EMAIL>',
            isVerified: true,
          },
        },
      },
    });

    const envs = [Environment.TEST, Environment.PROD];
    for (let i = 0, len = envs.length; i < len; i++) {
      const botId = ******** + i;
      const botName = 'Bot with very low rate limit';

      const group = await prisma.group.create({
        data: {
          id: botId,
          name: botName,
          env: envs[i],
        },
      });
      await prisma.lLMModel.create({
        data: {
          modelId: `Model for ${botId}`,
          name: `Model for ${botId}`,
          groupId: group.id,
        },
      });
      await prisma.membership.create({
        data: {
          userId: ********,
          groupId: group.id,
          roleId: botCreatorRole?.id as number,
        },
      });
    }
  });
  console.log('finished');
}
console.log('initing user account for local development...');
main()
  .catch(async (e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => await prisma.$disconnect());
