import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  App,
  AppResource,
  AppResourceStatus,
  AppType,
  Environment,
  Group,
  GroupType,
  Prisma,
  User,
} from '@prisma/client';
import axios, { AxiosError, AxiosRequestConfig, Method } from 'axios';
import { Response } from 'express';
import { JSONPath } from 'jsonpath-plus';
import pRetry from 'p-retry';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { convertToJsonObj } from 'src/helpers/object';
import { KeycloakService } from '../../providers/keycloak/keycloak.service';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { UserRequest } from '../auth/auth.interface';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { CreateGroupDto } from '../groups/groups.dto';
import { MembershipsService } from '../memberships/memberships.service';
import { SessionsService } from '../sessions/sessions.service';
import { WebhookConfig } from './app-center.interface';
import { CreateAppDto, CreateBotToolsDto } from './dto/create-app.dto';
import { LaunchAppDto, LaunchAppReqDto } from './dto/launch-app.dto';
import { BotToolsPaginationDto } from './dto/list-app.dto';
import { UpdateAppDto } from './dto/update-app.dto';

@Injectable()
export class AppCenterService {
  private readonly logger: Logger = new Logger(AppCenterService.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly keycloakService: KeycloakService,
    private readonly configService: ConfigService,
    private readonly membershipsService: MembershipsService,
    private readonly sessionService: SessionsService,
  ) {}

  /**
   * Creates an AppResource for a BOT_TOOL type application.
   * These are typically associated with a group.
   * @param {CreateBotToolsDto} createBotToolsDto - DTO containing details for the bot tool app resource.
   * @param {UserRequest} req - The user request object (currently unused for userId).
   * @returns {Promise<AppResource>} The created app resource.
   */
  async createBotToolsApp(
    createBotToolsDto: CreateBotToolsDto,
    req: UserRequest,
  ): Promise<AppResource> {
    const { name, groupId, description, appType: appDefinitionName } = createBotToolsDto;
    const userId = req.user.id;
    const appDefinition = await this.checkAppDefinitionExists(appDefinitionName, AppType.BOT_TOOL);
    const group = await this.prisma.group.findFirst({ where: { id: groupId } });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }
    // Allow only one groupId to create a single clientId app.
    const checkBootToolsExists = await this.prisma.appResource.findFirst({
      where: {
        groupId: createBotToolsDto.groupId,
        appInfo: {
          clientId: appDefinition.clientId,
          appType: AppType.BOT_TOOL,
        },
      },
    });
    if (checkBootToolsExists) {
      throw new ApiException(ErrorCode.BOT_TOOLS_ALREADY_CREATED);
    }
    const createdAppResource = await this.prisma.$transaction(async (tx) => {
      const createdAppResource = await this.createAppResource(
        tx,
        name,
        description,
        appDefinition,
        userId,
        group,
      );
      return createdAppResource;
    });
    return createdAppResource;
  }

  /**
   * Creates an AppResource for an INDEPENDENCE type application.
   * These are standalone and not tied to a specific group.
   * @param {CreateGroupDto} createAppDto - DTO containing details for the independent app resource.
   * @param {UserRequest} req - The user request object
   * @returns {Promise<AppResource>} The created app resource.
   * @throws {NotFoundException} If the corresponding App definition doesn't exist.
   */
  async createApp(createAppDtoInput: CreateAppDto, req: UserRequest): Promise<AppResource> {
    const { name, description, appType: appDefinitionName } = createAppDtoInput;
    const userId = req.user.id;
    const appDefinition = await this.checkAppDefinitionExists(
      appDefinitionName,
      AppType.INDEPENDENCE,
    );
    const createdAppResource = await this.prisma.$transaction(async (tx) => {
      const createGroupDto: CreateGroupDto = { ...createAppDtoInput, groupType: GroupType.APP };
      delete createGroupDto?.['appType'];
      const createdGroup = await this.membershipsService.createGroupAndUserMembership(
        tx,
        userId,
        FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD,
        FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_PROD,
        {
          ...createGroupDto,
          env: Environment.TEST,
        },
      );
      const createdAppResource = await this.createAppResource(
        tx,
        name,
        description,
        appDefinition,
        userId,
        createdGroup.group,
      );
      return createdAppResource;
    });
    return createdAppResource;
  }

  /**
   * Creates an app resource within a transaction.
   * It handles the creation of the AppResource record and, if necessary,
   * creates a Keycloak resource and calls a webhook for post-creation actions.
   *
   * @private
   * @param {Prisma.TransactionClient} tx - The Prisma transaction client.
   * @param {string} name - The name of the app resource.
   * @param {string} description - The description of the app resource.
   * @param {App} appDefinition - The app definition for this resource.
   * @param {number} userId - The ID of the user creating this resource.
   * @param {Group} group - The group to which this app resource belongs.
   * @returns {Promise<AppResource>} The newly created app resource.
   */
  private async createAppResource(
    tx: Prisma.TransactionClient,
    name: string,
    description: string,
    appDefinition: App,
    userId: number,
    group: Group,
  ): Promise<AppResource> {
    const appResourceData: Prisma.AppResourceUncheckedCreateInput = {
      name,
      description: description || null,
      status: AppResourceStatus.PROCESSING,
      launchUrl: '',
      metaData: {},
      createdAt: new Date(),
      appId: appDefinition.id,
      groupId: group.id,
      createdBy: userId,
    };
    if (appDefinition.needCreateKeyCloakResource) {
      await this.keycloakService.createResource(appDefinition.clientId, group.id.toString());
    }
    const user = await this.prisma.user.findFirst({
      include: {
        emails: true,
      },
      where: {
        id: userId,
      },
    });
    const createdAppResource = await tx.appResource.create({
      data: appResourceData,
    });
    this.callAppCreationWebhook(appDefinition, group, user, createdAppResource).catch((error) => {
      this.logger.error(error, 'callAppCreationWebhook failed');
    });
    return createdAppResource;
  }

  /**
   * Checks if an App definition exists with the given name and type.
   * This is used to validate that an AppResource can be created based on a pre-defined App.
   * @param {string} appDefinitionName - The name of the App definition (corresponds to App.name).
   * @param {AppType} expectedAppModelType - The expected type of the App definition (e.g., AppType.BOT_TOOL or AppType.INDEPENDENCE).
   * @returns {Promise<PrismaApp>} The found App definition.
   */
  private async checkAppDefinitionExists(
    appDefinitionName: string,
    expectedAppModelType: AppType,
  ): Promise<App> {
    const appDefinition = await this.prisma.app.findFirst({
      where: {
        name: appDefinitionName,
        appType: expectedAppModelType,
      },
    });

    if (!appDefinition) {
      throw new ApiException(ErrorCode.CREATE_APP_TYPE_NOT_FOUND);
    }
    return appDefinition;
  }

  async findAllAppResources(groupId?: number): Promise<AppResource[]> {
    return this.prisma.appResource.findMany({
      where: { groupId: groupId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneAppResource(
    appResourceId: number,
    groupId: number,
    include?: Record<string, any>,
  ): Promise<AppResource> {
    const appResource = await this.prisma.appResource.findFirst({
      include: include,
      where: { id: appResourceId, groupId },
    });
    if (!appResource) {
      throw new ApiException(ErrorCode.BOT_TOOLS_NOT_FOUND);
    }
    return appResource;
  }

  async updateAppResource(
    appResourceId: number,
    updateAppResourceDto: UpdateAppDto,
    groupId: number,
  ): Promise<AppResource> {
    await this.findOneAppResource(appResourceId, groupId);
    try {
      return this.prisma.appResource.update({
        where: { id: appResourceId },
        data: updateAppResourceDto,
      });
    } catch (error) {
      console.error(
        `[AppCenterService] Could not update app resource with ID ${appResourceId}:`,
        error,
      );
      throw new InternalServerErrorException(
        `Could not update app resource with ID ${appResourceId}.`,
      );
    }
  }

  /**
   * Removes an application resource and performs necessary cleanup.
   * This function handles the deletion of an AppResource from the database.
   * It also orchestrates additional cleanup tasks, such as:
   * - Calling a configured webhook to notify external systems of the removal.
   * - Deleting any associated Keycloak resources if the application requires them.
   * The entire process is wrapped in a database transaction to ensure atomicity.
   *
   * @param {number} appResourceId - The ID of the app resource to be removed.
   * @param {number} groupId - The ID of the group to which the app resource belongs.
   * @param {UserRequest} req - The user request object, containing user details for webhook payload.
   * @returns {Promise<AppResource>} A promise that resolves with the removed app resource.
   * @throws {ApiException} If the webhook call fails, it throws an APP_CENTER_REMOVE_RESOURCE_FAILED error.
   */
  async removeAppResource(
    appResourceId: number,
    groupId: number,
    req: UserRequest,
  ): Promise<AppResource> {
    const appResource = await this.findOneAppResource(appResourceId, groupId, { appInfo: true });
    await this.prisma.$transaction(async (tx) => {
      await tx.appResource.delete({ where: { id: appResourceId } });
      const appInfo = appResource?.['appInfo'] as App;
      if (appInfo.removeParamConfig && appResource.status == AppResourceStatus.CREATED) {
        await this.callAppRemoveWebhook(appInfo, appResource, groupId, req).catch((error) => {
          this.logger.error(`[AppCenterService] Remove Webhook Failed`, error);
          throw new ApiException(ErrorCode.APP_CENTER_REMOVE_RESOURCE_FAILED);
        });
      }
      if (appInfo.needCreateKeyCloakResource) {
        await this.keycloakService.deleteResource(appInfo.clientId, groupId.toString());
      }
    });
    return appResource;
  }

  public async launchAppWithGroupId(
    groupId: number,
    response: Response,
    req: UserRequest,
    data: LaunchAppReqDto,
  ) {
    const appResource = await this.prisma.appResource.findFirst({
      include: { appInfo: true },
      where: { groupId },
    });
    if (!appResource) {
      throw new ApiException(ErrorCode.APP_NOT_FOUND);
    }
    return await this.launchApp(appResource, response, req, data);
  }

  public async launchBotTool(
    appId: number,
    groupId: number,
    response: Response,
    req: UserRequest,
    data: LaunchAppReqDto,
  ) {
    const app = await this.prisma.appResource.findUnique({
      include: { appInfo: true },
      where: { id: appId, groupId },
    });
    if (!app) {
      throw new ApiException(ErrorCode.BOT_TOOLS_NOT_FOUND);
    }
    return await this.launchApp(app, response, req, data);
  }

  async launchApp(
    app: AppResource & { appInfo: App },
    response: Response,
    req: UserRequest,
    data: LaunchAppReqDto,
  ): Promise<LaunchAppDto> {
    if (app.status !== AppResourceStatus.CREATED) {
      throw new ApiException(ErrorCode.APP_NOT_CREATED);
    }
    const userSession = await this.sessionService.getUserSessionWithRefreshToken(
      req.user.id,
      data.refreshToken,
    );
    const userAppCenterToken = await this.keycloakService
      .refreshToken(userSession.appCenterRefreshToken)
      .catch((err: AxiosError) => {
        this.logger.error('refreshToken error', err);
        throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
      });
    const { tokenCookiesName, clientId, launchBaseUrl, launchType } = app.appInfo;

    const tokenSet = await this.keycloakService.exchangeToken(
      userAppCenterToken.access_token,
      clientId,
    );
    if (launchType === 'REDIRECT') {
      if (tokenCookiesName) {
        response.cookie(tokenCookiesName, JSON.stringify({ access_token: tokenSet.access_token }), {
          httpOnly: true,
          secure: true,
          sameSite: 'none',
          path: '/',
          domain: this.configService.get('frontendDomain'),
          maxAge: 3600000,
        });
      }
    }
    return {
      launchUrl: launchBaseUrl + app.launchUrl,
      launchType,
      tokenKey: tokenCookiesName,
      token: JSON.stringify(tokenSet),
    };
  }

  async findBotTools(botToolsPageDto: BotToolsPaginationDto, groupId: number) {
    const list = await this.prisma.appResource.findMany({
      ...botToolsPageDto,
      include: {
        appInfo: true,
        createdAppUser: true,
      },
      where: {
        ...botToolsPageDto.where,
        groupId,
      },
    });
    const count = await this.prisma.appResource.count({
      where: {
        ...botToolsPageDto.where,
        groupId,
      },
    });
    return { list, count };
  }

  /**
   * Calls a configured webhook after an application resource is initiated.
   * This method is responsible for making an HTTP request to a URL specified
   * in the AppDefinition's `createParamConfig`. It uses `p-retry` for resilience.
   * On successful webhook execution, it updates the AppResource's status to 'CREATED'.
   * If the webhook call fails after retries, it updates the status to 'UNKNOWN'.
   *
   * @private
   * @param {App} appDefinition - The definition of the application, containing webhook configuration.
   * @param {Group} group - The group associated with the app resource.
   * @param {User} user - The user who initiated the app creation.
   * @param {AppResource} appResourceId - The AppResource.
   * @returns {Promise<void>} A promise that resolves when the webhook call (and subsequent status update) is complete or has failed.
   */
  private async callAppCreationWebhook(
    appDefinition: App,
    group: Group,
    user: User,
    appResource: AppResource,
  ): Promise<void> {
    const appCreationWebhookConfig = this.resolvedWebHookAndData(
      appDefinition?.createParamConfig as WebhookConfig,
      { group, user, appResource, app: appDefinition },
    );
    if (!appCreationWebhookConfig) {
      this.logger.log('[AppCenterService] no resolvedWebHookAndData');
      return;
    }
    const webhookPassData = appCreationWebhookConfig.data
      ? appCreationWebhookConfig.data
      : appCreationWebhookConfig.params;
    const callWebhook = async () => {
      this.logger.log(
        `[AppCenterService] Attempting webhook for appResource ${appResource.id} (group ${
          group.id
        }): ${appCreationWebhookConfig.method} ${
          appCreationWebhookConfig.url
        } with payload/params: ${JSON.stringify(webhookPassData)}`,
      );
      const resp = await axios.request(appCreationWebhookConfig);
      await this.prisma.appResource.update({
        where: { id: appResource.id },
        data: {
          status: AppResourceStatus.CREATED,
          metaData: (resp?.data as Prisma.JsonObject) ?? {},
          launchUrl: resp?.data?.launchUrl ?? '',
        },
      });
    };
    await pRetry(callWebhook, {
      retries: 3,
      onFailedAttempt: (error) => {
        this.logger.warn(
          `[AppCenterService] Webhook attempt ${error.attemptNumber} failed for appResource ${appResource.id} (group ${group.id}). Retries left: ${error.retriesLeft}. Error: ${error.message}`,
        );
      },
    }).catch((error) => {
      this.logger.error(error, `create ${JSON.stringify(appDefinition)} error`);
      this.prisma.appResource
        .update({
          where: { id: appResource.id },
          data: { status: AppResourceStatus.UNKNOWN, metaData: convertToJsonObj(error) },
        })
        .then()
        .catch((err) => {
          this.logger.error(err, `update ${JSON.stringify(appDefinition)} error`);
        });
    });
  }

  async findAppWithAppName(appName: string) {
    const app = await this.prisma.app.findFirst({
      where: { name: appName },
    });
    if (!app) {
      throw new ApiException(ErrorCode.CREATE_APP_TYPE_NOT_FOUND);
    }
    return app;
  }

  /**
   * @description (currently.Only allow one groupId to create a single clientId app) Finds all created bot tools by group ID and returns their names.
   * @param {number} groupId - The ID of the group.
   * @returns {Promise<string[]>} - A promise that resolves to an array of bot tool names.
   */
  async findAllCreatedBotToolsType(groupId: number) {
    const createdBotTools = await this.prisma.appResource.findMany({
      select: {
        appInfo: {
          select: {
            clientId: true,
          },
        },
      },
      where: {
        groupId: groupId,
        appInfo: {
          appType: AppType.BOT_TOOL,
        },
      },
    });
    if (createdBotTools.length == 0) {
      return [];
    }
    const clientIds = createdBotTools.map((item) => item.appInfo.clientId);
    const createdBotToolsWithAppName = await this.prisma.app.findMany({
      select: {
        name: true,
      },
      where: {
        clientId: {
          in: clientIds,
        },
      },
    });
    return createdBotToolsWithAppName?.map((item) => item.name) ?? [];
  }

  /*
   * Calls a configured webhook when an application resource is removed.
   * This method constructs and sends an HTTP request to a URL specified
   * in the App's `removeParamConfig`. It's used to notify external
   * systems or trigger cleanup processes upon app resource deletion.
   *
   * @private
   * @param {App} appInfo - The definition of the application, containing webhook configuration.
   * @param {AppResource} appResource - The app resource being removed.
   * @param {number} groupId - The ID of the group associated with the app resource.
   * @param {UserRequest} req - The user request object, containing user details.
   * @returns {Promise<void>} A promise that resolves when the webhook call is complete.
   */
  private async callAppRemoveWebhook(
    appInfo: App,
    appResource: AppResource,
    groupId: number,
    req: UserRequest,
  ): Promise<void> {
    const group = await this.prisma.group.findFirst({ where: { id: groupId } });
    const user = await this.prisma.user.findFirst({
      include: {
        emails: true,
      },
      where: {
        id: req.user.id,
      },
    });
    const appCenterRemoveWebHookConfig = this.resolvedWebHookAndData(
      appInfo.removeParamConfig as WebhookConfig,
      { appResource, group, user, app: appInfo },
    );
    if (!appCenterRemoveWebHookConfig) {
      this.logger.error('[AppCenterService] callAppRemoveWebhook cannot resolved data');
      return;
    }
    const webhookPassData = appCenterRemoveWebHookConfig.data
      ? appCenterRemoveWebHookConfig.data
      : appCenterRemoveWebHookConfig.params;
    this.logger.log(
      `[AppCenterService] Attempting  AppRemoveWebhook ${appResource.id} (group ${group.id}): ${
        appCenterRemoveWebHookConfig.method
      } ${appCenterRemoveWebHookConfig.url} with payload/params: ${JSON.stringify(
        webhookPassData,
      )}`,
    );
    await axios.request(appCenterRemoveWebHookConfig);
  }

  private resolvedWebHookAndData(
    appCenterWebhookConfig: WebhookConfig | null,
    passData: { group: Group; user: User; app: App; appResource: AppResource },
  ) {
    const isValidWebHookConfigType =
      appCenterWebhookConfig &&
      typeof appCenterWebhookConfig === 'object' &&
      appCenterWebhookConfig !== null;
    if (!isValidWebHookConfigType) {
      return;
    }
    if (!appCenterWebhookConfig.url || !appCenterWebhookConfig.method) {
      this.logger.log(
        `[AppCenterService] Webhook for app ${passData.group.id} not called: 'url', 'method' mappings missing/invalid in createParamConfig.`,
      );
      return;
    }
    const resolvedWebhookData: Record<string, unknown> = {};
    for (const [key, jsonPathString] of Object.entries(appCenterWebhookConfig.data)) {
      const result = JSONPath({ path: jsonPathString, json: passData, wrap: false });
      resolvedWebhookData[key] = result;
    }
    const axiosMethod = appCenterWebhookConfig.method.toUpperCase() as Method;
    const finalAxiosConfig: AxiosRequestConfig = {
      url: appCenterWebhookConfig.url,
      method: axiosMethod,
      headers: appCenterWebhookConfig.headers || {},
      validateStatus: (status: number) => {
        return status >= 200 && status < 300;
      },
    };
    if (['GET', 'DELETE'].includes(axiosMethod)) {
      finalAxiosConfig.params = resolvedWebhookData;
    } else {
      finalAxiosConfig.data = resolvedWebhookData;
    }
    return finalAxiosConfig;
  }
}
