import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/providers/prisma/prisma.service';

import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Configuration } from 'src/config/configuration.interface';
import { ModelFileUploadDto } from './insight.dto';
import { ErrorCode } from 'src/errors/errors.constants';
import { ApiException } from 'src/errors/errors.constants';

@Injectable()
export class InsightInputFileService {
  private logger = new Logger(InsightInputFileService.name);
  axios?: AxiosInstance;
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
  ) {
    const config = this.configService.get<Configuration['insightGenerator']>('insightGenerator');
    if (config) {
      this.axios = axios.create({
        baseURL: config.host,
        timeout: config.timeout,
      });
    } else {
      this.logger.error('No LLM backend URL set.');
      throw new Error('No LLM Backend URL set.');
    }
  }

  async uploadInsightModelFiles(groupId: number, data: ModelFileUploadDto) {
    try {
     
      const modelFiles = await this.prisma.modelFile.findMany({
        where: {
          id: { in: data.modelFileIds },
        },
      });
      this.logger.log(`upload insight input files from data - ${JSON.stringify(data)}, groupId - ${groupId}, modelFiles - ${modelFiles}`);
      const group = await this.prisma.group.findUnique({
        where: {
          id: groupId,
        },
        select: {
          env: true,
        },
      });
      const files = modelFiles.map((modelFile) => ({
        id: modelFile.id,
        docId: modelFile.docId,
        s3Path: modelFile.s3Path,
        fileSize: modelFile.fileSize,
        fileName: modelFile.filename,
        env: group.env,
      }));
      const res = await this.axios.post(`/insight-generator/input-file/${groupId}/model-files`, {
        files,
      });
      return res.data;
    } catch (err) {
      this.logger.error(err, `upload insight input files, groupId - ${groupId}`);
      throw new ApiException(ErrorCode.UPLOAD_INSIGHT_INPUT_FILES_FAILED);
    }
  }
}
