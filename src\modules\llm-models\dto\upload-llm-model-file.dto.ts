import { FileClassification } from '@prisma/client';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class UploadLlmModelFileDto {
  @IsEnum(FileClassification)
  fileClassification: FileClassification;

  @IsOptional()
  @IsBoolean()
  @Transform(({value}) => {
    return value === 'true';
  })
  autoIndex?: boolean;
}

export enum HasPii {
  YES = 'YES',
  NO = 'NO',
  PENDING = 'PENDING',
  ERROR = 'ERROR',
}
