import { ApiProperty } from '@nestjs/swagger';
import { IsObject } from 'class-validator';

export class ExecuteAppWebhookBodyDto {
  @ApiProperty({
    description: 'Inputs for the App webhook. Structure can vary based on the webhook.',
    type: 'object',
    additionalProperties: true,
    example: {
      key1: 'value1',
      anotherKey: 123,
      nested: {
        prop: true,
      },
    },
  })
  @IsObject()
  webhookInputs: Record<string, unknown>;
}
