import { Body, Delete, Get, Logger, Param, ParseIntPipe, Patch, Post, Req } from '@nestjs/common';

import { Controller } from '@nestjs/common';
import { InsightTemplateService } from './insightTemplate.service';
import { Scopes } from '../auth/scope.decorator';
import { Request } from 'express';
import { UserRequest } from '../auth/auth.interface';
import { InsightTemplateDto } from './insight.dto';

@Controller('insights')
export class InsightTemplateController {
  logger = new Logger(InsightTemplateController.name);
  constructor(private insightTemplateService: InsightTemplateService) {}

  @Scopes('group-{groupId}:read-insight-template')
  @Get('/:groupId/templates')
  async getInsightTemplates(@Param('groupId', ParseIntPipe) groupId: number, @Req() req: Request) {
    const queryStr = req.url.split('?')?.[1] ?? '';
    return await this.insightTemplateService.getInsightTemplates(groupId, queryStr);
  }

  @Scopes('group-{groupId}:read-insight-template')
  @Get('/:groupId/template/default')
  async getDefaultInsightTemplate(@Param('groupId', ParseIntPipe) groupId: number) {
    return await this.insightTemplateService.getDefaultInsightTemplate(groupId);
  }

  @Scopes('group-{groupId}:read-insight-template')
  @Get('/:groupId/template/:id')
  async getInsightTemplateById(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
  ) {
    return await this.insightTemplateService.getInsightTemplateById(id, groupId);
  }

  @Scopes('group-{groupId}:write-insight-template')
  @Post('/:groupId/template')
  async createTemplate(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() request: UserRequest,
    @Body() createInsightTemplateDto: InsightTemplateDto,
  ) {
    const userId = request.user.id;
    return await this.insightTemplateService.createTemplate(
      groupId,
      userId,
      createInsightTemplateDto,
    );
  }

  @Scopes('group-{groupId}:write-insight-template')
  @Patch('/:groupId/template/:id')
  async updateTemplate(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Req() request: UserRequest,
    @Body() updateInsightTemplateDto: InsightTemplateDto,
  ) {
    const userId = request.user.id;
    return await this.insightTemplateService.updateTemplate(
      groupId,
      id,
      userId,
      updateInsightTemplateDto,
    );
  }

  @Scopes('group-{groupId}:write-insight-template')
  @Delete('/:groupId/template/:id')
  async deleteTemplate(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return await this.insightTemplateService.deleteTemplate(groupId, id);
  }
}
