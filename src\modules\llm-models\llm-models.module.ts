import { Module, forwardRef } from '@nestjs/common';
import { LLMModelsService } from './llm-models.service';
import { LLMModelsController } from './llm-models.controller';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { ApiKeysModule } from '../api-keys/api-keys.module';
import { TokensService } from '../../providers/tokens/tokens.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LLMBackendModule } from '../../providers/llm-backend/llm-backend.module';
import { GraviteeService } from '../../providers/gravitee/gravitee.service';
import { ChatSessionsModule } from '../chat-sessions/chat-sessions.module';
import { PermissionsService } from '../permissions/permissions.service';
import { S3Module } from 'src/providers/s3/s3.module';
import { FlowBotsModule } from '../flow-bots/flow-bots.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { MailModule } from 'src/providers/mail/mail.module';
import { GroupsModule } from '../groups/groups.module';
import { ChatFilesModule } from '../chat-files/chat-files.module';
import { RedisModule } from 'src/providers/redis/redis.module';
import { BotSecurityModule } from '../bot-security/bot-security.module';
import { UsersModule } from '../users/users.module';
import { ApiResourceModule } from '../api-resource/api-resource.module';
import { ElasticSearchModule } from 'src/providers/elasticsearch/elasticsearch.module';
import { FortiSanboxModule } from 'src/providers/fortisandbox/fortisandbox.module';
import { FortiSanboxService } from 'src/providers/fortisandbox/fortisandbox.service';
import { LlmEnginesModule } from '../llm-engines/llm-engines.module';
import { LLMModelsSettingService } from './llm-models-setting.service';
import { GroupNotificationModule } from '../group-notification/group-notification.module';
import { LLMModelsParametersService } from './llm-models-parameters.service';

import { QueueModule } from 'src/providers/queue/queue.module';
import { LLMModelsAdminController } from './llm-models.admin.controller';
import { LabelsModule } from '../labels/labels.module';
import { GCSModule } from '../gcs/gcs.module';
import { FileConversionModule } from 'src/providers/file-conversion/file-conversion.module';

@Module({
  imports: [
    PrismaModule,
    forwardRef(() => ApiKeysModule),
    forwardRef(() => LLMBackendModule),
    forwardRef(() => ChatSessionsModule),
    S3Module,
    FlowBotsModule,
    FeatureFlagModule,
    MailModule,
    ConfigModule,
    forwardRef(() => GroupsModule),
    forwardRef(() => ChatFilesModule),
    RedisModule,
    BotSecurityModule,
    forwardRef(() => UsersModule),
    forwardRef(() => ApiResourceModule),
    ElasticSearchModule,
    FortiSanboxModule,
    LlmEnginesModule,
    GroupNotificationModule,
    QueueModule,
    LabelsModule,
    GCSModule,
    FileConversionModule,
  ],
  controllers: [LLMModelsController, LLMModelsAdminController],
  providers: [
    LLMModelsService,
    TokensService,
    ConfigService,
    GraviteeService,
    PermissionsService,
    FortiSanboxService,
    LLMModelsSettingService,
    LLMModelsParametersService,
  ],
  exports: [LLMModelsService, LLMModelsSettingService, LLMModelsParametersService],
})
export class LLMModelsModule {}
