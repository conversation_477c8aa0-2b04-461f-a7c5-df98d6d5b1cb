import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config'; // Corrected import
import apm from 'elastic-apm-node'; // Corrected import
import { Response } from 'express';
import moment from 'moment';
import OpenAI from 'openai';
import { ChatCompletionChunk, CompletionUsage, EmbeddingCreateParams } from 'openai/resources';
import { Stream } from 'openai/streaming';
import { Configuration } from 'src/config/configuration.interface';
import { ElasticSearchService } from 'src/providers/elasticsearch/elasticsearch.service';
import { UserRequest } from '../auth/auth.interface';
import { GroupsService } from '../groups/groups.service';
import { ChannelType } from '../llm-models/dto/chat-llm-model.dto';
import { PlansService } from '../plans/plans.service';
import { PrefilledLog } from './open-ai.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import {
  ChatCompletionCreateParamsDto,
  CreateResponseDto,
  EmbeddingCreateParamsDto,
} from './open-ai.dto';
import { Feature } from '@prisma/client';
import { EMBEDDINGS_MODEL } from 'src/providers/llm-backend/llm-backend.interface';

@Injectable()
export class OpenAIService {
  private logger = new Logger(OpenAIService.name);
  private openai: OpenAI;

  constructor(
    private readonly plansService: PlansService,
    private readonly groupService: GroupsService,
    private readonly elasticSearchService: ElasticSearchService,
    private readonly configService: ConfigService,
  ) {
    const config = configService.get<Configuration['litellm']>('litellm');
    this.openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseUrl,
    });
  }

  async createCompletion(
    groupId: number,
    createCompletion: ChatCompletionCreateParamsDto,
    request: UserRequest,
    res: Response,
  ): Promise<OpenAI.ChatCompletion | void> {
    const models = await this.getGroupsSupportModels(groupId);
    if (!models.includes(createCompletion.model)) {
      throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
    }
    try {
      let completion = await this.openai.chat.completions.create(
        {
          ...createCompletion,
          ...(createCompletion.stream
            ? {
                stream_options: {
                  include_usage: true,
                },
              }
            : {}),
        },
        {
          query: request.query ? request.query : undefined,
        },
      );
      const query =
        typeof createCompletion?.messages?.[0].content === 'string'
          ? createCompletion?.messages?.[0].content
          : JSON.stringify(createCompletion?.messages?.[0].content);
      const prefilledLog = await this.generatePrefilledChatLog(
        request,
        createCompletion.model,
        query,
        createCompletion.max_tokens || 0,
      );
      if (!createCompletion.stream) {
        completion = completion as OpenAI.ChatCompletion;
        const answer = completion.choices?.[0].message.content;
        this.logChatResponse(prefilledLog, completion.usage, answer);

        return completion;
      }
      let answer = '';
      for await (const part of completion as unknown as Stream<ChatCompletionChunk>) {
        const data = JSON.stringify(part);
        answer += part?.choices?.[0]?.delta?.content ?? '';
        if (part?.usage) {
          this.logChatResponse(prefilledLog, part.usage, answer);
        }
        res.write(`data: ${data}\n\n`);
      }
      res.end();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async createResponse(
    groupId: number,
    createResponseDto: CreateResponseDto,
    request: UserRequest,
    res: Response,
  ): Promise<OpenAI.Responses.Response | void> {
    const models = await this.getGroupsSupportModels(groupId);
    if (createResponseDto.model && !models.includes(createResponseDto.model)) {
      throw new ApiException(ErrorCode.LLM_ENGINE_NOT_FOUND);
    }
    try {
      let response = await this.openai.responses.create(createResponseDto, {
        query: request.query ? request.query : undefined,
      });

      const query =
        typeof createResponseDto.input === 'string'
          ? createResponseDto.input
          : JSON.stringify(createResponseDto.input);
      const prefilledLog = await this.generatePrefilledChatLog(
        request,
        createResponseDto.model,
        query,
        createResponseDto.max_output_tokens || 0,
      );
      if (!createResponseDto.stream) {
        response = response as OpenAI.Responses.Response;
        const answer = JSON.stringify(response.output);
        this.logChatResponse(
          prefilledLog,
          {
            completion_tokens: response.usage?.output_tokens || 0,
            prompt_tokens: response.usage?.input_tokens || 0,
            total_tokens: response.usage?.total_tokens || 0,
          },
          answer,
        );
        return response as OpenAI.Responses.Response;
      }
      for await (const part of response as Stream<OpenAI.Responses.ResponseStreamEvent>) {
        const data = JSON.stringify(part);
        res.write(`data: ${data}\n\n`);
      }
      res.end();
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async generatePrefilledChatLog(
    request: UserRequest,
    model: ChatCompletionCreateParamsDto['model'],
    query: string,
    maxTokens: number,
  ): Promise<PrefilledLog> {
    const requester = {
      requesterId: request?.user.id?.toString(),
      requesterName: '',
      requesterStaffId: '',
      requesterEmail: '',
    };
    const group = await this.groupService.getGroup(request.user.groupId, {});
    let modelKey = model;
    if (modelKey.includes('gemini')) {
      modelKey = 'vertexai-' + request.body.model;
    }
    const prefilledLog: PrefilledLog = {
      date: moment(),
      botId: group.id,
      botEnv: group.env,
      botName: group.name,
      channel: ChannelType.API_KEY,
      ...requester,
      engine: modelKey,
      engineConfig: { model, max_tokens: maxTokens } as any,
      query,
      chatSessionName: '',
      isChatSessionDefault: true,
      feature: Feature.OPENAI_COMPATIBLE,
    };
    return prefilledLog;
  }

  logChatResponse(
    prefilledLog: PrefilledLog,
    completionUsage: Pick<CompletionUsage, 'prompt_tokens' | 'completion_tokens' | 'total_tokens'>,
    answer: string,
  ) {
    const usage = {
      promptTokens: completionUsage?.prompt_tokens || 0,
      completionTokens: completionUsage?.completion_tokens || 0,
      totalCompletionTokens: completionUsage?.total_tokens || 0,
      embeddingTokens: 0,
    };
    prefilledLog.durationInMS = moment().diff(prefilledLog.date, 'milliseconds');
    prefilledLog.usage = usage;
    prefilledLog.answer = answer;
    prefilledLog.traceId = apm?.currentTraceIds?.['trace.id'];
    this.logger.debug(prefilledLog);
    const trackingConfig = this.configService.get<Configuration['tracking']>('tracking');
    this.elasticSearchService.logChatRecord(
      `${trackingConfig.index}-${moment(prefilledLog.date).format('YYYY-MM-DD')}`,
      prefilledLog,
    );
  }

  async createEmbedding(
    groupId: number,
    createEmbeddingDto: EmbeddingCreateParamsDto,
    request: UserRequest,
  ): Promise<OpenAI.Embeddings.CreateEmbeddingResponse> {
    try {
      const embeddingCreateParams: EmbeddingCreateParams = {
        ...createEmbeddingDto,
      };
      const embedding = await this.openai.embeddings.create(embeddingCreateParams);

      const prefilledLog = await this.generatePrefilledEmbeddingLog(request, createEmbeddingDto);
      const usageForLogging: CompletionUsage = {
        prompt_tokens: embedding.usage?.prompt_tokens || 0,
        completion_tokens: 0,
        total_tokens: embedding.usage?.total_tokens || 0,
      };
      this.logEmbeddingResponse(prefilledLog, usageForLogging, createEmbeddingDto.input);

      return embedding;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(`Error creating embedding: ${error.message}`, error.stack);
      } else {
        this.logger.error('An unknown error occurred while creating embedding', error);
      }
      throw error;
    }
  }

  async generatePrefilledEmbeddingLog(
    request: UserRequest,
    createEmbeddingDto: EmbeddingCreateParamsDto,
  ): Promise<PrefilledLog> {
    const requester = {
      requesterId: request?.user.id?.toString(),
      requesterName: '',
      requesterStaffId: '',
      requesterEmail: '',
    };
    const group = await this.groupService.getGroup(request.user.groupId, {});
    let modelKey = createEmbeddingDto.model;
    const validModelKey = EMBEDDINGS_MODEL[modelKey.replaceAll('-', '_').toUpperCase()];
    if (validModelKey) modelKey = validModelKey;
    const prefilledLog: PrefilledLog = {
      date: moment(),
      botId: group.id,
      botEnv: group.env,
      botName: group.name,
      channel: ChannelType.API_KEY,
      ...requester,
      engine: modelKey,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      engineConfig: { ...createEmbeddingDto } as any,
      query: Array.isArray(createEmbeddingDto.input)
        ? createEmbeddingDto.input.join('; ')
        : createEmbeddingDto.input.toString(),
      chatSessionName: '',
      isChatSessionDefault: true,
      feature: Feature.EMBEDDING,
    };
    return prefilledLog;
  }

  logEmbeddingResponse(
    prefilledLog: PrefilledLog,
    usage: CompletionUsage | undefined,
    input: string | string[] | number[] | number[][],
  ) {
    const embeddingUsage = {
      promptTokens: usage?.prompt_tokens || 0,
      completionTokens: usage?.completion_tokens || 0, // Will be 0 for embeddings as set above
      totalCompletionTokens: usage?.total_tokens || 0, // This is the sum of prompt and completion, for embeddings it's just total_tokens from API
      embeddingTokens: usage?.total_tokens || 0, // Specifically log total_tokens as embeddingTokens
    };
    prefilledLog.durationInMS = moment().diff(prefilledLog.date, 'milliseconds');
    prefilledLog.usage = embeddingUsage;
    // For embeddings, 'answer' might not be directly applicable.
    // We could log a hash of the input or a summary if needed.
    // For now, let's keep it consistent or decide on a specific logging strategy for embeddings.
    prefilledLog.answer = `Generated embedding for input: ${(Array.isArray(input)
      ? input.join('; ')
      : input.toString()
    ).substring(0, 100)}...`; // Example, might need adjustment
    prefilledLog.traceId = apm?.currentTraceIds?.['trace.id'];
    this.logger.debug(prefilledLog);
    const trackingConfig = this.configService.get<Configuration['tracking']>('tracking');
    this.elasticSearchService.logChatRecord(
      // Consider if a different index or log type is needed for embeddings
      `${trackingConfig.index}-${moment(prefilledLog.date).format('YYYY-MM-DD')}`,
      prefilledLog,
    );
  }

  async getActiveModels(groupId: number): Promise<OpenAI.Models.ModelsPage> {
    const modellist = await this.getGroupsSupportModels(groupId);
    const models = await this.openai.models.list();
    const activeModels = models.data.filter((model) => modellist.includes(model.id));
    return {
      object: 'list',
      data: activeModels,
    } as any;
  }

  public async getGroupsSupportModels(groupId: number) {
    const groupsPlans = await this.plansService.getResources('BOT', groupId);
    const models = groupsPlans
      .filter((item) => item.resourceEntityType == 'LLM_ENGINE')
      .map((item) => item.resourceEntityKey.replace('vertexai-', ''));
    return models;
  }
}
