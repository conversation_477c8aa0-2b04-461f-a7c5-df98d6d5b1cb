import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { ChatMessageContentType } from '@prisma/client';
import { Observable } from 'rxjs';
import { UserRequest } from 'src/modules/auth/auth.interface';
import { ChatLlmModelDto, ChannelType } from 'src/modules/llm-models/dto/chat-llm-model.dto';
import { ChatSessionsService } from '../chat-sessions.service';

@Injectable()
export class ChatHistoryInterceptor implements NestInterceptor {
  constructor(private readonly chatSessionsService: ChatSessionsService) {}
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    // before chat
    const request = context.switchToHttp().getRequest() as UserRequest;
    const requestBody: ChatLlmModelDto = request.body;
    const isValidRequest =
      (requestBody.chatSessionType || requestBody.chatSessionId) &&
      requestBody.history?.length &&
      request.user.type === 'user' &&
      request.user.id;

    if (isValidRequest) {
      const chatSession = await this.chatSessionsService.findChatSessionOrCreateDefault(
        requestBody.chatSessionId,
        requestBody.chatSessionType,
        parseInt(request.params?.['groupId']),
        request.user.id,
      );

      let originalQuery = requestBody.history[request.body.history.length - 1];
      let query = { ...originalQuery };
      if(ChannelType.OUTLOOK === requestBody.channel){
        query.content = "**********";
      }
      
      const chatHistory = await this.chatSessionsService.createChatHistory(
        chatSession.id,
        ChatMessageContentType.TEXT,
        query, // assume last history is the user input
      );
      request.body.chatSession = chatSession;
      request.body.chatSessionId = chatSession.id;
      request.body.userPromptHistoryId = chatHistory.id;
    }
    return next.handle();
  }
}
