import {
  <PERSON>,
  Post,
  Body,
  Param,
  Logger,
  UseGuards,
  HttpCode,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { StaartAuthGuard } from 'src/modules/auth/staart-auth.guard';
import { Scopes } from 'src/modules/auth/scope.decorator';
import { ExternalApi } from 'src/swagger-document';
import { N8nService } from 'src/providers/n8n/n8n.service';
import { ExecuteAppWebhookParamsDto } from '../dto/app-webhook/execute-app-webhook-params.dto';
import { ExecuteAppWebhookBodyDto } from '../dto/app-webhook/execute-app-webhook-body.dto';
import { ExecuteAppWebhookResponseDto } from '../dto/app-webhook/execute-app-webhook-response.dto';

@ApiTags('App Webhooks')
@ApiBearerAuth()
@Controller('apps/:app/webhook')
@UseGuards(StaartAuthGuard)
export class GroupAppWebhooksController {
  private readonly logger = new Logger(GroupAppWebhooksController.name);

  constructor(private readonly n8nService: N8nService) {}

  @Post('groups/:groupId/:customPath(*)')
  @ExternalApi()
  @Scopes('group-{groupId}:execute-app-webhook')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Execute an App webhook via a custom path.',
    description:
      'Triggers a pre-configured App webhook identified by the groupId and a flexible customPath. Requires `x-api-key` with `group-{groupId}:execute-app-webhook` scope.',
  })
  @ApiParam({
    name: 'app',
    description: 'ID of the App of the webhook configuration belongs.',
    type: String,
    example: 'n8n',
  })
  @ApiParam({
    name: 'groupId',
    description: 'ID of the group to which the App webhook configuration belongs.',
    type: Number,
    example: 123,
  })
  @ApiParam({
    name: 'customPath',
    description:
      'User-defined path for the App webhook (e.g., "my-workflow/trigger1" or "simple-trigger"). This path is used by the backend to determine the actual target webhook URL to call.',
    type: String,
    example: 'my-workflow/trigger1',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'App webhook execution initiated successfully.',
    type: ExecuteAppWebhookResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized - API key is missing or invalid.',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - API key does not have the required scope.',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad Request - Invalid parameters or body.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal Server Error.',
  })
  async executeAppWebhook(
    @Param() params: ExecuteAppWebhookParamsDto,
    @Body() body: ExecuteAppWebhookBodyDto,
  ): Promise<ExecuteAppWebhookResponseDto> {
    this.logger.log(
      `[${params.groupId}] Received request to execute App webhook for path: ${params.customPath}`,
    );
    this.logger.debug(`[${params.groupId}] Webhook inputs: ${JSON.stringify(body.webhookInputs)}`);

    const app = params.app.toLowerCase();
    this.logger.debug(`[${params.groupId}] App specified: ${app}`);
    if (app !== 'n8n') {
      this.logger.error(
        `[${params.groupId}] Invalid app specified: ${app}. Only 'n8n' is supported.`,
      );
      throw new HttpException(
        'Invalid app specified. Only "n8n" is supported.',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const serviceResponse = await this.n8nService.execute(
        params.groupId,
        `webhook/groups/${params.groupId}/${params.customPath.replace(/^\//, '')}`,
        body.webhookInputs,
      );

      this.logger.log(
        `[${params.groupId}] App webhook for path "${params.customPath}" executed successfully.`,
      );

      return {
        success: true,
        message: 'App webhook executed successfully.',
        appWebhookResponse: serviceResponse as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error(
        `[${params.groupId}] Error executing App webhook for path "${params.customPath}": ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof HttpException) {
        throw error; // Re-throw if it's already an HttpException
      }

      let statusCode = HttpStatus.BAD_GATEWAY; // Default for downstream errors
      let message = 'An error occurred while communicating with the App webhook service.';

      if (error instanceof Error) {
        message = error.message; // Use the service's error message
        // Try to parse status from N8nService's specific error messages
        const n8nPlatformErrorMatch = error.message.match(/N8N platform request failed: (\d{3})/);
        if (n8nPlatformErrorMatch && n8nPlatformErrorMatch[1]) {
          const parsedStatus = parseInt(n8nPlatformErrorMatch[1], 10);
          if (parsedStatus >= 400 && parsedStatus <= 599) {
            statusCode = parsedStatus;
          }
        } else if (error.message.includes('N8N Service is not configured')) {
          statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
          message = 'App webhook service is not properly configured.';
        }
      }

      throw new HttpException(message, statusCode);
    }
  }

  @Post('test/groups/:groupId/:customPath(*)')
  @ExternalApi()
  @Scopes('group-{groupId}:execute-app-webhook')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Execute an App webhook test via a custom path.',
    description:
      'Triggers a pre-configured App webhook identified by the groupId and a flexible customPath. Requires `x-api-key` with `group-{groupId}:execute-app-webhook` scope.',
  })
  async executeAppTestWebhook(
    @Param() params: ExecuteAppWebhookParamsDto,
    @Body() body: ExecuteAppWebhookBodyDto,
  ): Promise<ExecuteAppWebhookResponseDto> {
    this.logger.log(
      `[${params.groupId}] Received request to execute App webhook for path: ${params.customPath}`,
    );
    this.logger.debug(`[${params.groupId}] Webhook inputs: ${JSON.stringify(body.webhookInputs)}`);

    const app = params.app.toLowerCase();
    this.logger.debug(`[${params.groupId}] App specified: ${app}`);
    if (app !== 'n8n') {
      this.logger.error(
        `[${params.groupId}] Invalid app specified: ${app}. Only 'n8n' is supported.`,
      );
      throw new HttpException(
        'Invalid app specified. Only "n8n" is supported.',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const serviceResponse = await this.n8nService.execute(
        params.groupId,
        `webhook-test/groups/${params.groupId}/${params.customPath.replace(/^\//, '')}`,
        body.webhookInputs,
      );

      this.logger.log(
        `[${params.groupId}] App webhook test for path "${params.customPath}" executed successfully.`,
      );

      return {
        success: true,
        message: 'App webhook test executed successfully.',
        appWebhookResponse: serviceResponse as Record<string, unknown>,
      };
    } catch (error) {
      this.logger.error(
        `[${params.groupId}] Error executing App webhook test for path "${params.customPath}": ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined,
      );

      if (error instanceof HttpException) {
        throw error; // Re-throw if it's already an HttpException
      }

      let statusCode = HttpStatus.BAD_GATEWAY; // Default for downstream errors
      let message = 'An error occurred while communicating with the App webhook service.';

      if (error instanceof Error) {
        message = error.message; // Use the service's error message
        // Try to parse status from N8nService's specific error messages
        const n8nPlatformErrorMatch = error.message.match(/N8N platform request failed: (\d{3})/);
        if (n8nPlatformErrorMatch && n8nPlatformErrorMatch[1]) {
          const parsedStatus = parseInt(n8nPlatformErrorMatch[1], 10);
          if (parsedStatus >= 400 && parsedStatus <= 599) {
            statusCode = parsedStatus;
          }
        } else if (error.message.includes('N8N Service is not configured')) {
          statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
          message = 'App webhook service is not properly configured.';
        }
      }

      throw new HttpException(message, statusCode);
    }
  }
}
