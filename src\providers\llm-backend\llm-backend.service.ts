import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ApiResource,
  ChatFileType,
  ChatMessageContentType,
  Group,
  LLMModel,
  ModelFile,
  Feature,
  FileStatus,
  FileVerifyStatus,
} from '@prisma/client';
import axios, { AxiosInstance } from 'axios';
import { Response } from 'express';
import moment from 'moment';
import { UserRequest } from 'src/modules/auth/auth.interface';
import { BotSecurityService } from 'src/modules/bot-security/bot-security.service';
import { ChatSessionsService } from 'src/modules/chat-sessions/chat-sessions.service';
import { GroupsService } from 'src/modules/groups/groups.service';
import {
  ChannelType,
  ChatLlmModelDto,
  ChatRequester,
  FunctionDefinition,
  HistoryMessage,
  SecurityScanType,
  ToolDefinition,
  ToolType,
} from 'src/modules/llm-models/dto/chat-llm-model.dto';
import { LLMModelsService } from 'src/modules/llm-models/llm-models.service';
import { UsersService } from 'src/modules/users/users.service';
import { Readable, Stream } from 'stream';
import { Configuration } from '../../config/configuration.interface';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { ElasticSearchService } from '../elasticsearch/elasticsearch.service';
import { PrismaService } from '../prisma/prisma.service';
import { S3Service } from '../s3/s3.service';
import {
  ChatResponse,
  ContentPoint,
  EmbeddingsResponse,
  GenerateRequest,
  GenerateResponse,
  LLMModeResponse,
  LLMModelCreateResponse,
  ROLE,
  Usage,
  overall,
  LLMModelKnowledgeBasePeer,
} from './llm-backend.interface';
import { EmbeddingsDto } from 'src/modules/llm-models/dto/embeddings-llm-model.dto';
import mime from 'mime';
import { ChatFilesService } from 'src/modules/chat-files/chat-files.service';
import { ChatFilesApproach } from 'src/modules/chat-files/chat-files.dto';
import { v4 } from 'uuid';
import apm from 'elastic-apm-node';
import { GCSService } from 'src/modules/gcs/gcs.service';
import { FeatureFlagService } from 'src/modules/feature-flags/feature-flags.service';
import { FeatureFlagKey } from 'src/modules/feature-flags/feature-flags.constants';

interface SseEvent {
  event: string;
  data: any;
}

@Injectable()
export class LLMBackendService {
  axios?: AxiosInstance;
  private logger = new Logger(LLMBackendService.name);

  constructor(
    private configService: ConfigService,
    private s3Service: S3Service,
    private gcsService: GCSService,
    private elasticSearchService: ElasticSearchService,
    private chatSessionsService: ChatSessionsService,
    private botSecurityService: BotSecurityService,
    @Inject(forwardRef(() => GroupsService))
    private groupsService: GroupsService,
    private usersService: UsersService,
    private prisma: PrismaService,
    @Inject(forwardRef(() => LLMModelsService))
    private readonly llmModelsService: LLMModelsService,
    private chatFilesSerice: ChatFilesService,
    private featureFlagService: FeatureFlagService,
  ) {
    const llmConfig = this.configService.get<Configuration['llm']>('llm');
    if (llmConfig && llmConfig.backend) {
      this.axios = axios.create({
        baseURL: llmConfig.backend,
        timeout: llmConfig.backend_timeout || 30000,
        headers: {
          'x-api-key': llmConfig.backend_api_key,
        },
      });
    } else {
      this.logger.error('No LLM backend URL set.');
      throw new Error('No LLM Backend URL set.');
    }
  }

  async createModel(env: string, name: string): Promise<LLMModelCreateResponse> {
    const response = await this.axios.post(`/api/${env.toLowerCase()}/models`, {
      name,
    });
    return response.data;
  }

  async getModel(env: string, modelId: string): Promise<LLMModeResponse> {
    const response = await this.axios.get(`/api/${env.toLowerCase()}/models/${modelId}`);
    return response.data;
  }

  async getLLMModelConfig() {
    const response = await this.axios.get(`/api/llm_model_config`);
    return response.data;
  }

  async chatWithModel(
    env: string,
    chatRequest: ChatLlmModelDto,
    controllerResponse: Response,
    prefilledLog: any,
    isStream: boolean,
    loginId?: number,
  ): Promise<ChatResponse | void> {
    this.logger.debug(chatRequest, `chatWithModel request of group id - ${chatRequest.groupId}`);
    try {
      this.convertFunctionCallToToolsCall(chatRequest);
      const isChatSession: boolean =
        (chatRequest.chatSessionType || chatRequest.chatSessionId) &&
        prefilledLog.requesterId &&
        ![ChannelType.API_KEY, ChannelType.AUTO_TEST].includes(prefilledLog.channel);

      const chatWithModelResponse = await this.axios.post(
        `/api/${env.toLowerCase()}/chat/${chatRequest.llmModel.modelId}`,
        chatRequest,
        { responseType: isStream ? 'stream' : 'json' },
      );
      const userAsk = chatRequest.history.filter((item) => item.role === 'user');
      const prompt = userAsk.at(-1).content;

      //judge enable PII or not
      const enableOutputPIIFlag = await this.botSecurityService.isChatEnablePIIScan(
        chatRequest.overrides,
        chatRequest.channel ?? ChannelType.PLAYGROUND,
        [SecurityScanType.ON, SecurityScanType.OUTPUT],
      );

      const showReferenceFlag = chatRequest.overrides?.show_reference ?? true;

      if (isStream) {
        const stream: Stream = chatWithModelResponse.data;
        let lastChunkData: string = '';
        stream.on('data', async (data: Uint8Array) => {
          const { sseEvents, remainingChunkData } = this.convertStreamDataToSseObj(
            data,
            lastChunkData,
          );
          lastChunkData = remainingChunkData;
          for (const sseEvent of sseEvents) {
            // handle error event
            if (sseEvent.event === 'error') {
              controllerResponse.write(this.convertSseEventToStreamData(sseEvent), 'utf-8');
              controllerResponse.end();
            }
            // handle overall event
            else if (sseEvent.event === 'overall') {
              let overallData: overall = sseEvent.data;
              // capture image usage
              if ('imageUsage' in sseEvent.data) {
                overallData = {
                  ...overallData,
                  file_expired_at: moment().add(1, 'months').toDate(),
                };
              }

              // handle pii scan
              if (enableOutputPIIFlag) {
                const outputScannerResult = await this.botSecurityService.outputDetect(
                  prompt,
                  overallData.answer,
                  chatRequest.groupId,
                  loginId,
                );

                overallData = {
                  ...overallData,
                  ...(chatRequest?.inputScannersResult ?? {}),
                  ...(outputScannerResult
                    ? {
                        output_scanners_result: outputScannerResult.output_scanners_result,
                        output_scanners_result_is_valid:
                          outputScannerResult.output_scanners_result_is_valid,
                      }
                    : {}),
                };
              }
              let answer = overallData?.answer ? overallData?.answer : '';
              const usage = overallData?.usage;
              // handle chat session
              if (isChatSession) {
                if (ChannelType.OUTLOOK === chatRequest.channel) {
                  answer = '**********';
                }
                const message: HistoryMessage = { content: answer, role: ROLE.AI };
                const contentPoints: ContentPoint[] = overallData?.content_points;
                const chatHistory = await this.chatSessionsService.createChatHistory(
                  chatRequest.chatSessionId,
                  ChatMessageContentType.TEXT,
                  message,
                  usage,
                  contentPoints,
                  undefined,
                  undefined,
                  overallData?.file_expired_at,
                  showReferenceFlag,
                );
                prefilledLog['chatHistoryId'] = chatHistory.id;
                overallData = {
                  ...overallData,
                  chatHistoryId: chatHistory.id,
                  userPromptHistoryId: chatRequest?.userPromptHistoryId,
                };
              }
              // show Reference
              overallData = {
                ...overallData,
                showReference: showReferenceFlag,
              };

              this.llmModelsService.logChatResponse(prefilledLog, usage, answer);
              controllerResponse.write(
                this.convertSseEventToStreamData({ event: 'overall', data: overallData }),
                'utf-8',
              );
              controllerResponse.end();
            }
            // handle other events
            else {
              controllerResponse.write(this.convertSseEventToStreamData(sseEvent), 'utf-8');
            }
          }
        });
        stream.on('end', () => {});
      } else {
        if (chatWithModelResponse.data.usage?.imageUsage) {
          chatWithModelResponse.data.file_expired_at = moment().add(1, 'months').toDate();
        }
        if (enableOutputPIIFlag) {
          const outputScannerResult = await this.botSecurityService.outputDetect(
            prompt,
            chatWithModelResponse.data.answer,
            chatRequest.groupId,
            loginId,
          );

          chatWithModelResponse.data = {
            ...chatWithModelResponse.data,
            output_scanners_result: outputScannerResult.output_scanners_result,
            output_scanners_result_is_valid: outputScannerResult.output_scanners_result_is_valid,
            input_scanners_result: chatRequest.inputScannersResult?.input_scanners_result,
            input_scanners_result_is_valid:
              chatRequest.inputScannersResult?.input_scanners_result_is_valid,
          };
        }

        if (isChatSession) {
          if (
            ChannelType.OUTLOOK === chatRequest.channel &&
            chatWithModelResponse.data?.message?.content
          ) {
            chatWithModelResponse.data.message.content = '**********';
          }

          const chatHistory = await this.chatSessionsService.createChatHistory(
            chatRequest.chatSessionId,
            ChatMessageContentType.TEXT,
            chatWithModelResponse.data?.message,
            chatWithModelResponse.data?.usage,
            chatWithModelResponse.data?.contentPoints,
            undefined,
            undefined,
            chatWithModelResponse.data?.file_expired_at,
            showReferenceFlag,
          );
          prefilledLog['chatHistoryId'] = chatHistory.id;
          chatWithModelResponse.data['chatHistoryId'] = chatHistory.id;
          chatWithModelResponse.data['userPromptHistoryId'] = chatRequest?.userPromptHistoryId;
        }
        this.llmModelsService.logChatResponse(
          prefilledLog,
          chatWithModelResponse.data.usage,
          chatWithModelResponse.data.answer,
        );
        chatWithModelResponse.data.showReference = showReferenceFlag;
        return chatWithModelResponse.data;
      }
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException((err as any)?.response?.data);
    }
  }

  private convertStreamDataToSseObj(
    chunkData: Uint8Array | string,
    incompleteChunkData: string = '',
  ): { remainingChunkData: string; sseEvents: SseEvent[] } {
    const lines = (
      incompleteChunkData + (typeof chunkData === 'string' ? chunkData : chunkData.toString())
    )
      .split('\n\r\n')
      .filter((line) => line);
    /*
      Example of lines
      [
        'event: message\r\ndata: {...}\r',
        'event: overall\r\ndata: {...}\r'
      ]
    */
    const sseEvents: SseEvent[] = [];
    let remainingChunkData: string = '';

    for (let i = 0; i < lines.length; i++) {
      let event: string | null = null;
      let data: unknown = null;
      const line = lines[i];
      // handle the incomplete line e.g: event:xxx\r\ndata:xxx
      if (!line.endsWith('}\r')) {
        this.logger.debug(`sse unexpected ending with - ${line}`);
        remainingChunkData = line;
        break;
      }
      const lineContents = line.split('\n');
      /*
      Example of lines content
      [
        'event: message\r',
        'data: {...}\r'
      ]
    */
      const eventLine = lineContents[0];
      const dataLine = lineContents[1];
      if (eventLine && eventLine.startsWith(`event:`)) {
        event = eventLine.replace('event:', '').trim();
        this.logger.debug(event, 'sse event');
      }
      if (dataLine && dataLine.startsWith(`data:`)) {
        try {
          data = JSON.parse(dataLine.replace('data:', '').trim());
          this.logger.debug(data, 'sse data');
        } catch (err) {
          this.logger.error(err, `sse data error - ${dataLine}`);
          data = {};
        }
      }
      if (event && data) {
        sseEvents.push({
          event,
          data,
        });
      }
    }
    return { remainingChunkData, sseEvents };
  }

  private convertSseEventToStreamData(sseEvent: SseEvent): string {
    return `event: ${sseEvent.event}\ndata:${JSON.stringify(sseEvent.data)} \n\n`;
  }

  /**
   * @deprecated when the function call not support to azure opneAI will remove this function
   */
  private convertFunctionCallToToolsCall(chatRequest: ChatLlmModelDto): void {
    if (
      chatRequest.overrides &&
      chatRequest.overrides.functions &&
      chatRequest.overrides.functions?.length > 0
    ) {
      if (chatRequest.overrides.tools && chatRequest.overrides.tools.length > 0) {
        chatRequest.overrides.functions.map((fun: FunctionDefinition) => {
          chatRequest.overrides.tools.push({
            type: ToolType.function,
            function: fun,
          } as ToolDefinition);
        });
      } else {
        chatRequest.overrides.tools = chatRequest.overrides.functions.map(
          (fun: FunctionDefinition) => {
            return {
              type: ToolType.function,
              function: fun,
            } as ToolDefinition;
          },
        );
      }

      delete chatRequest.overrides.functions;
    }
  }

  async generateWithModel(
    groupId: number,
    env: string,
    index: string,
    generateRequest: GenerateRequest,
    request: UserRequest,
    controllerResponse: Response,
  ): Promise<GenerateResponse> {
    try {
      const startTime = moment();
      const response = await this.axios.post(
        `/api/${env.toLowerCase()}/generate/${index}`,
        generateRequest,
        {
          responseType: generateRequest.overrides?.stream ? 'stream' : 'json',
          validateStatus: (status) =>
            (status >= 200 && status < 300) || (status >= 400 && status < 500),
        },
      );

      const model = await this.llmModelsService.findOneByGroupId(groupId);
      const group = await this.groupsService.getGroup(groupId, {});
      const requester: ChatRequester = {
        requesterId: String(request?.user?.id ?? ''),
        requesterName: '',
        requesterEmail: null,
        requesterStaffId: '0',
      };
      let channel = ChannelType.PLAYGROUND;
      if (request.user?.type === 'user') {
        const user = await this.usersService.getUser(request.user.id);
        requester.requesterName = user?.name;
        requester.requesterStaffId = user?.staffId;
        const email = await this.prisma.email.findFirst({ where: { userId: user.id } });
        requester.requesterEmail = email?.email;
      } else {
        channel = ChannelType.API_KEY;
      }
      if (generateRequest.overrides?.stream) {
        controllerResponse.set({
          'Content-Type': response.headers['content-type'],
        });

        const stream: Stream = response.data;
        const sseEvents: SseEvent[] = [];
        let lastChunkData: string = '';
        stream.on('data', (data: Uint8Array) => {
          const { sseEvents, remainingChunkData } = this.convertStreamDataToSseObj(
            data,
            lastChunkData,
          );
          lastChunkData = remainingChunkData;

          for (const sseEvent of sseEvents) {
            if (sseEvent.event === 'overall') {
              const overallData: GenerateResponse = sseEvent.data;
              if (overallData?.data?.file) {
                const newData: string = data
                  .toString()
                  .replace(
                    overallData.data.file,
                    `/${groupId}/llm-model/generate/${overallData.data.file}`,
                  );
                data = new Uint8Array(Array.from(newData).map((char) => char.charCodeAt(0)));
                controllerResponse.write(data);
              } else {
                controllerResponse.write(this.convertSseEventToStreamData(sseEvent), 'utf-8');
              }
            } else {
              controllerResponse.write(this.convertSseEventToStreamData(sseEvent), 'utf-8');
            }
          }
        });
        stream.on('end', () => {
          controllerResponse.end();
          const overall: GenerateResponse = sseEvents.find(
            (sseEvent) => sseEvent.event === 'overall',
          )?.data;
          this.logChatRecord(
            group,
            model,
            startTime,
            generateRequest,
            overall?.usage as unknown as Usage,
            channel,
            requester,
          ).catch((error: Error) => {
            this.logger.error(`logChatRecord error ${error.message}`, error.stack);
          });
        });
      } else {
        if (response.data.data?.file) {
          response.data.data.file = `/${groupId}/llm-model/generate/${response.data.data.file}`;
        }

        this.logChatRecord(
          group,
          model,
          startTime,
          generateRequest,
          response?.data?.usage,
          channel,
          requester,
        ).catch((error: Error) => {
          this.logger.error(`logChatRecord error ${error.message}`, error.stack);
        });

        return response.data;
      }
    } catch (err) {
      this.logger.error((err as any)?.response?.data);
      throw err;
    }
  }

  async logChatRecord(
    group: Group,
    model: LLMModel,
    startTime: moment.Moment,
    request: GenerateRequest,
    usage: Usage,
    channel: ChannelType,
    requester: ChatRequester,
  ) {
    const config = this.configService.get<Configuration['tracking']>('tracking');
    let feature: string | undefined;
    if (request.function === 'tts') {
      feature = Feature.TTS;
    } else if (request.function === 'stt') {
      feature = Feature.STT;
    }

    try {
      this.elasticSearchService.logChatRecord(
        `${config.index}-${moment(startTime).format('YYYY-MM-DD')}`,
        {
          date: startTime,
          botId: model.groupId,
          botEnv: group.env,
          botName: model.name,
          ...requester,
          traceId: apm?.currentTraceIds?.['trace.id'],
          channel,
          engine: request.model,
          engineConfig: request,
          durationInMS: moment().diff(startTime, 'milliseconds'),
          feature,
          usage,
        },
      );
    } catch (e) {
      this.logger.error(e, 'error in elastic search');
    }
  }

  async updateModelTonesOrStartupMessage(
    env: string,
    modelId: string,
    tone: string,
    startupMessage: string,
  ): Promise<LLMModeResponse> {
    const data: Record<string, string> = {};
    if (tone) {
      data['tone'] = tone;
    }
    if (startupMessage) {
      data['startup_message'] = startupMessage;
    }
    const response = await this.axios.put(
      `/api/${env.toLowerCase()}/models/${modelId}/tones`,
      data,
    );
    return response.data;
  }

  async verifyModelFile(
    env: string,
    modelId: string,
    s3Path: string,
    groupId: number,
    userId: number,
  ): Promise<any> {
    try {
      if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
        throw new InternalServerErrorException('Static file bucket not set');
      const response = await this.axios.post(
        `/api/${env.toLowerCase()}/models/${modelId}/embeddings?verify=true`,
        {
          s3_bucket: this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
          s3_key: s3Path,
          s3_env: env,
          group_id: groupId,
          user_id: userId,
        },
      );
      this.logger.log(JSON.stringify(response.data));
      return response.data;
    } catch (error) {
      this.logger.error(JSON.stringify(error));
    }
  }

  async processModelFile(
    env: string,
    modelFile: ModelFile & { model: LLMModel & { group: Group } },
  ) {
    if (
      modelFile.status === FileStatus.PROCESSING ||
      modelFile.status === FileStatus.COMPLETED ||
      modelFile.verifyStatus != FileVerifyStatus.VERIFY_SUCCESS
    ) {
      return modelFile;
    }
    if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
      throw new InternalServerErrorException(`${env} static file bucket not set`);
    try {
      const response = await this.axios.post(
        `/api/${env.toLowerCase()}/models/${modelFile.model.modelId}/embeddings`,
        {
          s3_bucket: this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
          s3_key: modelFile.s3Path,
          s3_env: env,
        },
      );
      this.logger.log(
        `model file ${modelFile.docId} embedding response ${JSON.stringify(response.data)}`,
      );
      const newModelFile = await this.prisma.modelFile.update({
        where: {
          docId: modelFile.docId,
        },
        data: { status: 'PROCESSING', indexingStartDate: new Date() },
      });
      return newModelFile;
    } catch (error) {
      this.logger.error(error, 'Failed to process Model File');
      throw error;
    }
  }

  async uploadTempFile(
    env: string,
    fileUuid: string,
    file: Express.Multer.File,
    groupId: number,
  ): Promise<string> {
    try {
      if (file.size > this.configService.get<number>('s3.staticFilesMaxSize'))
        throw new ApiException(ErrorCode.FILE_TOO_LARGE);
      if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
        throw new InternalServerErrorException('Static file bucket not set');

      const filename = `${fileUuid}.${file.originalname.split('.').pop()}`;
      const fileKey = `generated-resources/temporary/${filename}`;

      const { Key } = await this.s3Service.upload(
        fileKey,
        file.originalname,
        file.buffer,
        file.mimetype,
        groupId,
        this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
        false,
      );
      if (!Key) {
        throw new ApiException(ErrorCode.UPLOAD_FILE_TO_MODEL_FAILED);
      }
      return filename;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }
  async uploadModelFile(
    env: string,
    modelId: string,
    fileUuid: string,
    file: Express.Multer.File,
    groupId: number,
    extraMetadata: Record<string, string> = {},
  ): Promise<{ s3Path: string; size: number; gcsPath: string }> {
    try {
      if (file.size > this.configService.get<number>('s3.staticFilesMaxSize'))
        throw new ApiException(ErrorCode.FILE_TOO_LARGE);
      if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
        throw new InternalServerErrorException('Static file bucket not set');

      const fileKey = `models/${modelId}/${fileUuid}.${file.originalname.split('.').pop()}`;
      const { Key } = await this.s3Service.upload(
        fileKey,
        file.originalname,
        file.buffer,
        file.mimetype,
        groupId,
        this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
        false,
        extraMetadata,
      );
      const enableGcsUpload = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        groupId,
        FeatureFlagKey.ENABLE_GCS_UPLOAD,
      );
      let gcsPath = null;
      if (enableGcsUpload?.isEnabled) {
        gcsPath = await this.gcsService.uploadFile(
          this.configService.get<string>(`gcs.bucket`),
          fileKey,
          Readable.from(file.buffer),
          groupId,
          extraMetadata,
        );
      }
      if (!Key) {
        throw new ApiException(ErrorCode.UPLOAD_FILE_TO_MODEL_FAILED);
      }
      return { s3Path: Key, gcsPath, size: file.size };
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async uploadChatFile(
    env: string,
    file: Express.Multer.File,
    groupId: number,
    userId: number,
    s3Basename: string,
    chatFileType: ChatFileType,
    extraMetadata: Record<string, string> = {},
  ): Promise<{ s3Path: string; size: number; gcsPath: string }> {
    const s3fileKey = this.chatFilesSerice.getS3FileKey(groupId, userId, s3Basename, chatFileType);

    await this.s3Service.upload(
      s3fileKey,
      file.originalname,
      file.buffer,
      file.mimetype,
      groupId,
      this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
      false,
      extraMetadata,
    );
    const enableGcsUpload = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.ENABLE_GCS_UPLOAD,
    );
    let gcsFileKey = null;
    if (enableGcsUpload?.isEnabled) {
      gcsFileKey = await this.gcsService.uploadFile(
        this.configService.get<string>(`gcs.bucket`),
        s3fileKey,
        Readable.from(file.buffer),
        groupId,
        extraMetadata,
      );
    }
    return { s3Path: s3fileKey, gcsPath: gcsFileKey, size: file.size };
  }

  async removeS3ModelFile(env: string, fileKey: string): Promise<any> {
    if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
      throw new InternalServerErrorException('Static file bucket not set');

    try {
      const response = await this.s3Service.delete(
        this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
        fileKey,
      );
      return response;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async getS3ModelFile(env: string, fileKey: string) {
    if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
      throw new InternalServerErrorException('Static file bucket not set');

    try {
      return this.s3Service.getFileObject(
        this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
        fileKey,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async removeModelFile(env: string, modelId: string, docId: string): Promise<any> {
    try {
      const response = await this.axios.delete(
        `/api/${env.toLowerCase()}/models/${modelId}/files/${docId}`,
      );
      if (!response) {
        throw new ApiException(ErrorCode.DELETE_FILE_TO_MODEL_FAILED);
      }

      return response.data;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async genApiResource(apiResource: ApiResource, moduleId: string, env: string) {
    const response = await this.axios.post(
      `/api/${env.toLowerCase()}/models/${moduleId}/files/${apiResource.id}`,
      apiResource,
    );
    return response.data;
  }

  async healthCheck() {
    return this.axios.get(`api/prod/models?size=1&page=0`);
  }

  async enableKb(env: string, modelId: string, isEnable: boolean): Promise<LLMModeResponse> {
    try {
      const res = await this.axios.put(`/api/${env.toLowerCase()}/models/${modelId}/kb`, {
        enable: isEnable,
      });
      return res.data;
    } catch (err) {
      this.logger.error(err, 'Failed to call enable kb put api');
      throw err;
    }
  }

  async updateKbApiKey(
    groupId: number,
    modelId: string,
    env: string,
    apiKey: string,
  ): Promise<void> {
    try {
      await this.axios.put(`/api/${env.toLowerCase()}/models/${modelId}/kb_key`, {
        kb_token: apiKey,
        group_id: groupId,
      });
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async removeS3PIIReportFile(env: string, fileKey: string): Promise<any> {
    if (!this.configService.get<string>(`s3.piiReportBuckets.${env}`))
      throw new InternalServerErrorException('Static file bucket not set');

    this.logger.log(`start to delete pii report file ${fileKey}`);
    try {
      const response = await this.s3Service.delete(
        this.configService.get<string>(`s3.piiReportBuckets.${env}`),
        fileKey,
      );
      return response;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async removeS3PIIReportFiles(env: string, fileKeys: string[]): Promise<any> {
    if (!this.configService.get<string>(`s3.piiReportBuckets.${env}`))
      throw new InternalServerErrorException('Static file bucket not set');

    this.logger.log(`start to delete pii report files ${fileKeys?.join(', ')}`);
    try {
      const response = await this.s3Service.deleteMany(
        this.configService.get<string>(`s3.piiReportBuckets.${env}`),
        fileKeys,
      );
      return response;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async embeddings(
    group: Group,
    request: UserRequest,
    embeddingsDto: EmbeddingsDto,
  ): Promise<EmbeddingsResponse> {
    this.logger.debug({ 'chatWithModel request ': embeddingsDto });
    try {
      const prefilledLog = await this.generateEmbeddingsPrefilledLog(group, request, embeddingsDto);

      const embeddinglRes = await this.axios.post(`/api/embeddings`, embeddingsDto);

      const embeddingsData: EmbeddingsResponse = embeddinglRes.data;

      this.llmModelsService.logChatResponse(prefilledLog, embeddingsData.usage, '');

      return embeddingsData;
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException((err as any)?.response?.data);
    }
  }

  async generateEmbeddingsPrefilledLog(
    group: Group,
    request: UserRequest,
    embeddingsDto: EmbeddingsDto,
  ) {
    const requester: ChatRequester = {
      requesterId: String(request?.user?.id ?? ''),
      requesterName: '',
    };
    const channel =
      request.headers['x-api-key'] != null ? ChannelType.API_KEY : ChannelType.PLAYGROUND;

    const prefilledLog = {
      date: moment(),
      botId: group.id,
      botEnv: group.env,
      botName: group.name,
      channel,
      feature: Feature.EMBEDDING,
      ...requester,
      engine: embeddingsDto.overrides?.model,
      engineConfig: embeddingsDto.overrides,
      query: '',
      chatSessionName: '',
      isChatSessionDefault: null,
      chatSessionId: null,
    } as any;

    return prefilledLog;
  }

  private async convertLocalImageUrl(
    groupId: number,
    userId: number,
    overall: overall,
  ): Promise<string | null> {
    const matched = overall.answer.match(/!\[image\]\((http[s]?:\/\/[^\s]+)\)/);

    if (matched.length > 0) {
      const url = matched[1];
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const buffer = Buffer.from(response.data, 'binary');
      const mimeType = response.headers['content-type'];
      const extension = mime.extension(mimeType);
      const fileName = `${v4()}.${extension}`;

      const file = {
        fieldname: fileName,
        originalname: fileName, // Replace with the actual file name and extension
        encoding: '7bit',
        mimetype: mimeType, // Replace with the actual MIME type
        buffer: buffer,
        size: buffer.length,
      } as Express.Multer.File;

      const rst = await this.chatFilesSerice.uploadInternalChatFile(
        groupId,
        userId,
        file,
        ChatFilesApproach.CWF,
        ChatFileType.RESPONSE,
        true,
      );

      if (typeof rst === 'string') {
        return `![image](${rst})`;
      }
    }

    return null;
  }

  async getS3ModelFileStream(env: string, fileKey: string) {
    if (!this.configService.get<string>(`s3.staticFilesBuckets.${env}`))
      throw new InternalServerErrorException('Static file bucket not set');

    try {
      const s3Response = await this.s3Service.get(
        this.configService.get<string>(`s3.staticFilesBuckets.${env}`),
        fileKey,
      );
      return s3Response.Body as Readable;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async updateKbPeerApiKey(
    env: string,
    modelId: string,
    peerKbs: LLMModelKnowledgeBasePeer[],
  ): Promise<LLMModeResponse> {
    try {
      const response = await this.axios.put(
        `/api/${env.toLowerCase()}/models/${modelId}/peer_kbs`,
        {
          peer_kbs: peerKbs,
        },
      );
      return response.data;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }
}
