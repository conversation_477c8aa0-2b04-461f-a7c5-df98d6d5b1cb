import { Injectable, Logger } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import { PassThrough, Readable } from 'stream';
import {
  AwsClient,
  AwsSecurityCredentials,
  AwsSecurityCredentialsSupplier,
} from 'google-auth-library';
import { ConfigService } from '@nestjs/config';
import { Configuration } from 'src/config/configuration.interface';
import { fromNodeProviderChain } from '@aws-sdk/credential-providers';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Injectable()
export class GCSService {
  private gcsClient: Storage;
  private logger = new Logger(GCSService.name);
  private config: Configuration['gcs'];
  constructor(private configService: ConfigService) {
    this.config = this.configService.get<Configuration['gcs']>('gcs');
    const decodedVertexAiAccount = JSON.parse(
      Buffer.from(this.config.accessKey ?? '', 'base64').toString('utf-8'),
    );
    if (decodedVertexAiAccount.type === 'external_account') {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.reinit_client_by_aws_external_account();
    } else {
      this.gcsClient = new Storage({ credentials: decodedVertexAiAccount });
    }
  }

  async reinit_client_by_aws_external_account() {
    try {
      const decodedVertexAiAccount = JSON.parse(
        Buffer.from(process.env['VERTEX_AI_ACCOUNT_KEY'] ?? '', 'base64').toString('utf-8'),
      );
      // If the account is service account, do not need to reinitialize the client
      if (decodedVertexAiAccount.type !== 'external_account') {
        return;
      }
      class AwsSupplier implements AwsSecurityCredentialsSupplier {
        private readonly region: string;

        constructor() {
          this.region = process.env['AWS_S3_REGION'] ?? 'ap-east-1';
        }

        async getAwsRegion(): Promise<string> {
          return this.region;
        }

        async getAwsSecurityCredentials(): Promise<AwsSecurityCredentials> {
          // Retrieve the AWS credentails.
          try {
            const awsCredentialsProvider = fromNodeProviderChain();
            const awsCredentials = await awsCredentialsProvider();

            const newCredentials: AwsSecurityCredentials = {
              accessKeyId: awsCredentials.accessKeyId,
              secretAccessKey: awsCredentials.secretAccessKey,
              token: awsCredentials.sessionToken,
            };
            return newCredentials;
          } catch (error) {
            throw new Error(`AWS credentials refresh failed: ${error}`);
          }
        }
      }
      const awsSupplier = new AwsSupplier();
      const clientOptions = {
        subjectTokenType: decodedVertexAiAccount.subject_token_type,
        audience: decodedVertexAiAccount.audience,
        service_account_impersonation_url: decodedVertexAiAccount.service_account_impersonation_url,
        aws_security_credentials_supplier: awsSupplier,
      };
      this.gcsClient = new Storage({
        authClient: new AwsClient(clientOptions),
      });
    } catch (error) {
      this.logger.error(error, 'Error reinitializing GCS client');
      throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  async uploadFile(
    bucketName: string,
    fileName: string,
    stream: PassThrough | Readable,
    groupId?: number,
    extraMetadata: Record<string, string> = {},
  ) {
    await this.reinit_client_by_aws_external_account();
    // Get a reference to the bucket
    const myBucket = this.gcsClient.bucket(bucketName);
    // Create a reference to a file object
    const file = myBucket.file(fileName);

    // Handle the source stream
    return new Promise<string>((resolve, reject) => {
      stream
        .pipe(
          file.createWriteStream({
            metadata: {
              metadata: {
                ...extraMetadata,
                filename: Buffer.from(fileName).toString('base64'),
                ...(groupId && { groupid: String(groupId) }),
              },
            },
          }),
        )
        .on('error', (err) => {
          this.logger.error(err, `GCS - ${fileName} upload failed`);
          reject(err);
        })
        .on('finish', () => {
          // The file upload is complete
          this.logger.log(`GCS - ${fileName} uploaded to ${bucketName}`);
          resolve(fileName);
        });

      // Handle errors from the source stream as well
      stream.on('error', (err) => {
        this.logger.error(err, `Stream error while uploading ${fileName}`);
        reject(err);
      });
    });
  }
}
