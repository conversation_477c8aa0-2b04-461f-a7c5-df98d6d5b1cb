import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KeycloakAdminClient } from '@s3pweb/keycloak-admin-client-cjs';
import axios, { AxiosError, AxiosInstance } from 'axios';
import axiosRetry from 'axios-retry';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { URLSearchParams } from 'url';
import { SSOUserResponse } from '../sso/sso.service';
import { WHITELIST_GROUP_SUFFIX } from 'src/constants/app-whitelist';

export interface KeycloakTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  id_token: string;
  'not-before-policy': number;
  session_state: string;
  scope: string;
}

export interface KeycloakIntrospectionResponse {
  active: boolean;
  username?: string;
  email?: string;
  client_id?: string;
  token_type?: string;
  exp?: number;
  iat?: number;
  nbf?: number;
  sub?: string;
  aud?: string | string[];
  iss?: string;
  jti?: string;
  scope?: string;
}

@Injectable()
export class KeycloakService {
  private readonly logger = new Logger(KeycloakService.name);
  private readonly kcAdminClient: KeycloakAdminClient;

  private readonly baseUrl: string;
  private readonly realmName: string;
  private readonly groupRoleMappingKey: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly keycloakClient: AxiosInstance;

  constructor(private configService: ConfigService) {
    this.baseUrl = this.configService.get<string>('appCenterKeycloak.baseUrl');
    this.realmName = this.configService.get<string>('appCenterKeycloak.realm');
    this.clientId = this.configService.get<string>('appCenterKeycloak.clientId');
    this.clientSecret = this.configService.get<string>('appCenterKeycloak.clientSecret');
    this.groupRoleMappingKey = this.configService.get<string>(
      'appCenterKeycloak.groupRolesMappingKey',
    );

    this.keycloakClient = axios.create();
    axiosRetry(this.keycloakClient, { retries: 2 });
    this.keycloakClient.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        const errorMessage =
          error?.response?.data || error?.message || 'Unknown Keycloak API error';
        this.logger.error(error, `Keycloak API request failed: ${errorMessage}`);
        throw error;
      },
    );

    this.kcAdminClient = new KeycloakAdminClient({
      baseUrl: this.baseUrl,
      realmName: 'master',
    });
  }

  private async ensureAdminAuthenticated(): Promise<void> {
    await this.kcAdminClient
      .auth({
        grantType: 'password',
        clientId: 'admin-cli',
        username: this.configService.get<string>('appCenterKeycloak.adminUsername'),
        password: this.configService.get<string>('appCenterKeycloak.adminPassword'),
      })
      .catch((error) => {
        this.logger.error('Failed to authenticate Keycloak Admin client', error);
        throw new ApiException(ErrorCode.KEYCLOAK_ADMIN_CLIENT_FAILED);
      });
  }

  async getAccessTokenFromCode(
    code: string,
    grant_type: string,
    client_id: string,
    redirect_uri: string,
    code_verifier: string,
  ): Promise<KeycloakTokenResponse> {
    const tokenUrl = `${this.baseUrl}/realms/${this.realmName}/protocol/openid-connect/token`;
    const params = new URLSearchParams();
    params.append('grant_type', grant_type);
    params.append('client_id', this.clientId);
    params.append('client_secret', this.clientSecret);
    params.append('code', code);
    params.append('redirect_uri', redirect_uri);
    params.append('code_verifier', code_verifier);

    this.logger.log(`Requesting token from ${tokenUrl} for client ${client_id}`);
    const response = await this.keycloakClient.post<KeycloakTokenResponse>(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    this.logger.log('Successfully fetched Keycloak token');
    return response.data;
  }

  /**
   * Exchanges a subject token for a new token with a different audience or scope.
   * @param subject_token The token to be exchanged.
   * @param requested_token_type An identifier for the type of the requested security token. (e.g., urn:ietf:params:oauth:token-type:access_token)
   * @param audience The logical name of the target service where the client intends to use the requested token.
   * @returns A Promise that resolves to the Keycloak token response.
   */
  async exchangeToken(subject_token: string, targetClient: string): Promise<KeycloakTokenResponse> {
    const tokenUrl = `${this.baseUrl}/realms/${this.realmName}/protocol/openid-connect/token`;
    const params = new URLSearchParams();
    params.append('grant_type', 'urn:ietf:params:oauth:grant-type:token-exchange');
    params.append('client_id', this.clientId);
    params.append('client_secret', this.clientSecret);
    params.append('subject_token', subject_token);
    params.append('subject_token_type', 'urn:ietf:params:oauth:token-type:access_token');
    params.append('requested_token_type', 'urn:ietf:params:oauth:token-type:access_token');

    params.append('audience', targetClient);

    this.logger.log(`Requesting token exchange from ${tokenUrl} for client ${this.clientId}`);
    const response = await this.keycloakClient.post<KeycloakTokenResponse>(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return response.data;
  }

  /**
   * Retrieves a new access token using a refresh token.
   * @param refreshToken The refresh token.
   * @returns A Promise that resolves to the Keycloak token response.
   */
  async refreshToken(refreshToken: string): Promise<KeycloakTokenResponse> {
    const tokenUrl = `${this.baseUrl}/realms/${this.realmName}/protocol/openid-connect/token`;
    const params = new URLSearchParams();
    params.append('grant_type', 'refresh_token');
    params.append('client_id', this.clientId);
    params.append('client_secret', this.clientSecret);
    params.append('refresh_token', refreshToken);

    this.logger.log(
      `Requesting new token using refresh token from ${tokenUrl} for client ${this.clientId}`,
      'KeycloakService.refreshToken',
    );
    const response = await this.keycloakClient.post<KeycloakTokenResponse>(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    this.logger.log('Successfully fetched new Keycloak token using refresh token');
    return response.data;
  }

  /**
   * Updates attributes for a Keycloak user identified by their email address.
   * @param email The email address of the user.
   * @returns A Promise that resolves when the update is complete.
   */
  async updateUserGroupRoleAttributesByEmail(email: string, groupRoles: string[]): Promise<void> {
    try {
      await this.ensureAdminAuthenticated();
      this.logger.log(`Attempting to update attributes for Keycloak user with email: ${email}`);
      const users = await this.kcAdminClient.users.find({
        realm: this.realmName,
        email,
        exact: true,
      });

      if (users.length === 0) {
        this.logger.error(`No Keycloak user found with email: ${email}. Cannot update attributes.`);
        return;
      }
      const userToUpdate = users[0];
      this.logger.log(
        `Found user ${userToUpdate.username} (ID: ${userToUpdate.id}) with email: ${email}. Proceeding with attribute update.`,
      );
      const currentAttributes = userToUpdate.attributes || {};
      const newAttributes = { ...currentAttributes };
      newAttributes[this.groupRoleMappingKey] = groupRoles;
      await this.kcAdminClient.users.update(
        { id: userToUpdate.id, realm: this.realmName },
        { ...userToUpdate, attributes: newAttributes },
      );
      return;
    } catch (error) {
      this.logger.error(
        `Error updating Keycloak user attributes for email ${email}: ${error?.['message']}`,
        error?.['stack'],
      );
      return;
    }
  }

  async extractUserFromToken(token: string): Promise<SSOUserResponse> {
    try {
      const userInfoUrl = `${this.baseUrl}/realms/${this.realmName}/protocol/openid-connect/userinfo`;
      const resp = await fetch(userInfoUrl, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });
      const tokenResponse = await resp.json();
      this.logger.log(tokenResponse, 'sso login token response');
      return tokenResponse;
    } catch (error) {
      this.logger.error(error, 'failed to get sso login token response');
      throw error;
    }
  }

  /**
   * Creates a resource in Keycloak for a given resource server client.
   * If the resource already exists, it returns the existing resource.
   * @param {string} resourceServerClientId - The ID of the resource server client.
   * @param {string} groupId - The name of the resource to create.
   * @returns {Promise<any>} A promise that resolves to the created or existing resource.
   */
  async createResource(resourceServerClientId: string, groupId: string) {
    await this.ensureAdminAuthenticated();
    const client = await this.kcAdminClient.clients.find({
      realm: this.realmName,
      clientId: resourceServerClientId,
    });
    if (!client || client.length == 0) {
      throw new ApiException(ErrorCode.KEYCLOAK_CLIENT_NOT_FOUND);
    }
    const resource = await this.kcAdminClient.clients.listResources({
      realm: this.realmName,
      id: client[0].id,
      name: groupId,
    });
    if (resource.length > 0) {
      return resource[0];
    }
    const resourceScopesAsStrings = await this.kcAdminClient.clients.listAllScopes({
      id: client[0].id,
      realm: this.realmName,
    });
    this.logger.log(
      `Attempting to create UMA resource "${groupId}" (for resource server client "${resourceServerClientId}" in realm "${this.realmName}"`,
    );
    const response = await this.kcAdminClient.clients
      .createResource(
        {
          id: client[0].id,
          realm: this.realmName,
        },
        {
          name: groupId,
          scopes: resourceScopesAsStrings,
        },
      )
      .catch((error) => {
        this.logger.error(error, 'Keycloak UMA resource creation failed.');
        throw new ApiException(ErrorCode.KEYCLOAK_RESOURCE_CREATION_FAILED);
      });
    return response;
  }

  /**
   * Deletes a resource in Keycloak for a given resource server client.
   * @param {string} resourceServerClientId - The ID of the resource server client.
   * @param {string} groupId - The name of the resource to delete.
   * @returns {Promise<void>} A promise that resolves when the resource is deleted.
   */
  async deleteResource(resourceServerClientId: string, groupId: string) {
    await this.ensureAdminAuthenticated();
    const client = await this.kcAdminClient.clients.find({
      realm: this.realmName,
      clientId: resourceServerClientId,
    });
    if (!client || client.length == 0) {
      throw new ApiException(ErrorCode.KEYCLOAK_CLIENT_NOT_FOUND);
    }
    const resource = await this.kcAdminClient.clients.listResources({
      realm: this.realmName,
      id: client[0].id,
      name: groupId,
    });
    if (!resource || resource?.length === 0) {
      return;
    }
    await this.kcAdminClient.clients
      .delResource({
        realm: this.realmName,
        id: client[0].id,
        resourceId: resource[0]._id,
      })
      .catch((error) => {
        this.logger.error(error, 'remove resource failed');
        throw new ApiException(ErrorCode.KEYCLOAK_RESOURCE_DELETION_FAILED);
      });
  }

  /**
   * Adds a user to a whitelist group in Keycloak.
   * If the user exists directly in Keycloak, they are added to a group named `${appType}_whitelist`.
   * If the user does not exist directly, their email is added to the 'claims.value'
   * of an Identity Provider (IdP) mapper named `${appType}_whitelist` for the 'hkt_sso' IdP.
   * This is typically used to grant access to users who might not have a direct Keycloak account
   *
   * @param appType - The type of application, used to determine the whitelist group/mapper name (e.g., "myApp" -> "myApp_whitelist").
   * @param email - The email address of the user to add to the whitelist.
   * @returns A Promise that resolves when the operation is complete.
   */
  async addUserToWhitelistGroup(appType: string, email: string) {
    await this.ensureAdminAuthenticated();
    const users = await this.kcAdminClient.users.find({
      realm: this.realmName,
      email,
      exact: true,
    });
    if (!users || users.length === 0) {
      this.logger.log(
        `User (${email}) is not recorded in Keycloak. No action is required with the whitelist.`,
      );
      return;
    }
    const user = users[0];
    const groupName = `${appType}${WHITELIST_GROUP_SUFFIX}`;
    const potentialGroups = await this.kcAdminClient.groups.find({
      realm: this.realmName,
      search: groupName,
    });
    const targetGroup = potentialGroups.find((g) => g.name === groupName);
    if (targetGroup) {
      await this.kcAdminClient.users.addToGroup({
        realm: this.realmName,
        id: user.id,
        groupId: targetGroup.id,
      });
    }
  }

  /**
   * Removes a user from a whitelist group in Keycloak.
   * If the user exists directly in Keycloak, they are removed from the group named `${appType}_whitelist`.
   * If the user does not exist directly, their email is removed from the 'claims.value'
   *
   * @param {string} appType - The type of application, used to determine the whitelist group/mapper name (e.g., "myApp" -> "myApp_whitelist").
   * @param {string} email - The email address of the user to remove from the whitelist.
   * @returns {Promise<void>} A Promise that resolves when the operation is complete.
   */
  async userLeaveWhitelistGroup(appType: string, email: string): Promise<void> {
    await this.ensureAdminAuthenticated();
    const users = await this.kcAdminClient.users.find({
      realm: this.realmName,
      email,
      exact: true,
    });
    if (!users || users.length === 0) {
      this.logger.log(
        `User (${email}) is not recorded in Keycloak. No action is required with the whitelist.`,
      );
      return;
    }
    const user = users[0];
    const groupName = `${appType}${WHITELIST_GROUP_SUFFIX}`;
    const userGroups = await this.kcAdminClient.users.listGroups({
      realm: this.realmName,
      id: user.id,
    });
    const isMember = userGroups.find((ug) => ug.name === groupName);
    if (isMember) {
      await this.kcAdminClient.users.delFromGroup({
        realm: this.realmName,
        id: user.id,
        groupId: isMember.id,
      });
      this.logger.log(
        `User ${email} (ID: ${user.id}) successfully removed from group ${groupName}).`,
      );
    } else {
      this.logger.log(
        `User ${email} (ID: ${user.id}) was not a member of group ${groupName}  No action taken.`,
      );
    }
  }

  async updateHktSsoIdentityProviderWhitelistMapper(
    appType: string,
    email: string,
    type: 'add' | 'remove',
  ) {
    await this.ensureAdminAuthenticated();
    const mapperName = `${appType}${WHITELIST_GROUP_SUFFIX}`;
    const identityProviderAlias = 'hkt_sso'; // As per existing logic
    const mappers = await this.kcAdminClient.identityProviders.findMappers({
      alias: identityProviderAlias,
      realm: this.realmName,
    });
    const targetMapper = mappers.find((mapper) => mapper.name === mapperName);

    const currentClaimValue = JSON.parse(targetMapper?.config?.['claims']) || '';
    let emailsArray: string[] = [];

    if (currentClaimValue && currentClaimValue[0]?.value) {
      emailsArray = currentClaimValue[0]?.value
        .split('|')
        .map((e) => e.trim())
        .filter((e) => e);
    }

    if (type == 'add') {
      emailsArray.push(email);
    } else {
      emailsArray = emailsArray.filter((existingEmail) => existingEmail !== email);
    }
    if (currentClaimValue[0]) {
      currentClaimValue[0].value = emailsArray.join('|');
    }
    const mapperToUpdate = {
      id: targetMapper.id,
      name: targetMapper.name,
      identityProviderAlias: identityProviderAlias,
      identityProviderMapper: targetMapper.identityProviderMapper,
      config: {
        ...targetMapper.config,
        claims: JSON.stringify(currentClaimValue),
      },
    };
    await this.kcAdminClient.identityProviders.updateMapper(
      {
        realm: this.realmName,
        alias: identityProviderAlias,
        id: targetMapper.id,
      },
      mapperToUpdate,
    );
  }

  /**
   * Validates a Keycloak access token, checks its activity, and retrieves user email and ID.
   * This method is specifically for validating tokens and extracting necessary user information
   * for further processing, such as scope retrieval.
   * @async
   * @param {string} accessToken The access token to validate.
   * @returns {Promise<{ isActive: boolean; email: string | null; userId: string | null; username: string | null }>}
   * A Promise that resolves to an object containing:
   * - `isActive`: A boolean indicating if the token is active.
   * - `email`: The user's email address if the token is active and email is present, otherwise null.
   * - `userId`: The user's ID (subject from Keycloak token) if the token is active and ID is present, otherwise null.
   * - `username`: The user's username if the token is active and username is present, otherwise null.
   */
  async validateAndGetUserEmailFromToken(accessToken: string): Promise<{
    isActive: boolean;
    email: string | null;
    userId: string | null;
    username: string | null;
  }> {
    const introspectUrl = `${this.baseUrl}/realms/${this.realmName}/protocol/openid-connect/token/introspect`;
    const params = new URLSearchParams();
    params.append('token', accessToken);
    params.append('client_id', this.clientId);
    params.append('client_secret', this.clientSecret);
    const response = await this.keycloakClient.post<KeycloakIntrospectionResponse>(
      introspectUrl,
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
    const introspectionData = response.data;
    return {
      isActive: introspectionData.active,
      email: introspectionData.email || null,
      userId: introspectionData.sub || null,
      username: introspectionData.username || null,
    };
  }
}
