import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../providers/prisma/prisma.service';
import {
  CreateSchedulerJobDto,
  SchedulerJobWhereDto,
  UpdateSchedulerJobDto,
} from './scheduler-job.dto';
import { SchedulerJobQueueMapping } from './scheduler-job.constant';
import { UserRequest } from '../auth/auth.interface';
import { rrulestr } from 'rrule';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { GroupsService } from '../groups/groups.service';
import { FeatureType, SchedulerJobStatus } from '@prisma/client';
import { BullMqService } from 'src/providers/bullmq/bullmq.service';
import { SchedulerJobDto } from 'src/providers/bullmq/bullmq.dto';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
@Injectable()
export class SchedulerJobService {
  private logger = new Logger(SchedulerJobService.name);
  constructor(
    private prisma: PrismaService,
    private groupService: GroupsService,
    private bullMqService: BullMqService,
    private featureFlagService: FeatureFlagService,
  ) {}

  async getSchedulerJobs(params: {
    request: UserRequest;
    groupId?: number;
    skip?: number;
    take?: number;
    orderBy?: Record<string, 'asc' | 'desc'>;
    where?: SchedulerJobWhereDto;
    includeDeleted?: boolean;
  }) {
    const { groupId, skip, take, orderBy, where, request, includeDeleted } = params;
    const roles = await this.getRolesSeeAllSchedulerJob(groupId);
    if (!roles.includes(request.user.role)) {
      where['createdBy'] = request.user.id;
    }
    if (!includeDeleted) {
      where['status'] = { not: SchedulerJobStatus.DELETED };
    }
    return await this.prisma.schedulerJob.findMany({
      include: {
        creator: {
          select: {
            name: true,
          },
        },
        updator: {
          select: {
            name: true,
          },
        },
      },
      where: {
        groupId,
        ...where,
      },
      skip,
      take,
      orderBy,
    });
  }

  async getSchedulerJobsCount(
    request: UserRequest,
    groupId: number,
    where: SchedulerJobWhereDto,
    includeDeleted?: boolean,
  ) {
    const roles = await this.getRolesSeeAllSchedulerJob(groupId);
    if (!roles.includes(request.user.role)) {
      where['createdBy'] = request.user.id;
    }
    if (!includeDeleted) {
      where['status'] = { not: SchedulerJobStatus.DELETED };
    }
    return this.prisma.schedulerJob.count({
      where: {
        groupId,
        ...where,
      },
    });
  }

  async createSchedulerJob(
    groupId: number,
    createSchedulerJobDto: CreateSchedulerJobDto,
    request: UserRequest,
  ) {
    // check scheduler job rrule
    const result = this.checkRuleInvalid(createSchedulerJobDto.schedulerJobRRule);
    if (result) {
      this.logger.error(
        `[SchedulerJobService][createSchedulerJob] invalid rule: ${createSchedulerJobDto.schedulerJobRRule}`,
      );
      throw new ApiException(ErrorCode.SCHEDULER_JOB_CONFIG_ERROR);
    }
    // check group existence
    await this.groupService.getGroup(groupId, {});
    try {
      // compose scheduler job table data
      const ds = rrulestr(createSchedulerJobDto.schedulerJobRRule).toText();
      const description = ds.replace('(~ approximate)', '');
      // save
      const schedulerJob = await this.prisma.schedulerJob.create({
        data: {
          schedulerJobId: `${
            SchedulerJobQueueMapping[createSchedulerJobDto.featureType]
          }-${groupId}-${createSchedulerJobDto.featureId}`,
          name: createSchedulerJobDto.name,
          description,
          featureId: createSchedulerJobDto.featureId,
          groupId,
          featureType: FeatureType[createSchedulerJobDto.featureType],
          status: SchedulerJobStatus.ACTIVE,
          createdBy: request.user.id,
          updatedBy: request.user.id,
          schedulerJobRRule: createSchedulerJobDto.schedulerJobRRule,
        },
      });

      // call job queue to create scheduler job
      const schedulerJobInfo = {
        schedulerJobId: schedulerJob.schedulerJobId,
        queueName: SchedulerJobQueueMapping[createSchedulerJobDto.featureType],
        jobConfig: schedulerJob.schedulerJobRRule,
        jobData: {
          id: schedulerJob.id,
          featureId: schedulerJob.featureId,
          groupId,
          createdBy: schedulerJob.createdBy,
          updatedBy: schedulerJob.updatedBy,
        },
      } as SchedulerJobDto;
      await this.bullMqService.createSchedulerJob(schedulerJobInfo);
      return schedulerJob;
    } catch (error) {
      this.logger.error(error, 'Failed to create scheduler job');
      throw new ApiException(ErrorCode.CREATE_SCHEDULER_JOB_ERROR);
    }
  }

  async updateSchedulerJob(
    id: number,
    updateSchedulerJobDto: UpdateSchedulerJobDto,
    request: UserRequest,
  ) {
    // check scheduler job rrule
    const result = this.checkRuleInvalid(updateSchedulerJobDto.schedulerJobRRule);
    if (result) {
      this.logger.error(
        `[SchedulerJobService][updateSchedulerJob] invalid rule: ${updateSchedulerJobDto.schedulerJobRRule}`,
      );
      throw new ApiException(ErrorCode.SCHEDULER_JOB_CONFIG_ERROR);
    }

    // find scheduler job
    const schedulerJob = await this.prisma.schedulerJob.findUnique({
      where: { id },
    });
    if (!schedulerJob) {
      throw new ApiException(ErrorCode.SCHEDULER_JOB_NOT_FOUND);
    }
    // only creator can update
    if (schedulerJob.createdBy !== request.user.id) {
      throw new ApiException(ErrorCode.CREATOR_ERROR);
    }
    try {
      // update
      const ds = rrulestr(updateSchedulerJobDto.schedulerJobRRule).toText();
      const description = ds.replace('(~ approximate)', '');
      const update = await this.prisma.schedulerJob.update({
        where: { id },
        data: {
          ...updateSchedulerJobDto,
          description,
          status: SchedulerJobStatus.ACTIVE,
          updatedBy: request.user.id,
        },
      });

      // call job queue to update scheduler job
      const schedulerJobInfo = {
        schedulerJobId: schedulerJob.schedulerJobId,
        queueName: SchedulerJobQueueMapping[schedulerJob.featureType],
        jobConfig: updateSchedulerJobDto.schedulerJobRRule,
        jobData: {
          id: schedulerJob.id,
          featureId: schedulerJob.featureId,
          groupId: schedulerJob.groupId,
          createdBy: schedulerJob.createdBy,
          updatedBy: schedulerJob.updatedBy,
        },
      } as SchedulerJobDto;

      await this.bullMqService.updateSchedulerJob(schedulerJobInfo);
      return update;
    } catch (error) {
      this.logger.error(error, 'Failed to update scheduler job');
      throw new ApiException(ErrorCode.UPDATE_SCHEDULER_JOB_ERROR);
    }
  }

  async deleteSchedulerJob(id: number, request: UserRequest) {
    // find scheduler job
    const schedulerJob = await this.prisma.schedulerJob.findUnique({
      where: { id },
    });
    if (!schedulerJob) {
      throw new ApiException(ErrorCode.SCHEDULER_JOB_NOT_FOUND);
    }
    // only creator can update
    if (schedulerJob.createdBy !== request.user.id) {
      throw new ApiException(ErrorCode.CREATOR_ERROR);
    }
    try {
      // delete
      await this.prisma.schedulerJob.update({
        where: { id },
        data: {
          status: SchedulerJobStatus.DELETED,
          updatedBy: request.user.id,
          deletedAt: new Date(),
        },
      });
      // call job queue to delete scheduler job
      await this.bullMqService.removeSchedulerJob(
        SchedulerJobQueueMapping[schedulerJob.featureType],
        schedulerJob.schedulerJobId,
      );
      return { status: 'success' };
    } catch (error) {
      this.logger.error(error, 'Failed to delete scheduler job');
      throw new ApiException(ErrorCode.DELETE_SCHEDULER_JOB_ERROR);
    }
  }

  async updateSchedulerJobStatus(id: number, status: SchedulerJobStatus, request: UserRequest) {
    // find scheduler job
    const schedulerJob = await this.prisma.schedulerJob.findUnique({
      where: { id },
    });
    if (!schedulerJob) {
      throw new ApiException(ErrorCode.SCHEDULER_JOB_NOT_FOUND);
    }
    // only creator can update
    if ('api-key' !== request.user.type && schedulerJob.createdBy !== request.user.id) {
      throw new ApiException(ErrorCode.CREATOR_ERROR);
    }
    try {
      switch (status) {
        case SchedulerJobStatus.ACTIVE:
          if (this.checkRuleInvalid(schedulerJob.schedulerJobRRule)) {
            this.logger.error(
              `[SchedulerJobService][updateSchedulerJobStatus] invalid rule: ${schedulerJob.schedulerJobRRule}`,
            );
            throw new ApiException(ErrorCode.SCHEDULER_JOB_CONFIG_ERROR);
          }
          await this.prisma.schedulerJob.update({
            where: { id },
            data: {
              status,
              updatedBy: request.user.id,
            },
          });
          // call job queue to update scheduler job
          await this.bullMqService.updateSchedulerJob({
            schedulerJobId: schedulerJob.schedulerJobId,
            queueName: SchedulerJobQueueMapping[schedulerJob.featureType],
            jobConfig: schedulerJob.schedulerJobRRule,
            jobData: {
              id: schedulerJob.id,
              featureId: schedulerJob.featureId,
              groupId: schedulerJob.groupId,
              createdBy: schedulerJob.createdBy,
              updatedBy: schedulerJob.updatedBy,
            },
          });
          return { status: 'success' };
        case SchedulerJobStatus.PAUSED:
          await this.prisma.schedulerJob.update({
            where: { id },
            data: {
              status,
              updatedBy: request.user.id,
            },
          });
          // call job queue to delete scheduler job
          await this.bullMqService.removeSchedulerJob(
            SchedulerJobQueueMapping[schedulerJob.featureType],
            schedulerJob.schedulerJobId,
          );
          return { status: 'success' };
        case SchedulerJobStatus.COMPLETED:
          await this.prisma.schedulerJob.update({
            where: { id },
            data: {
              status,
              updatedBy: request.user.id,
            },
          });
          return { status: 'success' };
        default:
          throw new ApiException(ErrorCode.INVALID_SCHEDULER_JOB_STATUS);
      }
    } catch (error) {
      this.logger.error(error, 'Failed to update scheduler job status');
      throw new ApiException(ErrorCode.UPDATE_SCHEDULER_JOB_ERROR);
    }
  }

  private checkRuleInvalid(rule: string) {
    const rrule = rrulestr(rule);
    const next_occurrence = rrule.after(new Date(), false);
    if (!next_occurrence) return true;
    return false;
  }

  private async getRolesSeeAllSchedulerJob(groupId?: number) {
    const { metaData } = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.CONFIG_ROLES_SEE_ALL_SCHEDULER_JOB,
    );
    const roles = metaData?.['roles'];
    if (Array.isArray(roles)) {
      return roles as string[];
    }
    return [roles];
  }
}
