import { LlmEngineType } from "@prisma/client";

export interface ResourceQuotaRuleSeed {
  ruleKey: string;
  description: string;
  quotaType: string;
  link?:  'LLM_ENGINE' | 'CHAT_CHANNEL';
  engineType?: LlmEngineType;
}

export const resourceQuotaRules: ResourceQuotaRuleSeed[] = [
  {
    ruleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    description: 'To declare maximum token usage of {{engine}} per month',
    quotaType: 'TokenLimitPerMonth',
    link: 'LLM_ENGINE',
    engineType: LlmEngineType.TEXT,
  },
  {
    ruleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    description: 'To declare maximum image number usage of {{engine}} per month',
    quotaType: 'ImageNumberLimitPerMonth',
    link: 'LLM_ENGINE',
    engineType: LlmEngineType.IMAGE,
  },
  {
    ruleKey: 'group-{groupId}:rate-limit-{{channel}}-quota',
    description: 'To declare maximum chat number per hour through {{channel}}',
    quotaType: 'RateLimit',
    link: 'CHAT_CHANNEL',
  },
];
