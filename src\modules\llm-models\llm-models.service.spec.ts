import { Test, TestingModule } from '@nestjs/testing';
import { LLMModelsService } from './llm-models.service';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { LLMBackendService } from '../../providers/llm-backend/llm-backend.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { MailService } from 'src/providers/mail/mail.service';
import { ConfigService } from '@nestjs/config';
import { ChatFilesService } from '../chat-files/chat-files.service';
import { S3Service } from 'src/providers/s3/s3.service';
import { UsersService } from '../users/users.service';
import { ApiKeysService } from '../api-keys/api-keys.service';
import { ApiResourceService } from '../api-resource/api-resource.service';
import { FlowBotsService } from '../flow-bots/flow-bots.service';
import { ElasticSearchService } from 'src/providers/elasticsearch/elasticsearch.service';
import { FortiSanboxService } from 'src/providers/fortisandbox/fortisandbox.service';
import { ChatSessionsService } from '../chat-sessions/chat-sessions.service';
import { GroupNotificationService } from '../group-notification/group-notification.service';
import { QueueService } from 'src/providers/queue/queue.service';
import { LabelsService } from '../labels/labels.service';
import { GroupsService } from '../groups/groups.service';
import { GCSService } from '../gcs/gcs.service';
import { RedisService } from 'src/providers/redis/redis.service';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';
import { Prisma, LLMModel, LlmEngine } from '@prisma/client';
import { LLMModeResponse } from '../../providers/llm-backend/llm-backend.interface';

describe('LLMModelsService', () => {
  let service: LLMModelsService;
  let llmBackendService: DeepMockProxy<LLMBackendService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LLMModelsService,
        { provide: PrismaService, useValue: mockDeep<PrismaService>() },
        { provide: LLMBackendService, useValue: mockDeep<LLMBackendService>() },
        { provide: FeatureFlagService, useValue: mockDeep<FeatureFlagService>() },
        { provide: MailService, useValue: mockDeep<MailService>() },
        { provide: ConfigService, useValue: mockDeep<ConfigService>() },
        { provide: ChatFilesService, useValue: mockDeep<ChatFilesService>() },
        { provide: S3Service, useValue: mockDeep<S3Service>() },
        { provide: UsersService, useValue: mockDeep<UsersService>() },
        { provide: ApiKeysService, useValue: mockDeep<ApiKeysService>() },
        { provide: ApiResourceService, useValue: mockDeep<ApiResourceService>() },
        { provide: FlowBotsService, useValue: mockDeep<FlowBotsService>() },
        { provide: ElasticSearchService, useValue: mockDeep<ElasticSearchService>() },
        { provide: FortiSanboxService, useValue: mockDeep<FortiSanboxService>() },
        { provide: ChatSessionsService, useValue: mockDeep<ChatSessionsService>() },
        { provide: GroupNotificationService, useValue: mockDeep<GroupNotificationService>() },
        { provide: QueueService, useValue: mockDeep<QueueService>() },
        { provide: LabelsService, useValue: mockDeep<LabelsService>() },
        { provide: GroupsService, useValue: mockDeep<GroupsService>() },
        { provide: GCSService, useValue: mockDeep<GCSService>() },
        { provide: RedisService, useValue: mockDeep<RedisService>() },
      ],
    }).compile();

    service = module.get<LLMModelsService>(LLMModelsService);
    llmBackendService = module.get(LLMBackendService);
  });

  describe('update', () => {
    it('should update a model successfully', async () => {
      const modelId = 'test-model';
      const userId = 1;
      const llmEngineId = 2;
      const mockModel = {
        id: 1,
        modelId,
        groupId: 1,
        llmEngineId: 1,
        parameters: { dataSource: 'test', llmParams: { max_tokens: 100 } },
        group: { env: 'TEST' },
      } as unknown as LLMModel & { group: { env: string } };

      const mockEngine = { id: llmEngineId, slug: 'new-engine' } as LlmEngine;

      const tx = {
        lLMModel: {
          findFirst: jest.fn().mockResolvedValue(mockModel),
          update: jest.fn().mockResolvedValue({ ...mockModel, llmEngineId }),
        },
        llmEngine: {
          findUnique: jest.fn().mockResolvedValue(mockEngine),
        },
        messageTemplate: {
          update: jest.fn(),
        },
      } as unknown as Prisma.TransactionClient;

      llmBackendService.updateModelTonesOrStartupMessage.mockResolvedValue({} as LLMModeResponse);

      const result = await service.update(
        modelId,
        'new tone',
        'new startup message',
        llmEngineId,
        'new type definition',
        true,
        userId,
        { newParam: 'value' },
        tx,
        1,
      );

      expect(tx.lLMModel.findFirst).toHaveBeenCalledWith({
        where: { modelId },
        include: { group: true, lastModifiedBy: true },
      });
      expect(tx.llmEngine.findUnique).toHaveBeenCalledWith({ where: { id: llmEngineId } });
      expect(llmBackendService.updateModelTonesOrStartupMessage).toHaveBeenCalledWith(
        'TEST',
        modelId,
        'new tone',
        'new startup message',
      );
      expect(tx.messageTemplate.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { used: { increment: 1 } },
      });
      expect(tx.lLMModel.update).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should reset llmParams when llmEngineId changes', async () => {
      const modelId = 'test-model';
      const userId = 1;
      const llmEngineId = 2;
      const mockModel = {
        id: 1,
        modelId,
        groupId: 1,
        llmEngineId: 1,
        parameters: { dataSource: 'test', llmParams: { max_tokens: 100 } },
        group: { env: 'TEST' },
      } as unknown as LLMModel & { group: { env: string } };

      const mockEngine = { id: llmEngineId, slug: 'new-engine' } as LlmEngine;

      const tx = {
        lLMModel: {
          findFirst: jest.fn().mockResolvedValue(mockModel),
          update: jest.fn().mockResolvedValue({ ...mockModel, llmEngineId }),
        },
        llmEngine: {
          findUnique: jest.fn().mockResolvedValue(mockEngine),
        },
        messageTemplate: {
          update: jest.fn(),
        },
      } as unknown as Prisma.TransactionClient;

      llmBackendService.updateModelTonesOrStartupMessage.mockResolvedValue({} as LLMModeResponse);

      await service.update(
        modelId,
        'new tone',
        'new startup message',
        llmEngineId,
        'new type definition',
        true,
        userId,
        mockModel.parameters,
        tx,
        1,
      );

      expect(tx.lLMModel.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            parameters: expect.objectContaining({
              llmParams: undefined, // llmParams should be reset
            }),
          }),
        }),
      );
    });

    it('should reset llmParams when llmEngineId changes and parameters are undefined', async () => {
      const modelId = 'test-model';
      const userId = 1;
      const llmEngineId = 2;
      const mockModel = {
        id: 1,
        modelId,
        groupId: 1,
        llmEngineId: 1,
        parameters: { dataSource: 'test', llmParams: { max_tokens: 100 } },
        group: { env: 'TEST' },
      } as unknown as LLMModel & { group: { env: string } };

      const mockEngine = { id: llmEngineId, slug: 'new-engine' } as LlmEngine;

      const tx = {
        lLMModel: {
          findFirst: jest.fn().mockResolvedValue(mockModel),
          update: jest.fn().mockResolvedValue({ ...mockModel, llmEngineId }),
        },
        llmEngine: {
          findUnique: jest.fn().mockResolvedValue(mockEngine),
        },
        messageTemplate: {
          update: jest.fn(),
        },
      } as unknown as Prisma.TransactionClient;

      llmBackendService.updateModelTonesOrStartupMessage.mockResolvedValue({} as LLMModeResponse);

      await service.update(
        modelId,
        'new tone',
        'new startup message',
        llmEngineId,
        'new type definition',
        true,
        userId,
        undefined,
        tx,
        1,
      );

      expect(tx.lLMModel.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            parameters: expect.objectContaining({
              llmParams: undefined, // llmParams should be reset
            }),
          }),
        }),
      );
    });

    it('should not update dataSource when parameters is null', async () => {
      const modelId = 'test-model';
      const userId = 1;
      const llmEngineId = 1; // Same engine
      const mockModel = {
        id: 1,
        modelId,
        groupId: 1,
        llmEngineId: 1,
        parameters: { dataSource: 'test', llmParams: { max_tokens: 100 } },
        group: { env: 'TEST' },
      } as unknown as LLMModel & { group: { env: string } };

      const tx = {
        lLMModel: {
          findFirst: jest.fn().mockResolvedValue(mockModel),
          update: jest.fn().mockResolvedValue({ ...mockModel, llmEngineId }),
        },
        llmEngine: {
          findUnique: jest.fn(),
        },
        messageTemplate: {
          update: jest.fn(),
        },
      } as unknown as Prisma.TransactionClient;

      llmBackendService.updateModelTonesOrStartupMessage.mockResolvedValue({} as LLMModeResponse);

      const result = await service.update(
        modelId,
        'new tone',
        'new startup message',
        llmEngineId,
        'new type definition',
        true,
        userId,
        null,
        tx,
        1,
      );

      expect(tx.lLMModel.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            parameters: expect.objectContaining({
              dataSource: 'test', // dataSource should not change
            }),
          }),
        }),
      );
    });

    it('should reset llmParams and not update dataSource when parameters are null and llmEngineId is changed', async () => {
      const modelId = 'test-model';
      const userId = 1;
      const llmEngineId = 2; // Different engine
      const mockModel = {
        id: 1,
        modelId,
        groupId: 1,
        llmEngineId: 1,
        parameters: { dataSource: 'test', llmParams: { max_tokens: 100 } },
        group: { env: 'TEST' },
      } as unknown as LLMModel & { group: { env: string } };

      const mockEngine = { id: llmEngineId, slug: 'new-engine' } as LlmEngine;

      const tx = {
        lLMModel: {
          findFirst: jest.fn().mockResolvedValue(mockModel),
          update: jest.fn().mockResolvedValue({ ...mockModel, llmEngineId }),
        },
        llmEngine: {
          findUnique: jest.fn().mockResolvedValue(mockEngine),
        },
        messageTemplate: {
          update: jest.fn(),
        },
      } as unknown as Prisma.TransactionClient;

      llmBackendService.updateModelTonesOrStartupMessage.mockResolvedValue({} as LLMModeResponse);

      const result = await service.update(
        modelId,
        'new tone',
        'new startup message',
        llmEngineId,
        'new type definition',
        true,
        userId,
        null,
        tx,
        1,
      );

      expect(tx.lLMModel.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            parameters: expect.objectContaining({
              dataSource: 'test', // dataSource should not change
              llmParams: undefined, // llmParams should be reset
            }),
          }),
        }),
      );
    });

    it('should not reset llmParams when llmEngineId does not change and parameters are undefined', async () => {
      const modelId = 'test-model';
      const userId = 1;
      const llmEngineId = 1; // Engine ID is the same
      const mockModel = {
        id: 1,
        modelId,
        groupId: 1,
        llmEngineId: 1,
        parameters: { dataSource: 'test', llmParams: { max_tokens: 100 } },
        group: { env: 'TEST' },
      } as unknown as LLMModel & { group: { env: string } };

      const tx = {
        lLMModel: {
          findFirst: jest.fn().mockResolvedValue(mockModel),
          update: jest.fn().mockResolvedValue({ ...mockModel }),
        },
        llmEngine: {
          findUnique: jest.fn(),
        },
        messageTemplate: {
          update: jest.fn(),
        },
      } as unknown as Prisma.TransactionClient;

      llmBackendService.updateModelTonesOrStartupMessage.mockResolvedValue({} as LLMModeResponse);

      const result = await service.update(
        modelId,
        'new tone',
        'new startup message',
        llmEngineId,
        'new type definition',
        true,
        userId,
        undefined, // Parameters are undefined
        tx,
        1,
      );

      expect(tx.lLMModel.update).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            parameters: undefined, // llmParams should be reset
          }),
        }),
      );
    });
  });
});
