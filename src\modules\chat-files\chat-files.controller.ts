import {
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express/multer';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { ChatFile, ChatFileType } from '@prisma/client';
import { Response } from 'express';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { ChatFilesService } from './chat-files.service';
import { ChatFilesApproach, ChatFilesConfigResponse, ChatFilesResponse } from './chat-files.dto';
import { ExternalApi } from 'src/swagger-document';
import { ChatFileEntity } from './entities/chat-files.entity';
import { Public } from '../auth/public.decorator';
import { Readable } from 'stream';

@Controller('groups/:groupId/chat-files')
@ApiBearerAuth('bearer-auth')
@ApiSecurity('x-api-key')
@ApiTags('Chat File')
export class ChatFilesController {
  constructor(private readonly chatFilesService: ChatFilesService) {}

  @Get()
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground')
  @ApiOperation({
    summary: 'List chat files',
    description: 'List chat files',
    tags: ['Chat File'],
    operationId: 'listChatFiles',
  })
  @ApiQuery({
    name: 'chatApproach',
    required: true,
    description: 'chat approach',
    enum: ChatFilesApproach,
    examples: {
      CWF: {
        summary: 'Chat with file',
        description: 'Chat with file',
        value: ChatFilesApproach.CWF,
      },
      CWD: {
        summary: 'Chat with data',
        description: 'Chat with data',
        value: ChatFilesApproach.CWD,
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: ChatFilesResponse,
  })
  async getFiles(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('chatApproach') chatFilesApproach?: ChatFilesApproach,
  ): Promise<ChatFilesResponse> {
    // TODO :: when frontend change the code to support CWD need remove this and update chatFilesApproach to required
    //const chatApproach = chatFilesApproach ?? ChatFilesApproach.CWF;
    return this.chatFilesService.getFiles(
      groupId,
      request.user.id,
      ChatFileType.QUESTION,
      chatFilesApproach,
    );
  }

  @Get('configuration')
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground')
  @ApiOperation({
    summary: 'Get chat files configuration',
    description: 'Get chat files configuration',
    tags: ['Chat File'],
    operationId: 'getChatFilesConfig',
  })
  @ApiQuery({
    name: 'chatApproach',
    required: true,
    description: 'chat approach',
    enum: ChatFilesApproach,
    examples: {
      CWF: {
        summary: 'Chat with file',
        description: 'Chat with file',
        value: ChatFilesApproach.CWF,
      },
      CWD: {
        summary: 'Chat with data',
        description: 'Chat with data',
        value: ChatFilesApproach.CWD,
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: ChatFilesConfigResponse,
  })
  async getFilesConfig(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('chatApproach') chatFilesApproach?: ChatFilesApproach,
  ): Promise<ChatFilesConfigResponse> {
    // TODO :: when frontend change the code to support CWD need remove this and update chatFilesApproach to required
    const chatApproach = chatFilesApproach ?? ChatFilesApproach.CWF;
    return this.chatFilesService.getChatFileConfig(groupId, chatApproach);
  }

  @Get('download')
  @Scopes('group-{groupId}:read-playground')
  @AuditLog('download-chat-file')
  @ApiOperation({
    summary: 'Download chat file',
    description: 'Download chat file',
    tags: ['Chat File'],
    operationId: 'getChatFile',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async downloadFile(
    @Req() request: UserRequest,
    @Res() response: Response,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('chatFileId', OptionalIntPipe) chatFileId?: number,
    @Query('s3Basename') s3Basename?: string,
    @Query('s3Filename') s3Filename?: string,
  ): Promise<void> {
    const fileResponse = await this.chatFilesService.downloadFile(
      groupId,
      request.user.id,
      chatFileId,
      s3Basename,
      s3Filename,
    );
    (fileResponse.Body as Readable).on('error', function () {
        response.status(404).json(new ApiException(ErrorCode.FIlE_NOT_FOUND).getResponse());
      })
      .pipe(response);
  }

  @Post()
  @ExternalApi()
  @Scopes('group-{groupId}:upload-chat-files')
  @UseInterceptors(FileInterceptor('file'))
  @UseInterceptors(ClassSerializerInterceptor)
  @ApiOperation({
    summary: 'Upload chat file (for chat with file or chat with data)',
    description: 'Upload chat file',
    tags: ['Chat File'],
    operationId: 'uploadChatFile',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiQuery({
    name: 'chatApproach',
    required: true,
    description: 'chat approach',
    enum: ChatFilesApproach,
    examples: {
      CWF: {
        summary: 'Chat with file',
        description: 'Chat with file',
        value: ChatFilesApproach.CWF,
      },
      CWD: {
        summary: 'Chat with data',
        description: 'Chat with data',
        value: ChatFilesApproach.CWD,
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'OK',
    type: ChatFileEntity,
  })
  async uploadFile(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @UploadedFile() file: Express.Multer.File,
    @Query('chatApproach') chatFilesApproach?: ChatFilesApproach,
    @Query('type') type?: ChatFileType,
  ): Promise<ChatFile> {
    await this.chatFilesService.monitorChatFileSizeLog(
      'external',
      ChatFileType.QUESTION,
      file,
      groupId,
      request.user.id,
    );
    // TODO :: when frontend change the code to support CWD need remove this and update chatFilesApproach to required
    const chatApproach = chatFilesApproach ?? ChatFilesApproach.CWF;
    return this.chatFilesService.uploadFile(
      groupId,
      request.user.id,
      file,
      chatApproach,
      ChatFileType.QUESTION,
      request.user.type,
    );
  }

  @Post('/internal')
  @Scopes('upload-chat-files-internal')
  @UseInterceptors(FileInterceptor('file'))
  async uploadInternalChatFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @UploadedFile() file: Express.Multer.File,
    @Query('type') type: ChatFileType,
    @Query('userId') userId: number,
    @Query('chatApproach') chatApproach: ChatFilesApproach,
    @Query('isPublic') isPublic: boolean,
  ): Promise<any> {
    await this.chatFilesService.monitorChatFileSizeLog('internal', type, file, groupId, userId);
    return await this.chatFilesService.uploadInternalChatFile(
      groupId,
      userId,
      file,
      chatApproach,
      type,
      isPublic,
    );
  }

  @Delete(':chatFileId')
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground')
  @ApiOperation({
    summary: 'Delete chat file',
    description: 'Delete chat file',
    tags: ['Chat File'],
    operationId: 'deleteChatFile',
  })
  @ApiParam({
    name: 'chatFileId',
    description: 'File id of chat file',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
    type: ChatFileEntity,
  })
  async deleteFile(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('chatFileId', ParseIntPipe) chatFileId: number,
  ): Promise<ChatFile> {
    return this.chatFilesService.deleteFile(groupId, request.user.id, chatFileId);
  }

  @Get(['image/:chatFileToken', 'generated/:chatFileToken'])
  @Public()
  async downloadPublicFile(
    @Res() response: Response,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('chatFileToken') chatFileToken: string,
  ) {
    const fileResponse = await this.chatFilesService.downloadGeneratedPublicFile(
      groupId,
      chatFileToken,
    );
    (fileResponse.Body as Readable).on('httpHeaders', (statusCode, headers) => {
        const filename = Buffer.from(headers['x-amz-meta-filename'], 'base64').toString('utf-8');

        response.setHeader(
          'Content-Disposition',
          `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`,
        );
        response.setHeader('Content-Type', headers['content-type']);
        response.setHeader('Content-Length', headers['content-length']);
      })
      .on('error', function () {
        response.status(404).json(new ApiException(ErrorCode.FIlE_NOT_FOUND).getResponse());
      })
      .pipe(response);
  }

  @Post('re-scan/:chatFileId')
  @HttpCode(200)
  @Scopes('group-{groupId}:upload-chat-files')
  async reScanFile(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('chatFileId') chatFileId: number,
  ) {
    return this.chatFilesService.reScanFile(groupId, request.user.id, chatFileId);
  }
}
