import { Module } from '@nestjs/common';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { FeatureFlagController } from './feature-flags.controller';
import { FeatureFlagService } from './feature-flags.service';
import { RedisModule } from 'src/providers/redis/redis.module';
import { CategoryModule } from '../category/category.module';
import { LabelsModule } from '../labels/labels.module';

@Module({
  imports: [PrismaModule, RedisModule, CategoryModule, LabelsModule],
  controllers: [FeatureFlagController],
  providers: [FeatureFlagService],
  exports: [FeatureFlagService],
})
export class FeatureFlagModule {}
