import { ArgumentMetadata, PipeTransform, Injectable } from '@nestjs/common';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Injectable()
export class OptionalParseEnumPipe<T extends object> implements PipeTransform {
  constructor(private readonly enumType: T) {}

  transform(value: unknown, metadata: ArgumentMetadata): T[keyof T] | undefined {
    if (value === undefined || value === null) {
      return undefined;
    }

    if (!Object.values(this.enumType).includes(value)) {
      throw new ApiException(
        ErrorCode.INVALID_ENUM_VALUE.replace('{paramName}', metadata.data).replace(
          '{enumValues}',
          Object.values(this.enumType).join(', '),
        ),
      );
    }

    return value as T[keyof T];
  }
}
