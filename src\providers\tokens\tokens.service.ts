import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { async as cryptoRandomString } from 'crypto-random-string';
import { decode, DecodeOptions, sign, SignOptions, verify, VerifyOptions } from 'jsonwebtoken';
import { v4 } from 'uuid';
import { ApiException, ErrorCode } from '../../errors/errors.constants';

@Injectable()
export class TokensService {
  constructor(private configService: ConfigService) {}

  /**
   * Sign a JWT
   * @param subject - Subject
   * @param payload - Object payload
   * @param expiresIn - Expiry string (vercel/ms)
   * @param options - Signing options
   */
  signJwt(
    subject: string,
    payload: number | string | object | Buffer,
    expiresIn?: string | number,
    options?: SignOptions,
  ) {
    if (typeof payload === 'number') payload = payload.toString();
    return sign(
      payload,
      this.configService.get<string>('security.privateKey') ?? '', //@@@
      {
        ...options,
        subject,
        expiresIn,
      },
    );
  }

  /**
   * Verify and decode a JWT
   * @param subject - Subject
   * @param token - JWT
   * @param options - Verify options
   */
  verify<T>(subject: string, token: string, options?: VerifyOptions) {
    try {
      return verify(token, this.configService.get<string>('security.publicKey') ?? '', {
        ...options,
        subject,
      }) as any as T;
    } catch (error) {
      throw new ApiException(ErrorCode.INVALID_TOKEN);
    }
  }

  /**
   * @param token - JWT
   * @param options - Decode options
   */
  decode<T>(token: string, options?: DecodeOptions) {
    return decode(token, options) as T;
  }

  /**
   * Generate a UUID
   */
  generateUuid() {
    return v4();
  }

  /**
   * Generate a cryptographically strong random string
   * @param length - Length of returned string
   * @param charactersOrType - Characters or one of the supported types
   */
  async generateRandomString(length = 32, charactersOrType = 'alphanumeric'): Promise<string> {
    if (
      [
        'hex',
        'base64',
        'url-safe',
        'numeric',
        'distinguishable',
        'ascii-printable',
        'alphanumeric',
      ].includes(charactersOrType)
    )
      return cryptoRandomString({
        length,
        type: charactersOrType as
          | 'hex'
          | 'base64'
          | 'url-safe'
          | 'numeric'
          | 'distinguishable'
          | 'ascii-printable'
          | 'alphanumeric',
      });
    return cryptoRandomString({ length, characters: charactersOrType });
  }

  /**
   * Verify wikijs and decode a JWT
   * @param token - JWT
   * @param options - Verify options
   */
  verifyWikijs<T>(token: string, options?: VerifyOptions) {
    try {
      const issuer = this.configService.get<string>('wikijs.token.issuer') ?? '';
      return verify(token, this.configService.get<string>('wikijs.token.publicKey') ?? '', {
        ...options,
        issuer,
      }) as any as T;
    } catch (error) {
      throw new ApiException(ErrorCode.INVALID_TOKEN);
    }
  }
}
