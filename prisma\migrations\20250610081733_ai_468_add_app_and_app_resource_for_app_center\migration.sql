-- CreateEnum
CREATE TYPE "AppType" AS ENUM ('BOT_TOOL', 'INDEPENDENCE');

-- <PERSON>reate<PERSON>num
CREATE TYPE "LaunchType" AS ENUM ('REDIRECT', 'NEW_TAG');

-- CreateEnum
CREATE TYPE "AppResourceStatus" AS ENUM ('CREATED', 'PROCESSING', 'UNKNOWN');

-- CreateTable
CREATE TABLE "App" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "tokenCookiesName" TEXT NOT NULL,
    "appType" "AppType" NOT NULL,
    "launchType" "LaunchType" NOT NULL,
    "launchBaseUrl" TEXT NOT NULL,
    "createParamConfig" JSONB NOT NULL,
    "needCreateKeyCloakResource" BOOLEAN NOT NULL,

    CONSTRAINT "App_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AppResource" (
    "id" SERIAL NOT NULL,
    "appId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" "AppResourceStatus" NOT NULL,
    "groupId" INTEGER NOT NULL,
    "launchUrl" TEXT NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "metaData" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AppResource_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "App_name_key" ON "App"("name");

-- CreateIndex
CREATE INDEX "App_name_idx" ON "App"("name");
