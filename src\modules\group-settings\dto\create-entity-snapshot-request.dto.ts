import { ApiProperty } from '@nestjs/swagger';
import { SnapshotEntityType } from '@prisma/client';
import { IsEnum, IsString } from 'class-validator';

export class CreateGroupSettingsSnapshotRequestDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: SnapshotEntityType })
  @IsEnum(SnapshotEntityType, { each: true })
  entityType: SnapshotEntityType[];

  @ApiProperty()
  groupId: number;

  @ApiProperty({ enum: SnapshotEntityType })
  @IsEnum(SnapshotEntityType)
  snapshotType: SnapshotEntityType;
}
