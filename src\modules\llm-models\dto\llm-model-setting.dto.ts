import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class LLMModelSettingResponse {
  @IsString()
  @IsNotEmpty()
  id: number;

  @IsString()
  @IsNotEmpty()
  modelId: string;

  @IsBoolean()
  @IsOptional()
  showInTeams?: boolean;

  @IsBoolean()
  @IsOptional()
  showRefInTeams?: boolean;

  @IsBoolean()
  @IsOptional()
  makeLiveToPublic?: boolean;

  @IsOptional()
  @IsBoolean()
  active: boolean;

  @IsOptional()
  @IsBoolean()
  canShareChat?: boolean;

  @IsBoolean()
  @IsOptional()
  showInOutLook?: boolean;
}

export class UpdateLLMModelSettingDto {
  @IsString()
  @IsNotEmpty()
  modelId: string;

  @IsBoolean()
  @IsOptional()
  showInTeams?: boolean;

  @IsBoolean()
  @IsOptional()
  showRefInTeams?: boolean;

  @IsBoolean()
  @IsOptional()
  makeLiveToPublic?: boolean;

  @IsOptional()
  @IsBoolean()
  active: boolean;

  @IsOptional()
  @IsBoolean()
  canShareChat?: boolean;

  @IsBoolean()
  @IsOptional()
  showInOutLook?: boolean;
}
