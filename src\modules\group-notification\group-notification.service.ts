import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { MailService } from '../../providers/mail/mail.service';
import { NotificationBackendService } from '../../providers/notification-backend/notification-backend.service';
import { RedisService } from '../../providers/redis/redis.service';
import {
  FileHistory,
  GroupNotification,
  GroupNotificationConfig,
  GroupNotificationRecipientRole,
  GroupNotificationType,
  Prisma,
  SystemName,
  GroupType,
  FeatureFlagTargetType,
} from '@prisma/client';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import {
  NotificationContent,
  NotificationContentType,
  NotificationRecipient,
  NotificationRequest,
} from '../../providers/notification-backend/notification-backend.interface';
import { GROUP_NOTIFICATION_SILENT_PERIOD_KEY } from '../../providers/redis/redis.constants';
import {
  GroupNotificationConfigDto,
  SendGroupNotificationContentType,
  SendGroupNotificationDto,
} from './group-notification.dto';
import { NotificationTemplateMap } from 'src/constants/notification';
import pRetry from 'p-retry';
import { ConfigService } from '@nestjs/config';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';

@Injectable()
export class GroupNotificationService {
  private logger = new Logger(GroupNotificationService.name);
  private groupNotificationRoleMap = {
    [GroupNotificationRecipientRole.ADMIN]: SystemName.GROUP_ADMIN,
    [GroupNotificationRecipientRole.OWNER]: SystemName.GROUP_OWNER,
  };
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private mailService: MailService,
    private notificationBackendService: NotificationBackendService,
    private redisService: RedisService,
    private featureFlagService: FeatureFlagService,
  ) {}

  private async _buildGroupNotificationWhereClause(
    groupId: number,
    existingWhere?: Prisma.GroupNotificationWhereInput,
  ): Promise<Prisma.GroupNotificationWhereInput> {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: { id: true, groupType: true },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    let featureFlagLogic: Prisma.GroupNotificationWhereInput;

    if (group.groupType === GroupType.INSIGHT) {
      featureFlagLogic = {
        OR: [
          {
            featureFlagKey: null,
          },
          {
            featureFlagKey: { not: null },
            featureFlag: {
              isEnabled: true, // For INSIGHT, only check main flag
            },
          },
        ],
      };
    } else {
      // For BOT or FLOW, which are valid FeatureFlagTargetType
      const targetType = group.groupType as unknown as FeatureFlagTargetType;
      featureFlagLogic = {
        OR: [
          {
            featureFlagKey: null,
          },
          {
            featureFlagKey: { not: null },
            featureFlag: {
              OR: [
                {
                  // Condition 1: Override exists for this group and is enabled
                  featureFlagOverrides: {
                    some: {
                      targetValue: groupId.toString(),
                      targetType: targetType,
                      isEnabled: true,
                    },
                  },
                },
                {
                  // Condition 2: No override for this group AND main flag is enabled
                  AND: [
                    {
                      featureFlagOverrides: {
                        none: {
                          targetValue: groupId.toString(),
                          targetType: targetType,
                        },
                      },
                    },
                    { isEnabled: true }, // Main featureFlag.isEnabled
                  ],
                },
              ],
            },
          },
        ],
      };
    }

    return existingWhere ? { AND: [existingWhere, featureFlagLogic] } : featureFlagLogic;
  }

  async getGroupNotificationsConfig(
    groupId: number,
    skip?: number,
    take?: number,
    where?: Prisma.GroupNotificationWhereInput,
    orderBy?: Prisma.GroupNotificationOrderByWithRelationInput,
  ) {
    const finalWhere = await this._buildGroupNotificationWhereClause(groupId, where);

    const notificationList = await this.prisma.groupNotification.findMany({
      where: finalWhere,
      skip,
      take,
      orderBy,
      include: {
        groupNotificationConfig: {
          where: {
            groupId: groupId,
          },
        },
      },
    });
    return notificationList;
  }

  async getGroupNotificationsConfigCount(
    groupId: number,
    where?: Prisma.GroupNotificationWhereInput,
  ) {
    const finalWhere = await this._buildGroupNotificationWhereClause(groupId, where);
    return this.prisma.groupNotification.count({ where: finalWhere });
  }

  async updateGroupNotificationsConfig(
    groupId: number,
    groupNotificationId: number,
    groupNotificationConfigDto: GroupNotificationConfigDto,
  ) {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    const groupNotification = await this.prisma.groupNotification.findUnique({
      where: { id: groupNotificationId },
    });
    if (!groupNotification) throw new ApiException(ErrorCode.GROUP_NOTIFICATION_NOT_FOUND);
    return this.prisma.groupNotificationConfig.upsert({
      where: {
        groupNotificationId_groupId: {
          groupId: groupId,
          groupNotificationId: groupNotificationId,
        },
      },
      update: {
        silentPeriod: groupNotificationConfigDto.silentPeriod ?? null,
        recipients: groupNotificationConfigDto.recipients ?? undefined,
        enabledNotify:
          groupNotification.type === GroupNotificationType.REPORT
            ? groupNotificationConfigDto.enabledNotify ?? undefined
            : undefined,
      },
      create: {
        groupId: groupId,
        groupNotificationId: groupNotificationId,
        silentPeriod: groupNotificationConfigDto.silentPeriod ?? null,
        recipients: groupNotificationConfigDto.recipients ?? undefined,
        enabledNotify:
          groupNotification.type === GroupNotificationType.REPORT
            ? groupNotificationConfigDto.enabledNotify ?? undefined
            : undefined,
      },
    });
  }

  async sendGroupNotification(groupId: number, sendGroupNotificationDto: SendGroupNotificationDto) {
    this.checkDataValid(sendGroupNotificationDto);
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    const groupNotifications = await this.prisma.groupNotification.findMany({
      where: {
        groupNotificationName: sendGroupNotificationDto.notificationKey,
      },
      include: {
        groupNotificationConfig: {
          where: {
            groupId: groupId,
          },
        },
      },
    });
    if (!groupNotifications || groupNotifications?.length === 0)
      throw new ApiException(ErrorCode.GROUP_NOTIFICATION_NOT_FOUND);
    const result = [];
    for (const groupNotification of groupNotifications) {
      sendGroupNotificationDto.title = sendGroupNotificationDto?.title ?? groupNotification.title;
      sendGroupNotificationDto.data = {
        ...(sendGroupNotificationDto?.data ?? {}),
        period: groupNotification.interval,
        title: sendGroupNotificationDto?.title ?? groupNotification.title,
      };
      const groupNotificationConfig =
        groupNotification.groupNotificationConfig?.length > 0
          ? groupNotification.groupNotificationConfig[0]
          : null;
      if (
        await this.checkEnabledNotify(
          groupId,
          sendGroupNotificationDto,
          groupNotification,
          groupNotificationConfig,
        )
      ) {
        const recipients = await this.getGroupNotificationRecipients(
          groupId,
          groupNotification,
          groupNotificationConfig,
        );
        if (!recipients || recipients.length === 0) {
          throw new ApiException(ErrorCode.GROUP_NOTIFICATION_NO_RECIPIENT);
        }
        const content: NotificationContent =
          await this.getNotificationContent(sendGroupNotificationDto);
        const notificationRequest: NotificationRequest = {
          source:
            groupNotification.groupNotificationName +
            '_' +
            groupNotification.channel +
            '_' +
            groupId,
          sourceId: sendGroupNotificationDto.sourceId,
          channel: groupNotification.channel,
          contentType: NotificationContentType.TEMPLATE,
          recipients: recipients,
          content: content,
          attachmentFileBucket: sendGroupNotificationDto.attachmentFileBucket,
          attachmentFilePaths: sendGroupNotificationDto.attachmentFilePaths,
        };
        const response =
          await this.notificationBackendService.sendNotification(notificationRequest);
        result.push(response);
        if (response && groupNotification.type === GroupNotificationType.IMMEDIATE_ALERT) {
          await this.setSilentPeriodCache(groupId, groupNotification, groupNotificationConfig);
        }
      }
    }
    return result;
  }

  private async checkEnabledNotify(
    groupId: number,
    sendGroupNotificationDto: SendGroupNotificationDto,
    groupNotification: GroupNotification,
    groupNotificationConfig?: GroupNotificationConfig,
  ) {
    if (!(groupNotificationConfig?.enabledNotify ?? groupNotification.defaultEnabledNotify)) {
      this.logger.log(
        `group ${groupId} notification ${sendGroupNotificationDto.notificationKey} disabled notify, will not send notification`,
      );
      return false;
    }
    // check if groupNotification feature flag is enabled
    if (groupNotification.featureFlagKey) {
      const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        groupId,
        groupNotification.featureFlagKey,
      );
      if (!featureFlag?.isEnabled) {
        this.logger.log(
          `group ${groupId} notification ${sendGroupNotificationDto.notificationKey} disabled by feature flag ${groupNotification.featureFlagKey}, will not send notification`,
        );
        return false;
      }
    }
    const isSilentPeriodValid = await this.checkSilentPeriodValid(groupId, groupNotification);
    if (!isSilentPeriodValid) return false;
    return true;
  }

  async getGroupNotificationRecipients(
    groupId: number,
    groupNotification: GroupNotification,
    groupNotificationConfig?: GroupNotificationConfig,
  ) {
    let recipients: NotificationRecipient[] = [];
    if (groupNotificationConfig?.recipients?.length > 0) {
      //get override recipients
      recipients = await this.getGroupNotificationOverrideRecipients(
        groupId,
        groupNotificationConfig,
      );
    }
    if (recipients.length === 0) {
      const roles = groupNotification.defaultRecipientRoles;
      recipients = await this.getGroupNotificationRecipientsByRole(groupId, roles);
    }
    return recipients;
  }

  async getGroupNotificationOverrideRecipients(
    groupId: number,
    groupNotificationConfig: GroupNotificationConfig,
  ) {
    const recipients: NotificationRecipient[] = [];
    const emails = await this.prisma.email.findMany({
      where: {
        email: {
          in: groupNotificationConfig.recipients,
        },
        user: {
          active: true,
          memberships: {
            some: {
              groupId: groupId,
            },
          },
        },
      },
      select: {
        email: true,
        userId: true,
        user: true,
      },
    });
    recipients.push(
      ...emails.map((email) => ({
        email: email.email,
        userId: email.userId,
        data: [{ key: 'userName', value: email.user.name }],
      })),
    );
    return recipients;
  }

  async getGroupNotificationRecipientsByRole(
    groupId: number,
    roles: GroupNotificationRecipientRole[],
  ) {
    const recipients: NotificationRecipient[] = [];
    for (const role of roles) {
      if (role === GroupNotificationRecipientRole.REVIEWER_N_APPROVER) {
        // get bot reviewer infos within group
        const seniorManagementUsers = await this.prisma.user.findMany({
          include: {
            emails: {
              take: 1, // assume 1 email per user
            },
          },
          where: {
            memberships: {
              some: {
                groupId: groupId,
              },
            },
            userRole: {
              systemName: SystemName.BOT_REVIEWER,
            },
          },
        });
        const botReviewNorminator = await this.prisma.botReviewNomination.findMany({
          where: {
            membership: {
              groupId: groupId,
            },
            startDate: { lte: new Date() },
            endDate: { gte: new Date() },
          },
          include: {
            membership: {
              include: {
                user: {
                  include: {
                    emails: { take: 1 },
                  },
                },
              },
            },
          },
        });
        const seniorManagementUserEmails = seniorManagementUsers
          .map((user) => {
            if (user.emails.length > 0) {
              return {
                email: user.emails[0].emailSafe,
                userId: user.id,
                data: [{ key: 'userName', value: user.name }],
              };
            }
          })
          .filter((item) => item.email !== null && item.email !== undefined);

        const botReviewNorminatorEmails = botReviewNorminator.map((norminator) => {
          if (norminator.membership.user.emails.length > 0) {
            return {
              email: norminator.membership.user.emails[0].emailSafe,
              userId: norminator.membership.userId,
              data: [{ key: 'userName', value: norminator.membership.user.name }],
            };
          }
        });
        recipients.push(...seniorManagementUserEmails, ...botReviewNorminatorEmails);
      } else {
        //get recipients by group role
        const systemName = this.groupNotificationRoleMap[role];
        const members = await this.prisma.membership.findMany({
          where: {
            groupId: groupId,
            Role: {
              systemName: systemName,
            },
            user: {
              active: true,
            },
          },
          select: {
            userId: true,
            user: {
              select: {
                name: true,
                emails: {
                  select: {
                    email: true,
                  },
                },
              },
            },
          },
        });
        recipients.push(
          ...members.map((member) => ({
            email: member.user.emails[0].email,
            userId: member.userId,
            data: [{ key: 'userName', value: member.user.name }],
          })),
        );
      }
    }
    return recipients;
  }

  async getNotificationContent(sendGroupNotificationDto: SendGroupNotificationDto) {
    let htmlContent,
      textContent,
      isMarkdownText = false;
    if (sendGroupNotificationDto.contentType === SendGroupNotificationContentType.Normal) {
      if (sendGroupNotificationDto.data) {
        htmlContent = this.applyDataToContent(
          sendGroupNotificationDto.htmlContent,
          sendGroupNotificationDto.data,
        );
        textContent = this.applyDataToContent(
          sendGroupNotificationDto.textContent,
          sendGroupNotificationDto.data,
        );
      } else {
        htmlContent = sendGroupNotificationDto.htmlContent;
        textContent = sendGroupNotificationDto.textContent;
      }
    }
    if (sendGroupNotificationDto.contentType === SendGroupNotificationContentType.Template) {
      //get template content
      try {
        const { html, markdown } = await this.mailService.getTemplateOptions({
          template: sendGroupNotificationDto.templateName,
          data: { userName: '{{userName}}', ...sendGroupNotificationDto.data },
        });
        htmlContent = html;
        textContent = markdown;
        isMarkdownText = true;
      } catch (error) {
        this.logger.error(error);
        throw new ApiException(ErrorCode.NOTIFICATION_TEMPLATE_NOT_FOUND);
      }
    }
    return {
      html: htmlContent,
      text: textContent,
      subject: sendGroupNotificationDto.title,
      isMarkdownText,
    };
  }

  private applyDataToContent(content: string, data: Record<string, any>) {
    if (!content) return content;
    return content.replace(/{{(.*?)}}/g, (match, key) => {
      return key.trim() in data ? data[key.trim()] : match;
    });
  }

  private checkDataValid(sendGroupNotificationDto: SendGroupNotificationDto) {
    if (
      sendGroupNotificationDto.contentType === SendGroupNotificationContentType.Normal &&
      !sendGroupNotificationDto.htmlContent?.trim() &&
      !sendGroupNotificationDto.textContent?.trim()
    ) {
      throw new ApiException(ErrorCode.INVALID_NOTIFICATION_CONTENT);
    }
    if (
      sendGroupNotificationDto.contentType === SendGroupNotificationContentType.Template &&
      !sendGroupNotificationDto.templateName?.trim()
    ) {
      throw new ApiException(ErrorCode.INVALID_NOTIFICATION_TEMPLATE);
    }
  }

  private async checkSilentPeriodValid(groupId: number, groupNotification: GroupNotification) {
    if (groupNotification.type === GroupNotificationType.IMMEDIATE_ALERT) {
      const redisKey = this.getSilentPeriodCacheKey(groupId, groupNotification);
      const silentPeriodCache = await this.redisService.getCache(redisKey);
      if (silentPeriodCache) {
        //silent period did not expire, will not send notification
        this.logger.log(`Silent period [${redisKey}] did not expire, will not send notification`);
        return false;
      }
    }
    return true;
  }

  private getSilentPeriodCacheKey(groupId: number, groupNotification: GroupNotification) {
    return GROUP_NOTIFICATION_SILENT_PERIOD_KEY.replace(
      '{NOTIFICATION_KEY}',
      groupNotification.groupNotificationName,
    )
      .replace('{CHANNEL}', groupNotification.channel)
      .replace('{GROUP_ID}', groupId.toString());
  }

  private async setSilentPeriodCache(
    groupId: number,
    groupNotification: GroupNotification,
    groupNotificationConfig?: GroupNotificationConfig,
  ) {
    const redisKey = this.getSilentPeriodCacheKey(groupId, groupNotification);
    const silentPeriodHour =
      groupNotificationConfig?.silentPeriod ?? groupNotification.defaultSilentPeriod;
    if (!silentPeriodHour || silentPeriodHour <= 0) return;
    await this.redisService.createCache(redisKey, 'true', silentPeriodHour * 3600);
  }

  async getEnabledGroupNotificationsConfig(notificationKey: string) {
    const groupNotifications = await this.prisma.groupNotification.findMany({
      where: {
        groupNotificationName: notificationKey,
      },
      include: {
        groupNotificationConfig: {
          select: {
            groupId: true,
            enabledNotify: true,
          },
        },
      },
    });
    const groupIds = [];
    for (const groupNotification of groupNotifications) {
      if (groupNotification.defaultEnabledNotify) {
        const disabledGroupIds = groupNotification.groupNotificationConfig
          ?.filter((config) => config.enabledNotify === false)
          .map((config) => config.groupId);
        const groups = await this.prisma.group.findMany({
          where: {
            id: {
              notIn: disabledGroupIds,
            },
          },
          select: {
            id: true,
          },
        });
        groupIds.push(...groups.map((group) => group.id));
      } else {
        const enabledGroupIds = groupNotification.groupNotificationConfig
          ?.filter((config) => config.enabledNotify === true)
          .map((config) => config.groupId);
        groupIds.push(...enabledGroupIds);
      }
    }
    return [...new Set<number>(groupIds)];
  }

  public async sendGroupReportNotification(
    fileHistory: FileHistory,
    from: string,
    to: string,
    s3FilePath?: string,
    notificationKey?: string,
  ) {
    const group = await this.prisma.group.findFirst({
      where: {
        id: fileHistory.entityId,
      },
    });
    const templateData = {
      botName: group.name,
      env: group.env,
      url: this.configService.get('frontendUrl') + `/groups/${fileHistory.entityId}/playground`,
      period: '',
    };
    const bucket = this.configService.get<string>(`s3.reportFilesBuckets`);
    this.logger.log(
      `send report notification -> ${s3FilePath} notificationKey: ${notificationKey}, dateFrom: ${from}, dateTo: ${to}`,
    );
    const sendGroupNotificationDto: SendGroupNotificationDto = {
      sourceId: [notificationKey, fileHistory.fileId, fileHistory.entityId, fileHistory.id].join(
        '_',
      ),
      contentType: SendGroupNotificationContentType.Template,
      notificationKey: notificationKey,
      templateName: NotificationTemplateMap?.[notificationKey] ?? '',
      attachmentFileBucket: bucket,
      attachmentFilePaths: [s3FilePath],
      data: templateData,
    };
    await pRetry(
      async () => await this.sendGroupNotification(fileHistory.entityId, sendGroupNotificationDto),
      {
        retries: 3,
        onFailedAttempt: (error) => {
          this.logger.error(
            `send report notification  ${notificationKey} failed, retrying (${error.retriesLeft} attempts left)`,
            error.name,
          );
          this.logger.error(error);
        },
      },
    );
  }
}
