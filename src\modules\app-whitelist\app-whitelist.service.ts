import { Injectable, Logger } from '@nestjs/common';
import { WHITELIST_GROUP_SUFFIX } from 'src/constants/app-whitelist';
import { KeycloakService } from 'src/providers/keycloak/keycloak.service';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { UsersService } from '../users/users.service';
import { BatchAppWhitelistAction, BatchAppWhitelistDto } from './dto/batch-app-whitelist.dto';
import { CreateAppWhitelistDto } from './dto/create-app-whitelist.dto';
import { QueryAppWhitelistDto } from './dto/query-app-whitelist.dto';
import { AppCenterService } from '../app-center/app-center.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';

@Injectable()
export class AppWhitelistService {
  private logger = new Logger(AppWhitelistService.name);
  private readonly APP_WHITELIST_SELECT_FIELD = {
    id: true,
    createdAt: true,
    appId: true,
    userId: true,
    user: {
      select: {
        name: true,
        staffId: true,
        emails: true,
      },
    },
    app: {
      select: {
        name: true,
      },
    },
  };
  constructor(
    private readonly prisma: PrismaService,
    private readonly keycloakService: KeycloakService,
    private readonly appCenterService: AppCenterService,
    private readonly userService: UsersService,
  ) {}

  async create(createAppWhitelistDto: CreateAppWhitelistDto) {
    const { appType, email } = createAppWhitelistDto;
    const app = await this.appCenterService.findAppWithAppName(appType);
    const user = await this.prisma.user.findFirst({ where: { emails: { some: { email } } } });
    if (!user) {
      throw new ApiException(ErrorCode.USER_NOT_FOUND);
    }
    const whitelist = await this.prisma.appWhitelist.findFirst({
      where: {
        app: { name: createAppWhitelistDto.appType },
        user: { emails: { some: { email: createAppWhitelistDto.email } } },
      },
    });
    if (whitelist) {
      return whitelist;
    }
    await this.keycloakService.addUserToWhitelistGroup(appType, email);
    return this.prisma.appWhitelist.create({
      data: {
        appId: app.id,
        userId: user.id,
      },
    });
  }

  async findAll(query: QueryAppWhitelistDto) {
    const { skip, take, orderBy, where } = query;
    const queryWhere = {
      ...(where
        ? {
            OR: [
              {
                app: {
                  name: where.search,
                },
              },
              {
                user: {
                  emails: {
                    every: {
                      email: where.search,
                    },
                  },
                },
              },
            ],
          }
        : {}),
    };
    const count = await this.prisma.appWhitelist.count({ where: queryWhere });
    const list = await this.prisma.appWhitelist.findMany({
      select: this.APP_WHITELIST_SELECT_FIELD,
      where: queryWhere,
      skip,
      take,
      orderBy,
    });
    return { count, list };
  }

  async findOne(id: number) {
    const appWhitelist = await this.prisma.appWhitelist.findUnique({
      select: this.APP_WHITELIST_SELECT_FIELD,
      where: { id },
    });
    if (!appWhitelist) {
      throw new ApiException(ErrorCode.APP_WHITELIST_NOT_FOUND);
    }
    return appWhitelist;
  }

  async remove(id: number) {
    const whitelist = await this.findOne(id);
    await this.keycloakService.userLeaveWhitelistGroup(
      whitelist.app.name,
      whitelist.user.emails[0].email,
    );
    await this.prisma.appWhitelist.delete({ where: { id } });
    return whitelist;
  }

  async batchProcess(batchAppWhitelistDto: BatchAppWhitelistDto) {
    const { items } = batchAppWhitelistDto;
    const successList = [];
    const failureList = [];
    for (const item of items) {
      const whitelist = await this.prisma.appWhitelist.findFirst({
        where: {
          app: { name: item.appType },
          user: { emails: { some: { email: item.email } } },
        },
      });
      try {
        if (item.action === BatchAppWhitelistAction.ADD && !whitelist) {
          await this.create({ ...item, appType: item.appType });
        } else if (item.action === BatchAppWhitelistAction.REMOVE && whitelist) {
          await this.remove(whitelist.id);
        }
        successList.push(item);
      } catch (err) {
        this.logger.error(err, `Failed to add user in Whitelist ${item.email}`);
        failureList.push({
          ...item,
          errMsg:
            (err as any)?.response?.error?.message ??
            'Internal Server Error. Please try to fix input and re-upload again.',
        });
      }
    }
    return { successList, failureList };
  }

  async downloadTemplate() {
    const apps = await this.findWhitelistApps();
    const appNames = apps.map((app) => app.name).join('/');
    const data = [
      {
        email: 'Email is required',
        appType: `AppType should be one of: ${appNames}`,
        action: 'Action should be one of: add/remove',
      },
    ];
    return data;
  }

  async getWhitelistsByEmail(email: string) {
    const user = await this.userService.findUserIdByEmail(email);
    const whiteListApps = await this.prisma.app.findMany({
      include: {
        AppWhitelist: true,
      },
      where: {
        whitelistControl: true,
        AppWhitelist: {
          some: {
            userId: user.userId,
          },
        },
      },
    });
    return whiteListApps.map((item) => `${item.name}${WHITELIST_GROUP_SUFFIX}`);
  }

  async findWhitelistApps() {
    const apps = await this.prisma.app.findMany({
      where: {
        whitelistControl: true,
      },
    });
    return apps;
  }
}
