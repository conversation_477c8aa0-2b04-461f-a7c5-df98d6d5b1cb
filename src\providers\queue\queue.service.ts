import { Injectable, InternalServerErrorException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServiceException } from '@smithy/smithy-client';

import {
  SendMessageBatchCommandInput,
  SendMessageCommandInput,
  SendMessageCommandOutput,
  SendMessageBatchCommand,
  SendMessageCommand,
  SendMessageBatchCommandOutput,
  SQSClient ,
} from '@aws-sdk/client-sqs';

import { Configuration } from 'src/config/configuration.interface';
import { QueueServiceInterface } from './queue.service.interface';
import { BullMqService } from '../bullmq/bullmq.service';

@Injectable()
export class QueueService {
  private client: SQSClient;
  private logger = new Logger(QueueService.name);
  private bullMqClient: QueueServiceInterface;

  constructor(
    private configService: ConfigService,
    private bullMqService: BullMqService,
  ) {
    const config = this.configService.get<Configuration['queue']>('queue');
    // TODO::  if need support other cloud service need create Provider service for this
    this.client = new SQSClient({ region: config.region });

    this.bullMqClient = this.bullMqService;
    
  }

  async sendMessageBatch(params: SendMessageBatchCommandInput): Promise<void> {
    try {
      const config = this.configService.get<Configuration['queue']>('queue');
      this.logger.log(`send SQS Msg Batch-> ${JSON.stringify(params)}`);
      
      if(config.provider == 'sqs'){
        const command = new SendMessageBatchCommand(params);
        const sendMessageBatchOutput: SendMessageBatchCommandOutput = await this.client.send(command);
        this.logger.log(`send SQS Msg Batch response -> ${sendMessageBatchOutput}`);
      }else{
        await this.bullMqClient.sendMessageBatch(params);
      }
    } catch (err) {
      this.logger.error((err as any)?.message, `Failed to send sqs message Batch - ${params.QueueUrl}`);
      throw new InternalServerErrorException();
    }
  }

  async sendMessage(params: SendMessageCommandInput): Promise<void> {
    try {
      const config = this.configService.get<Configuration['queue']>('queue');
      this.logger.log(`send SQS Msg -> ${JSON.stringify(params)}`);
      if(config.provider == 'sqs'){
        
        const command = new SendMessageCommand(params);
        const sendMessageCommandOutput: SendMessageCommandOutput = await this.client.send(command);
        this.logger.log(`send SQS Msg response -> ${JSON.stringify(sendMessageCommandOutput)}`);
        this.logger.debug(sendMessageCommandOutput.MessageId);
      }else{
        await this.bullMqClient.sendMessage(params);
      }
    } catch (err) {
      this.logger.error((err as any)?.message, `Failed to send sqs message - ${params.QueueUrl}`);
      throw new InternalServerErrorException();
    }
  }
}
