import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { FeatureFlag, PrismaClient } from '@prisma/client';
import { FeatureFlagService } from './feature-flags.service';
import { RedisService } from '../../providers/redis/redis.service';
import { CreateFeatureFlagDTO } from './feature-flags-model.dto';
import { LabelsService } from '../labels/labels.service';
import { UserRequest } from '../auth/auth.interface';
const moduleMocker = new ModuleMocker(global);

describe('FeatureFlagService', () => {
  let featureFlagService: FeatureFlagService;
  let redisService: DeepMockProxy<RedisService>;
  let prismaService: DeepMockProxy<{ [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'> }>;
  let labelsService: DeepMockProxy<LabelsService>;
  beforeEach(async () => {
    prismaService = mockDeep<PrismaClient>() as unknown as DeepMockProxy<{
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    redisService = mockDeep<RedisService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeatureFlagService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
        {
          provide: RedisService,
          useValue: redisService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    featureFlagService = module.get(FeatureFlagService);
    labelsService = module.get(LabelsService);
  });

  describe('getAll', () => {
    it('should return feature flag list', async () => {
      const featureFlagList = [
        {
          id: 1,
          value: '',
          targetType: 'BOT',
          targetValue: '10580279',
          level: 1,
          featureFlagId: 1,
          createdByUserId: 1,
          createdAt: '2024-05-29T06:47:38.668Z',
          updatedAt: '2024-05-29T06:47:38.667Z',
          updatedByUserId: 1,
          metaData: '',
          isEnabled: true,
          featureFlag: { key: 'CONNECT_API.ALLOWED_HOST_URL_MAP' },
          updatedBy: { name: '<EMAIL>' },
          createdBy: { name: '<EMAIL>' },
        },
      ] as unknown as FeatureFlag[];
      prismaService.featureFlag.findMany.mockResolvedValue(featureFlagList);
      const res = await featureFlagService.getAll();
      expect(res).toEqual(featureFlagList);
      expect(prismaService.featureFlag.findMany).toHaveBeenCalled();
    });
  });

  describe('getCount', () => {
    it('should return feature flag count', async () => {
      const count = 20;
      prismaService.featureFlag.count.mockResolvedValue(count);
      const res = await featureFlagService.getCount();
      expect(res).toEqual(count);
      expect(prismaService.featureFlag.count).toHaveBeenCalled();
    });

    it('should return feature flag count with categoryId', async () => {
      const count = 10;
      const categoryId = 1;
      prismaService.featureFlag.count.mockResolvedValue(count);
      const res = await featureFlagService.getCount({}, categoryId);
      expect(res).toEqual(count);
      expect(prismaService.featureFlag.count).toHaveBeenCalledWith({
        where: { categoryId },
      });
    });
  });

  describe('create', () => {
    it('should return feature flag', async () => {
      const featureFlagDTO = {
        key: 'ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN',
        description: '',
        isEnabled: true,
        value: '',
        isForClientSide: true,
        categoryId: 1,
      } as CreateFeatureFlagDTO;
      const responseData = {
        id: 173,
        value: '',
      } as FeatureFlag;
      prismaService.featureFlag.create.mockResolvedValue(responseData);
      const res = await featureFlagService.create(featureFlagDTO);
      expect(res).toEqual(responseData);
      expect(prismaService.featureFlag.create).toHaveBeenCalledWith({
        data: {
          ...featureFlagDTO,
          updatedAt: expect.any(Date),
        },
      });
    });
  });

  describe('update', () => {
    it('should return feature flag', async () => {
      const featureFlagDTO = {
        key: 'ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN',
        description: '',
        isEnabled: true,
        value: '',
        isForClientSide: true,
        categoryId: 1,
      } as CreateFeatureFlagDTO;
      const responseData = {
        id: 173,
        value: '',
        EntityLabels: [],
      } as unknown as FeatureFlag;
      (prismaService.$transaction as jest.Mock).mockImplementation((fn) => fn(prismaService));
      prismaService.featureFlag.update.mockResolvedValue(responseData);
      const includeQuery = {
        EntityLabels: {
          include: {
            Labels: true,
          },
        },
      };
      (labelsService.getLabelsPrismaQuery as jest.Mock).mockReturnValue(includeQuery);
      const userReq = { user: { id: 1 } } as UserRequest;
      const res = await featureFlagService.update(1, featureFlagDTO, userReq);
      expect(res).toEqual(responseData);
      expect(prismaService.featureFlag.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          ...featureFlagDTO,
          updatedAt: expect.any(Date),
        },
        include: includeQuery,
      });
    });
  });

  describe('delete', () => {
    it('should return feature flag', async () => {
      const featureFlag = {
        id: 1,
        value: '',
      };
      prismaService.featureFlag.delete.mockResolvedValue(featureFlag as FeatureFlag);
      const res = await featureFlagService.delete(1);
      expect(res).toEqual(featureFlag);
      expect(prismaService.featureFlag.delete).toHaveBeenCalledWith({ where: { id: 1 } });
    });
  });

  describe('getById', () => {
    it('should return feature flag', async () => {
      const featureFlag = {
        id: 1,
        value: '',
      };
      prismaService.featureFlag.findUnique.mockResolvedValue(featureFlag as FeatureFlag);
      const res = await featureFlagService.getById(1);
      expect(res).toEqual(featureFlag);
      expect(prismaService.featureFlag.findUnique).toHaveBeenCalledWith({ where: { id: 1 } });
    });
  });
});
