#Original root
.env
.vscode
aws/
backend/http_ca.crt

# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Dotenv
.env
.env-*

venv

.cache/
.node_repl_history
.npm/

cert/

prisma/ERD.md
prisma/custom.seed.ts

.roo*
tmp
prompts
src/app.controller.ts