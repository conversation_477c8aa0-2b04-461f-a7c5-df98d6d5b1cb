import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { Group, Prisma, SnapshotEntityType } from '@prisma/client';
import { ApiKeysService } from '../api-keys/api-keys.service';
import { ApiResourceService } from '../api-resource/api-resource.service';
import { BotSecurityService } from '../bot-security/bot-security.service';
import { FlowsService } from '../flows/flows.service';
import { LLMModelsSettingService } from '../llm-models/llm-models-setting.service';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { EntityPromotionHandler } from '../change-management/interfaces/entity-promotion-handler.interface';
import { LLMModelsParametersService } from '../llm-models/llm-models-parameters.service';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { ChangeManagementService } from '../change-management/change-management.service';
import { CompareValuesWithDetailedDifferences } from 'object-deep-compare';

@Injectable()
export class GroupSettingsService implements PromotableService {
  private logger = new Logger(GroupSettingsService.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly apiKeysService: ApiKeysService,
    private readonly llmModelsService: LLMModelsService,
    private readonly flowsService: FlowsService,
    private readonly apiResourceService: ApiResourceService,
    private readonly botSecurityService: BotSecurityService,
    private readonly llmModelsSettingService: LLMModelsSettingService,
    private readonly llmModelsParametersService: LLMModelsParametersService,
    @Inject(forwardRef(() => ChangeManagementService))
    private readonly changeManagementService: ChangeManagementService,
  ) {
    // config promotable each snapshot entity type
    this.entityPromotionHandler = this.changeManagementService.getEntityPromotionHandler();
  }
  private readonly entityPromotionHandler: EntityPromotionHandler;
  async generateEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityType: SnapshotEntityType[],
  ): Promise<object> {
    // get the latest approved snapshot for GROUP_SETTINGS
    const latestApprovedRequest =
      await this.changeManagementService.getDataPromotionRequestAndEntitySnapshotByGroupId(groupId);
    const latestEntityData = (latestApprovedRequest?.entitySnapshot?.entityData ?? {}) as Record<
      string,
      any
    >;
    // save last approved snapshot entity data,if key have new data replace it
    const entityData: Record<string, any> = { ...latestEntityData };
    // get entity data for each type
    // add checking Whitelist for checking the entityType
    const groupSettingsKeys: SnapshotEntityType[] = [
      SnapshotEntityType.LLM_MODEL,
      SnapshotEntityType.BOT_SECURITY,
      SnapshotEntityType.LLM_MODEL_SETTING,
      SnapshotEntityType.LLM_MODEL_PARAMETERS,
    ];
    for (const type of entityType) {
      if (groupSettingsKeys.includes(type)) {
        const data = await this.entityPromotionHandler[type].service.generateEntityDataForSnapshot(
          groupId,
          entityId,
        );
        entityData[type] = data;
      }
    }
    return entityData;
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }

  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: any,
    operatorId: number,
  ): Promise<string> {
    const ids: Record<string, string> = {};
    for (const key of Object.keys(entityData)) {
      const entityType = SnapshotEntityType[key];
      const id = await this.entityPromotionHandler[entityType].service.promoteCreate(
        tx,
        targetGroup,
        entityData[key],
        operatorId,
      );
      ids[entityType] = id;
    }
    return targetGroup.id.toString();
  }

  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: any,
    operatorId: number,
  ): Promise<void> {
    return this.prisma.$transaction(async (tx) => {
      for (const key of Object.keys(entityData)) {
        const entityType = SnapshotEntityType[key];
        const id = await this.findTargetId(entityType, targetGroup.id);
        await this.entityPromotionHandler[entityType].service.promoteUpdate(
          tx,
          targetGroup,
          id,
          entityData[entityType],
          operatorId,
        );
      }
    });
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    throw new ApiException(ErrorCode.DATA_PROMOTION_DELETE_NOT_SUPPORTED);
  }

  async checkPromotedEntityValid(targetGroupId: string): Promise<object> {
    if (!targetGroupId) {
      return {};
    } else {
      const entityData: Record<string, any> = {};
      const groupSettingsKeys = [
        SnapshotEntityType.LLM_MODEL,
        SnapshotEntityType.BOT_SECURITY,
        SnapshotEntityType.LLM_MODEL_SETTING,
        SnapshotEntityType.LLM_MODEL_PARAMETERS,
      ];
      for (const key of groupSettingsKeys) {
        // use groupId to find entity data
        const targetEntityId = await this.findTargetId(key, parseInt(targetGroupId));
        const current =
          await this.entityPromotionHandler[key].service.checkPromotedEntityValid(targetEntityId);
        entityData[key] = current;
      }
      return entityData;
    }
  }

  async findTargetId(type: SnapshotEntityType, targetGroupId: number): Promise<string> {
    if (SnapshotEntityType.BOT_SECURITY === type) {
      const botSecurity = await this.botSecurityService.findByGroupId(targetGroupId);
      return botSecurity ? botSecurity.botSecurityId.toString() : '';
    } else {
      const llmModel = await this.llmModelsService.findOneByGroupId(targetGroupId);
      return llmModel ? llmModel.id.toString() : '';
    }
  }
  async getGroupSettingsDifferences(groupId: number) {
    // get the latest approved snapshot for GROUP_SETTINGS
    const latestApprovedRequest =
      await this.changeManagementService.getDataPromotionRequestAndEntitySnapshotByGroupId(groupId);
    const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
    const approvedData = (latestApprovedRequest?.entitySnapshot?.entityData ?? {}) as Record<
      string,
      any
    >;
    // find entityData for each groupSettingsKeys
    const differences: Record<string, any> = {};
    const groupSettingsKeys = [
      SnapshotEntityType.LLM_MODEL,
      SnapshotEntityType.BOT_SECURITY,
      SnapshotEntityType.LLM_MODEL_SETTING,
      SnapshotEntityType.LLM_MODEL_PARAMETERS,
    ];
    for (const key of groupSettingsKeys) {
      const current = await this.entityPromotionHandler[key].service.generateEntityDataForSnapshot(
        groupId,
        llmModel.id.toString(),
      );
      // and compare with the latest approved snapshot for each group setting type
      differences[key] = CompareValuesWithDetailedDifferences(
        approvedData[key] ?? {},
        current ?? {},
      );
    }
    /**
     differences: {
     {
        path: 'user.age',
        type: 'changed',
        oldValue: 30,
        newValue: 31
      },
      {
        path: 'user.location',
        type: 'added',
        oldValue: undefined,
        newValue: 'New York'
      }
     },

      convert the differences to a more readable format
      {
        user: {
          age: 31,
          location: 'New York'
        }
      }
     */
    return this.getDifferencesJson(differences);
  }

  getDifferencesJson(differences: Record<string, any>) {
    const result = {};
    for (const key in differences) {
      const diff = differences[key];
      if (Array.isArray(diff)) {
        const newObj = {};
        diff.forEach((item) => {
          if (item.type === 'added' || item.type === 'changed') {
            const path = item.path.split('.');
            let current = newObj;
            for (let i = 0; i < path.length; i++) {
              if (path[i] !== 'createdAt' && path[i] !== 'updatedAt') {
                if (i === path.length - 1) {
                  current[path[i]] = item.newValue;
                } else {
                  current[path[i]] = current[path[i]] || {};
                  current = current[path[i]];
                }
              }
            }
          }
        });
        result[key] = newObj;
      }
    }
    return result;
  }

  async createGroupSettingsSnapshot(
    groupId: number,
    createdByUserId: number,
    name: string,
    entityType: SnapshotEntityType[],
    snapshotType: SnapshotEntityType,
  ) {
    if (entityType.length === 0) {
      throw new ApiException(ErrorCode.ENTITY_SNAPSHOT_EMPTY);
    }
    const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
    // Please confirm whether other generateEntityDataForSnapshot use llmModel id
    const entityData = await this.entityPromotionHandler[
      snapshotType
    ].service.generateEntityDataForSnapshot(groupId, llmModel.id.toString(), entityType);
    if (Object.keys(entityData).length > 0) {
      return await this.prisma.entitySnapshot.create({
        data: {
          group: {
            connect: {
              id: groupId,
            },
          },
          createdBy: {
            connect: {
              id: createdByUserId,
            },
          },
          name,
          entityType: SnapshotEntityType.GROUP_SETTINGS,
          entityId: llmModel.id.toString(),
          entityData,
          versionDate: new Date(),
        },
      });
    }
  }
}
