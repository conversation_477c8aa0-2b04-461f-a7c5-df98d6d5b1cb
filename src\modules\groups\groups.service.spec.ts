import { PrismaService } from 'src/providers/prisma/prisma.service';
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock';
import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
import { Test, TestingModule } from '@nestjs/testing';
import { Environment, Group, GroupType, PrismaClient } from '@prisma/client';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { GroupsService } from './groups.service';
const moduleMocker = new ModuleMocker(global);

describe('GroupsService', () => {
  let groupsService: GroupsService;
  let featureFlagService: DeepMockProxy<FeatureFlagService>;
  let prismaService: DeepMockProxy<{ [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'> }>;
  beforeEach(async () => {
    const prismaMock = mockDeep<PrismaClient>();
    prismaService = Object.assign(prismaMock, {
      expose: jest.fn((data) => data),
    }) as unknown as DeepMockProxy<{
      [K in keyof PrismaClient]: Omit<PrismaClient[K], 'groupBy'>;
    }>;
    featureFlagService = mockDeep<FeatureFlagService>();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroupsService,
        {
          provide: PrismaService,
          useValue: prismaService,
        },
      ],
    })
      .useMocker((token) => {
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>;
          const Mock = moduleMocker.generateFromMetadata(mockMetadata);
          return new Mock();
        }
      })
      .compile();
    groupsService = module.get(GroupsService);
  });

  describe('getGroup', () => {
    it('should return Group', async () => {
      const existGroup = {
        autoJoinDomain: false,
        forceTwoFactor: false,
        id: 1,
        ipRestrictions: null,
        name: 'Superadmin',
        env: 'PROD',
        onlyAllowDomain: false,
        profilePictureUrl: '',
        attributes: null,
        ccc: 'Superadmin',
        department: 'Superadmin',
        businessUnit: 'Superadmin',
        description: 'Superadmin',
        parentId: null,
        pairId: 1,
        deletedAt: null,
        groupType: 'BOT',
        createdByUserId: 1,
      };
      prismaService.group.findUnique.mockResolvedValue(existGroup as Group);
      const res = await groupsService.getGroup(1, {});
      expect(res).toEqual(existGroup);
      expect(prismaService.group.findUnique).toBeCalledWith({
        where: { id: 1 },
      });
    });
  });

  describe('replaceGroup', () => {
    it('should replace Group', async () => {
      const existGroup = {
        autoJoinDomain: false,
        forceTwoFactor: false,
        id: 1,
        ipRestrictions: null,
        name: 'Superadmin',
        env: 'PROD',
        onlyAllowDomain: false,
        profilePictureUrl: '',
        attributes: null,
        ccc: 'Superadmin',
        department: 'Superadmin',
        businessUnit: 'Superadmin',
        description: 'Superadmin',
        pairId: 1,
        deletedAt: null,
        groupType: 'BOT',
      } as Group;
      const updateGroup = {
        autoJoinDomain: false,
        forceTwoFactor: false,
        id: 1,
        ipRestrictions: null,
        name: 'Superadmin-tets',
        env: 'PROD',
        onlyAllowDomain: false,
        profilePictureUrl: '',
        attributes: null,
        ccc: 'Superadmin-tets',
        department: 'Superadmin-tets',
        businessUnit: 'Superadmin-tets',
        description: 'Superadmin-tets',
        pairId: 1,
        deletedAt: null,
        groupType: 'BOT',
      } as Group;
      prismaService.group.findUnique.mockResolvedValue(existGroup);
      prismaService.group.update.mockResolvedValue(updateGroup);
      const res = await groupsService.replaceGroup(1, updateGroup);
      expect(res).toEqual(updateGroup);
      expect(prismaService.group.findUnique).toHaveBeenCalledWith({
        where: {
          id: 1,
        },
      });
      expect(prismaService.group.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateGroup,
      });
    });
  });

  describe('getGroupTypeByGroupId', () => {
    it('should return Group Type', async () => {
      const existGroupType = {
        groupType: GroupType.BOT,
      };
      prismaService.group.findUnique.mockResolvedValue(existGroupType as Group);
      const res = await groupsService.getGroupTypeByGroupId(1);
      expect(res).toEqual(existGroupType.groupType);
      expect(prismaService.group.findUnique).toBeCalledWith({
        where: { id: 1 },
        select: { groupType: true },
      });
    });
  });

  describe('getGroupEnvByGroupId', () => {
    it('should return Group ENV', async () => {
      const existGroupEnv = {
        env: Environment.TEST,
      };
      prismaService.group.findUnique.mockResolvedValue(existGroupEnv as Group);
      const res = await groupsService.getGroupEnvByGroupId(1);
      expect(res).toEqual(existGroupEnv.env);
      expect(prismaService.group.findUnique).toBeCalledWith({
        where: { id: 1 },
        select: { env: true },
      });
    });
  });
});
