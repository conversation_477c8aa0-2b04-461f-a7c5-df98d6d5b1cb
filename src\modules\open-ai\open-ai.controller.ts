import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  Res,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiOperation, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import OpenAI from 'openai';
import { UserRequest } from '../auth/auth.interface';
import { OpenAIService } from './open-ai.service';
import { Scopes } from '../auth/scope.decorator';
import { ExternalApi } from 'src/swagger-document';
import { ConvertSseInterceptor } from 'src/interceptors/convert-sse.interceptor';
import { Quota } from '../auth/quota.decorator';
import {
  ChatCompletionCreateParamsDto,
  CreateResponseDto,
  EmbeddingCreateParamsDto,
} from './open-ai.dto';

@ApiSecurity('api-key')
@ApiTags('OpenAI Compatible API')
@Controller('groups/:groupId')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class OpenAIController {
  constructor(private readonly openAIService: OpenAIService) {}

  @Post(['llm-model/chat/completions', 'openai/chat/completions'])
  @ExternalApi()
  @ApiOperation({ summary: 'Open AI Chat Completions', description: '', tags: ['Chat'] })
  @Quota(
    'group-{groupId}:llm-engine-{llmEngine}-quota',
    'group-{groupId}:rate-limit-{channel}-quota',
  )
  @UseInterceptors(ConvertSseInterceptor)
  @Scopes('group-{groupId}:read-playground')
  async createCompletion(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createCompletionDto: ChatCompletionCreateParamsDto,
    @Req() req: UserRequest,
    @Res() res: Response,
  ) {
    const ChatResponse = (await this.openAIService.createCompletion(
      groupId,
      createCompletionDto,
      req,
      res,
    )) as unknown as OpenAI.Completions.Completion;
    if (!createCompletionDto.stream) {
      return res.status(200).json(ChatResponse);
    }
  }

  @Post('openai/responses')
  @ExternalApi()
  @ApiOperation({ summary: 'Open AI Response', description: '', tags: ['Response'] })
  @UseInterceptors(ConvertSseInterceptor)
  @Scopes('group-{groupId}:read-playground')
  async createResponse(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createResponseDto: CreateResponseDto,
    @Req() req: UserRequest,
    @Res() res: Response,
  ) {
    const response = (await this.openAIService.createResponse(
      groupId,
      createResponseDto,
      req,
      res,
    )) as OpenAI.Responses.Response;
    if (!createResponseDto.stream) {
      return res.status(200).json(response);
    }
  }

  @Post('openai/embeddings')
  @ExternalApi()
  @ApiOperation({
    summary: 'OpenAI Compatible Embeddings',
    description: '',
    tags: ['Embeddings'],
  })
  @Scopes('group-{groupId}:read-playground')
  async createEmbedding(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createEmbeddingDto: EmbeddingCreateParamsDto,
    @Req() req: UserRequest,
    @Res() res: Response,
  ) {
    const embeddingResponse = await this.openAIService.createEmbedding(
      groupId,
      createEmbeddingDto,
      req,
    );
    return res.status(200).json(embeddingResponse);
  }

  @Get(['llm-model/models', 'openai/models'])
  @ExternalApi()
  @Scopes('group-{groupId}:read-playground')
  @ApiOperation({ summary: 'Support Model Lists', description: '', tags: ['Models'] })
  async getActiveModels(@Param('groupId', ParseIntPipe) groupId: number) {
    const models = await this.openAIService.getActiveModels(groupId);
    return models;
  }
}
