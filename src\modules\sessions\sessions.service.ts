import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import type { Prisma } from '@prisma/client';
import { Session } from '@prisma/client';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';

@Injectable()
export class SessionsService {
  constructor(private prisma: PrismaService) {}

  async getSessions(
    userId: number,
    params: {
      skip?: number;
      take?: number;
      cursor?: Prisma.SessionWhereUniqueInput;
      where?: Prisma.SessionWhereInput;
      orderBy?: Prisma.SessionOrderByWithRelationInput;
    },
    sessionId?: number,
  ): Promise<Expose<Session & { isCurrentSession: boolean }>[]> {
    const { skip, take, cursor, where, orderBy } = params;
    try {
      const sessions = await this.prisma.session.findMany({
        skip,
        take,
        cursor,
        where: { ...where, user: { id: userId } },
        orderBy,
      });
      return sessions
        .map((user) => this.prisma.expose<Session>(user))
        .map((i) => ({ ...i, isCurrentSession: sessionId === i.id }));
    } catch (error) {
      return [];
    }
  }

  async getSessionsCount(userId: number, where?: Prisma.SessionWhereInput) {
    return await this.prisma.session.count({
      where: { ...where, user: { id: userId } },
    });
  }

  async getSession(
    userId: number,
    id: number,
    sessionId?: number,
  ): Promise<Expose<Session & { isCurrentSession: boolean }>> {
    const session = await this.prisma.session.findUnique({
      where: { id },
    });
    if (!session) throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
    if (session.userId !== userId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    if (!session) throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
    return {
      ...this.prisma.expose<Session>(session),
      isCurrentSession: sessionId === session.id,
    };
  }

  async deleteSession(userId: number, id: number): Promise<Expose<Session>> {
    const testSession = await this.prisma.session.findUnique({
      where: { id },
    });
    if (!testSession) throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
    if (testSession.userId !== userId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);
    const session = await this.prisma.session.delete({
      where: { id },
    });
    return this.prisma.expose<Session>(session);
  }

  async getUserSessionWithRefreshToken(userId: number, refresh: string) {
    const session = await this.prisma.session.findFirst({
      where: {
        userId: userId,
        appCenterRefreshToken: {
          not: null,
        },
        token: refresh,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });
    if (!session) {
      throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
    }
    return session;
  }
}
