import { Injectable, Logger } from '@nestjs/common';
import { PromotableService } from '../change-management/interfaces/promotable-service.interface';
import { Prisma, Group, LLMModel } from '@prisma/client';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import { GROUP_MEMBERSHIP_SCOPE_KEY } from 'src/providers/redis/redis.constants';
import { LLMModelSettingResponse } from './dto/llm-model-setting.dto';

@Injectable()
export class LLMModelsSettingService implements PromotableService {
  private logger = new Logger(LLMModelsSettingService.name);

  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  async findOneByGroupId(groupId: number): Promise<LLMModelSettingResponse> {
    try {
      const model: LLMModelSettingResponse = await this.prisma.lLMModel.findFirst({
        where: {
          groupId,
        },
        select: {
          id: true,
          modelId: true,
          active: true,
          showInTeams: true,
          showRefInTeams: true,
          makeLiveToPublic: true,
          canShareChat: true,
          showInOutLook: true,
        },
      });
      if (!model) {
        throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      }
      return model;
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async update(
    modelId: string,
    userId: number,
    active: boolean,
    showInTeams?: boolean,
    showRefInTeams?: boolean,
    makeLiveToPublic?: boolean,
    canShareChat?: boolean,
    showInOutLook?: boolean,
    tx?: Prisma.TransactionClient,
  ) {
    const prismaTx = tx ? tx : this.prisma;
    try {
      const data: Record<string, string | number | unknown> = {
        makeLiveToPublic,
      };
      const model = await this.prisma.lLMModel.findFirst({
        where: {
          modelId,
        },
        include: { group: true, lastModifiedBy: true },
      });

      if (!model) {
        throw new ApiException(ErrorCode.LLM_MODEL_CONFIG_NOT_FOUND);
      }

      if (showInTeams !== null && typeof showInTeams !== 'undefined') {
        data['showInTeams'] = showInTeams;
      }

      if (showInTeams !== null && typeof showInTeams !== 'undefined') {
        data['showRefInTeams'] = showRefInTeams;
      }

      if (showInOutLook !== null && typeof showInOutLook !== 'undefined') {
        data['showInOutLook'] = showInOutLook;
      }

      const updated = await prismaTx.lLMModel.update({
        where: {
          modelId,
        },
        include: {
          group: true,
        },
        data: {
          ...data,
          active,
          canShareChat,
          lastModifiedBy: { connect: { id: userId } },
        },
      });
      if (!updated) {
        throw new ApiException(ErrorCode.FAIL_TO_UPDATE_DB_LLM_MODEL);
      }
      await this.redis.batchClearCache(
        GROUP_MEMBERSHIP_SCOPE_KEY.replace('{USER_ID}', '*').replace(
          '{GROUP_ID}',
          model.groupId.toString(),
        ),
      );
      const resultData: LLMModelSettingResponse = {
        id: updated.id,
        modelId: updated.modelId,
        active: updated.active,
        showInTeams: updated.showInTeams,
        showRefInTeams: updated.showRefInTeams,
        makeLiveToPublic: updated.makeLiveToPublic,
        canShareChat: updated.canShareChat,
        showInOutLook: updated.showInOutLook,
      };
      return resultData;
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async generateEntityDataForSnapshot(groupId: number, entityId: string): Promise<object> {
    const lLMModel = await this.prisma.lLMModel.findFirst({
      where: {
        groupId,
        id: parseInt(entityId),
      },
    });
    if (!lLMModel) throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
    return {
      active: lLMModel.active,
      showInTeams: lLMModel.showInTeams,
      showRefInTeams: lLMModel.showRefInTeams,
      makeLiveToPublic: lLMModel.makeLiveToPublic,
      canShareChat: lLMModel.canShareChat,
      showInOutLook: lLMModel.showInOutLook,
    };
  }

  async deleteEntityDataForSnapshot(
    groupId: number,
    entityId: string,
    entityData: any,
  ): Promise<void> {
    return;
  }
  async promoteCreate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    entityData: any,
    operatorId: number,
  ): Promise<string> {
    const llmModel = await tx.lLMModel.findFirst({
      select: { id: true },
      where: { groupId: targetGroup.id },
    });
    if (!llmModel) throw new ApiException(ErrorCode.LLM_MODEL_NOT_FOUND);
    await this.promoteUpdate(tx, targetGroup, llmModel.id.toString(), entityData, operatorId);
    return llmModel.id.toString();
  }
  async promoteUpdate(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    entityData: LLMModel,
    operatorId: number,
  ): Promise<void> {
    const dstLlmModel = await tx.lLMModel.findUniqueOrThrow({
      where: { id: parseInt(targetEntityId) },
    });

    await tx.lLMModel.update({
      data: {
        lastPromotedAt: new Date(),
      },
      where: {
        id: parseInt(targetEntityId),
      },
    });

    // plug into original update function
    await this.update(
      dstLlmModel.modelId,
      operatorId,
      entityData.active,
      entityData.showInTeams,
      entityData.showRefInTeams,
      entityData.makeLiveToPublic,
      entityData.canShareChat,
      entityData.showInOutLook,
      tx,
    );
  }

  async promoteDelete(
    tx: Prisma.TransactionClient,
    targetGroup: Group,
    targetEntityId: string,
    operatorId: number,
  ): Promise<void> {
    throw new ApiException(ErrorCode.DATA_PROMOTION_DELETE_NOT_SUPPORTED);
  }

  async checkPromotedEntityValid(targetEntityId: string): Promise<object> {
    const llmmodel = await this.prisma.lLMModel.findUnique({
      where: { id: parseInt(targetEntityId) },
    });
    return {
      active: llmmodel.active,
      showInTeams: llmmodel.showInTeams,
      showRefInTeams: llmmodel.showRefInTeams,
      makeLiveToPublic: llmmodel.makeLiveToPublic,
      canShareChat: llmmodel.canShareChat,
      showInOutLook: llmmodel.showInOutLook,
    };
  }
}
