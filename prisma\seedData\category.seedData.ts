import { CategoryType } from '@prisma/client';
import { CategoryDTO } from 'src/modules/category/category.dto';

export const defaultCategory: CategoryDTO[] = [
  // System
  {
    name: 'System',
    key: 'system',
    type: CategoryType.FEATURE_FLAG,
    order: 1,
  },
  {
    name: 'Account',
    key: 'account',
    type: CategoryType.FEATURE_FLAG,
    order: 1,
    parent: 'system',
  },
  {
    name: 'Alert',
    key: 'system-alert',
    type: CategoryType.FEATURE_FLAG,
    order: 2,
    parent: 'system',
  },
  {
    name: 'Announcement',
    key: 'announcement',
    type: CategoryType.FEATURE_FLAG,
    order: 3,
    parent: 'system',
  },
  {
    name: 'Billing',
    key: 'billing',
    type: CategoryType.FEATURE_FLAG,
    order: 4,
    parent: 'system',
  },
  {
    name: 'Common',
    key: 'system-common',
    type: CategoryType.FEATURE_FLAG,
    order: 5,
    parent: 'system',
  },
  {
    name: 'Dashboard',
    key: 'system-dashboard',
    type: CategoryType.FEATURE_FLAG,
    order: 6,
    parent: 'system',
  },
  {
    name: 'Group Management',
    key: 'group-management',
    type: CategoryType.FEATURE_FLAG,
    order: 7,
    parent: 'system',
  },
  {
    name: 'Login',
    key: 'login',
    type: CategoryType.FEATURE_FLAG,
    order: 8,
    parent: 'system',
  },
  {
    name: 'Plan management',
    key: 'plan-management',
    type: CategoryType.FEATURE_FLAG,
    order: 9,
    parent: 'system',
  },
  {
    name: 'Report',
    key: 'report',
    type: CategoryType.FEATURE_FLAG,
    order: 10,
    parent: 'system',
  },
  {
    name: 'Security',
    key: 'system-security',
    type: CategoryType.FEATURE_FLAG,
    order: 11,
    parent: 'system',
  },

  // Bot
  {
    name: 'Bot',
    key: 'bot',
    type: CategoryType.FEATURE_FLAG,
    order: 2,
  },
  {
    name: 'Alert',
    key: 'bot-alert',
    type: CategoryType.FEATURE_FLAG,
    order: 1,
    parent: 'bot',
  },
  {
    name: 'API Key',
    key: 'api-key',
    type: CategoryType.FEATURE_FLAG,
    order: 2,
    parent: 'bot',
  },
  {
    name: 'Call chat',
    key: 'call-chat',
    type: CategoryType.FEATURE_FLAG,
    order: 3,
    parent: 'bot',
  },
  {
    name: 'Chat Setting',
    key: 'chat-setting',
    type: CategoryType.FEATURE_FLAG,
    order: 4,
    parent: 'bot',
  },
  {
    name: 'Chat with Data',
    key: 'chat-with-data',
    type: CategoryType.FEATURE_FLAG,
    order: 5,
    parent: 'bot',
  },
  {
    name: 'Chat with File',
    key: 'chat-with-file',
    type: CategoryType.FEATURE_FLAG,
    order: 6,
    parent: 'bot',
  },
  {
    name: 'Connect API',
    key: 'connect-api',
    type: CategoryType.FEATURE_FLAG,
    order: 7,
    parent: 'bot',
  },
  {
    name: 'Custom Role',
    key: 'custom-role',
    type: CategoryType.FEATURE_FLAG,
    order: 8,
    parent: 'bot',
  },
  {
    name: 'Dashboard',
    key: 'bot-dashboard',
    type: CategoryType.FEATURE_FLAG,
    order: 9,
    parent: 'bot',
  },
  {
    name: 'GenKB',
    key: 'genkb',
    type: CategoryType.FEATURE_FLAG,
    order: 10,
    parent: 'bot',
  },
  {
    name: 'Notification',
    key: 'notification',
    type: CategoryType.FEATURE_FLAG,
    order: 11,
    parent: 'bot',
  },
  {
    name: 'Outlook Addin',
    key: 'outlook-addin',
    type: CategoryType.FEATURE_FLAG,
    order: 12,
    parent: 'bot',
  },
  {
    name: 'Rating Comment',
    key: 'rating-comment',
    type: CategoryType.FEATURE_FLAG,
    order: 13,
    parent: 'bot',
  },
  {
    name: 'Security',
    key: 'bot-security',
    type: CategoryType.FEATURE_FLAG,
    order: 14,
    parent: 'bot',
  },
  {
    name: 'Test automation',
    key: 'test-automation',
    type: CategoryType.FEATURE_FLAG,
    order: 15,
    parent: 'bot',
  },
  {
    name: 'Upload Data',
    key: 'upload-data',
    type: CategoryType.FEATURE_FLAG,
    order: 16,
    parent: 'bot',
  },

  // Insight Generator
  {
    name: 'Insight Generator',
    key: 'insight-generator',
    type: CategoryType.FEATURE_FLAG,
    order: 3,
  },
  {
    name: 'Common',
    key: 'insight-generator-common',
    type: CategoryType.FEATURE_FLAG,
    order: 1,
    parent: 'insight-generator',
  },
  {
    name: 'Instruction Template',
    key: 'instruction-template',
    type: CategoryType.FEATURE_FLAG,
    order: 2,
    parent: 'insight-generator',
  },
  {
    name: 'Insight Settings',
    key: 'insight-settings',
    type: CategoryType.FEATURE_FLAG,
    order: 3,
    parent: 'insight-generator',
  },
  {
    name: 'Notification',
    key: 'insight-generator-notification',
    type: CategoryType.FEATURE_FLAG,
    order: 4,
    parent: 'insight-generator',
  },

  // Flow
  {
    name: 'Flow',
    key: 'flow',
    type: CategoryType.FEATURE_FLAG,
    order: 4,
  },
  {
    name: 'Common',
    key: 'flow-common',
    type: CategoryType.FEATURE_FLAG,
    order: 1,
    parent: 'flow',
  },
  {
    name: 'Playground',
    key: 'playground',
    type: CategoryType.FEATURE_FLAG,
    order: 2,
    parent: 'flow',
  },
  {
    name: 'Chat Setting',
    key: 'chat-setting',
    type: CategoryType.FEATURE_FLAG,
    order: 2,
    parent: 'bot',
  },
];
