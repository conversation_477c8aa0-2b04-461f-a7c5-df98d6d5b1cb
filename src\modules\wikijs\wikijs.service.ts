import { BadRequestException, forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoginType, Prisma, PrismaClient, WikiJSGroupRole, SystemName } from '@prisma/client';

import { GraphQLClient, gql } from 'graphql-request';
import { Configuration } from '../../config/configuration.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { SkipIfNoClient } from './skip-if-no-client.decorator';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { GroupsService } from '../groups/groups.service';
import { wikiJSPermission } from './wikijs.constant';
import {
  CreateWikijsMembershipDTO,
  DeleteWikiMembershipDTO,
  WikiJsUserUpdateResponse,
  WikiPeerDTO,
  WikiPeerKbDTO,
  GenKBDTO,
  GenKBPeerDTO,
} from './wikijs.interface';
import {
  WIKIJS_CHATBOT_CONTENT,
  WIKIJS_CHATBOT_CSS,
  WIKIJS_CHATBOT_JAVASCRIPT,
} from './wikijs-chatbot';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { LLMBackendService } from 'src/providers/llm-backend/llm-backend.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import {
  LLMModelKnowledgeBasePeer,
  LLMModelKnowledgeBase,
} from 'src/providers/llm-backend/llm-backend.interface';
import { GroupInfo } from 'src/modules/groups/groups.dto';
import { WikijsAccessTokenPayload } from '../auth/auth.interface';

@Injectable()
// Advanced implementation: AWS SNS (bot builder event) + AWS lambda
export class WikijsService {
  private logger = new Logger(WikijsService.name);

  private static readonly SKIPPED_WIKIJS_USER_UPDATE_RESPONSE: WikiJsUserUpdateResponse = {
    users: {
      update: {
        responseResult: {
          succeeded: true,
          slug: 'skipped-wikijs-update',
          message: 'Wiki.js user update was intentionally skipped.',
        },
      },
    },
  };

  private defaultPrismaTransactionOption = { timeout: 30000 };

  private defaultLocale = 'en';
  private defaultEditor = 'code';
  private defaultApiKeyExp = 50;

  private graphqlClient: GraphQLClient = null;

  private wikijsConfig: Configuration['wikijs'];

  private isSyncing = false;

  private outSyncUserWhere = {
    wikijsId: null,
    OR: [
      {
        loginType: null,
      },
      {
        loginType: LoginType.HKT,
      },
      {
        loginType: LoginType.LOCAL,
        emails: {
          every: {
            isVerified: true,
          },
        },
      },
    ],
  };

  private KbExistsCode = 1031;

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    @Inject(forwardRef(() => LLMBackendService))
    private llmBackendService: LLMBackendService,
    @Inject(forwardRef(() => LLMModelsService))
    private llmModelsService: LLMModelsService,
    @Inject(forwardRef(() => GroupsService))
    private groupService: GroupsService,
    private tokensService: TokensService,
    private featureFlagService: FeatureFlagService,
  ) {
    this.wikijsConfig = this.configService.get<Configuration['wikijs']>('wikijs');

    if (this.wikijsConfig.host) {
      this.graphqlClient = new GraphQLClient(`${this.wikijsConfig.host}/graphql`, {
        headers: {
          authorization: `Bearer ${this.wikijsConfig.apiKey}`,
        },
      });
    }
  }

  private async createWikijsPage(
    botBuilderGroupId: number, // for creating path
    botBuilderGroupEnv: string,
    botBuilderGroupName: string,
  ): Promise<string> {
    const wikijsGroupName = `[${botBuilderGroupEnv}] ${botBuilderGroupName}`;

    const createWikijsGroupPageMutation = gql`
      mutation (
        $content: String!
        $editor: String!
        $isPublished: Boolean!
        $isPrivate: Boolean!
        $locale: String!
        $path: String!
        $tags: [String]!
        $title: String!
        $description: String!
        $scriptJs: String
        $scriptCss: String
      ) {
        pages {
          create(
            content: $content
            editor: $editor
            isPublished: $isPublished
            isPrivate: $isPrivate
            locale: $locale
            path: $path
            tags: $tags
            title: $title
            description: $description
            scriptJs: $scriptJs
            scriptCss: $scriptCss
          ) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
          }
        }
      }
    `;

    const groupPagePath = `home/${botBuilderGroupId}`;

    const createWikijsGroupPageVariables = {
      content: WIKIJS_CHATBOT_CONTENT,
      scriptJs: WIKIJS_CHATBOT_JAVASCRIPT,
      scriptCss: WIKIJS_CHATBOT_CSS,
      editor: this.defaultEditor,
      isPublished: true,
      isPrivate: false,
      locale: this.defaultLocale,
      path: groupPagePath,
      tags: [],
      title: wikijsGroupName,
      description: '',
    };

    const createWikijsGroupPageResData: any = await this.graphqlClient.request(
      createWikijsGroupPageMutation,
      createWikijsGroupPageVariables,
    );

    this.logger.debug(
      createWikijsGroupPageResData.pages.create.responseResult,
      'createWikijsGroupPageRes',
    );
    return groupPagePath;
  }

  private async createGenkb(
    botBuilderGroupId: number, // for creating path
    botBuilderGroupEnv: string,
    botBuilderGroupName: string,
    ownerEmail: string,
  ): Promise<any> {
    const genKbName = `[${botBuilderGroupEnv}] ${botBuilderGroupName}`;

    const createGenKbMutation = gql`
      mutation ($name: String!, $id: Int, $ownerEmail: String) {
        kbs {
          create(name: $name, id: $id, ownerEmail: $ownerEmail) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
            kb {
              id
              name
              kbGroups {
                groupId
                groupType
              }
              createdAt
              updatedAt
            }
          }
        }
      }
    `;

    const createGenKbVariables = {
      name: genKbName,
      id: botBuilderGroupId,
      ownerEmail: ownerEmail,
    };

    const createGenKbResData: any = await this.graphqlClient.request(
      createGenKbMutation,
      createGenKbVariables,
    );

    this.logger.debug(createGenKbResData.kbs.create.responseResult, 'createGenKbRes');
    return createGenKbResData.kbs.create;
  }

  private async findGenkb(botBuilderGroupId: number): Promise<any> {
    try {
      const query = gql`
        query ($id: Int!) {
          kbs {
            single(id: $id) {
              id
              name
              userCount
              kbGroups {
                groupId
                groupType
              }
              createdAt
              updatedAt
            }
          }
        }
      `;
      const variables = {
        id: botBuilderGroupId,
      };
      const data: any = await this.graphqlClient.request(query, variables);
      this.logger.log(`get kb group detail res - ${JSON.stringify(data)}`);
      if (!data?.kbs?.single) {
        throw new ApiException(ErrorCode.GET_KB_GROUP_DETAIL_FAILED);
      }
      return data.kbs.single;
    } catch (err) {
      throw new ApiException(ErrorCode.GET_KB_GROUP_DETAIL_FAILED);
    }
  }

  private async createWikijsGroup(
    botBuilderGroupEnv: string,
    botBuilderGroupName: string,
    wikijsRole: WikiJSGroupRole,
    groupPagePath: string,
  ): Promise<number> {
    // create group
    const wikijsGroupName = `[${botBuilderGroupEnv}-${wikijsRole}] ${botBuilderGroupName}`;

    const createWikijsGroupMutation = gql`
      mutation ($name: String!) {
        groups {
          create(name: $name) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
            group {
              id
            }
          }
        }
      }
    `;
    const createWikijsGroupVariables = { name: wikijsGroupName };
    const createWikijsGroupResData: any = await this.graphqlClient.request(
      createWikijsGroupMutation,
      createWikijsGroupVariables,
    );

    this.logger.debug(
      createWikijsGroupResData.groups.create.responseResult,
      'createWikijsGroupRes',
    );

    const wikijsGroupId = createWikijsGroupResData.groups.create.group.id;

    // update group for redirectOnLogin, permissions, pageRules
    const updateWikijsGroupMutation = gql`
      mutation (
        $id: Int!
        $name: String!
        $redirectOnLogin: String!
        $permissions: [String]!
        $pageRules: [PageRuleInput]!
      ) {
        groups {
          update(
            id: $id
            name: $name
            redirectOnLogin: $redirectOnLogin
            permissions: $permissions
            pageRules: $pageRules
          ) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
          }
        }
      }
    `;
    // update permissions to the group
    const permissionList =
      wikiJSPermission.find(({ role }) => role === wikijsRole)?.permissionList ?? [];

    const updateWikijsGroupVariables = {
      id: wikijsGroupId,
      name: wikijsGroupName,
      redirectOnLogin: `/${groupPagePath}`,
      permissions: permissionList,
      pageRules: [
        {
          deny: false,
          id: 'groupPage',
          locales: [],
          match: 'EXACT',
          path: groupPagePath,
          roles: ['read:pages', 'read:assets'],
        },
        {
          deny: false,
          id: 'groupFolder',
          locales: [],
          match: 'START',
          path: `${groupPagePath}/`,
          roles: permissionList,
        },
      ],
    };

    const updateWikijsGroupResData: any = await this.graphqlClient.request(
      updateWikijsGroupMutation,
      updateWikijsGroupVariables,
    );

    this.logger.debug(
      updateWikijsGroupResData.groups.update.responseResult,
      'updateWikijsGroupRes',
    );

    return wikijsGroupId;
  }

  private async createGroupAPIKeyAndPassToLlmBackend(
    botBuilderGroupId: number, // for creating path
    botBuilderGroupModelId: string,
    botBuilderGroupEnv: string,
    wikijsGroupId: number,
  ) {
    // create api key
    const createWikijsApiKeyMutation = gql`
      mutation ($name: String!, $expiration: String!, $fullAccess: Boolean!, $group: Int) {
        authentication {
          createApiKey(
            name: $name
            expiration: $expiration
            fullAccess: $fullAccess
            group: $group
          ) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
            key
          }
        }
      }
    `;

    const createWikijsApiKeyVariables = {
      name: botBuilderGroupId.toString(),
      expiration: `${this.defaultApiKeyExp}y`,
      fullAccess: false,
      group: wikijsGroupId,
    };

    const createWikijsApiKeyResData: any = await this.graphqlClient.request(
      createWikijsApiKeyMutation,
      createWikijsApiKeyVariables,
    );

    this.logger.debug(
      createWikijsApiKeyResData.authentication.createApiKey.responseResult,
      'createWikijsApiKeyRes',
    );

    const wikijsGroupApiKey = createWikijsApiKeyResData.authentication.createApiKey.key;

    // send api key to llm model backend
    await this.llmBackendService.updateKbApiKey(
      botBuilderGroupId,
      botBuilderGroupModelId,
      botBuilderGroupEnv,
      wikijsGroupApiKey,
    );
  }

  // find user id by email and providerKey
  private async searchWikijsUser(email: string, providerKey: string): Promise<any> {
    const searchWikijsUserQuery = gql`
      query ($query: String!) {
        users {
          search(query: $query) {
            id
            email
            providerKey
          }
        }
      }
    `;

    const searchWikijsUserVariables = { query: email };

    const searchWikijsUserResData: any = await this.graphqlClient.request(
      searchWikijsUserQuery,
      searchWikijsUserVariables,
    );

    const wikijsUsers: any[] = searchWikijsUserResData.users.search;
    const wikijsUser = wikijsUsers.find((wikijsUser) => wikijsUser.providerKey === providerKey);

    return wikijsUser ? wikijsUser : null;
  }

  async syncWikiJsUserPassword(id: number, newPassword: string): Promise<void> {
    const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      undefined,
      FeatureFlagKey.ENABLE_GEN_KB,
    );
    if (!featureFlag! || !featureFlag.isEnabled) {
      return;
    }
    const updateWikiJsUserMutation = gql`
      mutation ($id: Int!, $newPassword: String) {
        users {
          update(id: $id, newPassword: $newPassword) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
          }
        }
      }
    `;

    const updateWikiJsUserVariables = {
      id,
      newPassword,
    };

    const updateWikiJsUserResData: any = await this.graphqlClient.request(
      updateWikiJsUserMutation,
      updateWikiJsUserVariables,
    );

    this.logger.log(updateWikiJsUserResData, 'updateWikiJsUserRes');
  }

  public async updateWikijsUser(
    wikijsUserId: number,
    data: { email?: string; name?: string; staffId?: string },
  ): Promise<WikiJsUserUpdateResponse> {
    const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      undefined,
      FeatureFlagKey.ENABLE_GEN_KB,
    );
    if (!featureFlag?.isEnabled || !this.graphqlClient) {
      this.logger.log(
        `Wiki.js integration or GenKB feature is disabled, skipping user update for wikijsUserId: ${wikijsUserId}.`,
      );
      return WikijsService.SKIPPED_WIKIJS_USER_UPDATE_RESPONSE;
    }

    const { email, name, staffId } = data;

    // Only proceed if there's something to update
    if (email === undefined && name === undefined && staffId === undefined) {
      this.logger.log(`No relevant fields to update in Wiki.js for wikijsUserId: ${wikijsUserId}.`);
      return WikijsService.SKIPPED_WIKIJS_USER_UPDATE_RESPONSE;
    }

    const updateUserMutation = gql`
      mutation ($id: Int!, $email: String, $name: String, $staffId: String) {
        users {
          update(id: $id, email: $email, name: $name, staffId: $staffId) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
            user {
              id
              name
              email
              staffId
            }
          }
        }
      }
    `;

    const variables: { id: number; email?: string; name?: string; staffId?: string } = {
      id: wikijsUserId,
    };
    if (email !== undefined) variables.email = email;
    if (name !== undefined) variables.name = name;
    if (staffId !== undefined) variables.staffId = staffId;

    try {
      const response: WikiJsUserUpdateResponse = await this.graphqlClient.request(
        updateUserMutation,
        variables,
      );
      if (response?.users?.update?.responseResult?.succeeded) {
        this.logger.log(
          `Successfully updated user ${wikijsUserId} in Wiki.js: ${JSON.stringify(
            response.users.update.user,
          )}`,
        );
      } else {
        this.logger.error(
          `Failed to update user ${wikijsUserId} in Wiki.js: ${response?.users?.update?.responseResult?.message}`,
        );
      }
      return response;
    } catch (error) {
      this.logger.error(error, `Error updating user ${wikijsUserId} in Wiki.js.`);
      throw error;
    }
  }
  private async createWikijsUser(
    tx: Prisma.TransactionClient,
    botBuilderUserId: number,
    botBuilderUserName: string,
    email: string,
    wikijsGroupIds: number[],
    loginType: LoginType,
    passwordRaw?: string,
    ccc?: string,
    staffId?: string,
  ): Promise<number | null> {
    try {
      const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        undefined,
        FeatureFlagKey.ENABLE_GEN_KB,
      );
      if (!featureFlag! || !featureFlag.isEnabled) {
        return;
      }
      const isLocalLoginType = loginType === LoginType.LOCAL;
      if (isLocalLoginType && passwordRaw == null) {
        passwordRaw = await this.tokensService.generateRandomString(8);
      }

      const providerKey = isLocalLoginType ? 'local' : this.wikijsConfig.ssoProviderKey;

      const wikijsUser = await this.searchWikijsUser(email, providerKey);
      let wikijsUserId = wikijsUser ? wikijsUser.id : null;

      // if not exists, create
      if (wikijsUserId == null) {
        const createWikijsUserMutation = gql`
          mutation (
            $email: String!
            $name: String!
            $passwordRaw: String
            $providerKey: String!
            $groups: [Int]!
            $mustChangePassword: Boolean
            $ccc: String
            $staffId: String
          ) {
            users {
              create(
                email: $email
                name: $name
                passwordRaw: $passwordRaw
                providerKey: $providerKey
                groups: $groups
                mustChangePassword: $mustChangePassword
                ccc: $ccc
                staffId: $staffId
              ) {
                responseResult {
                  succeeded
                  errorCode
                  slug
                  message
                }
              }
            }
          }
        `;

        const createWikijsUserVariables = {
          email,
          name: botBuilderUserName.trim() === '' ? email : botBuilderUserName,
          passwordRaw,
          providerKey,
          groups: wikijsGroupIds,
          mustChangePassword: false,
          ccc: ccc ?? '',
          staffId: staffId ?? '',
        };

        if (passwordRaw) {
          createWikijsUserVariables.passwordRaw = passwordRaw;
        }

        const createWikijsUserResData: any = await this.graphqlClient.request(
          createWikijsUserMutation,
          createWikijsUserVariables,
        );
        this.logger.log('createWikijsUserRes', createWikijsUserResData);

        // since user mutation, create operation does not return user object
        // https://github.com/requarks/wiki/blob/main/server/graph/resolvers/user.js#L70
        // search for the user id again
        const newWikiUser = await this.searchWikijsUser(email.toLowerCase(), providerKey);
        wikijsUserId = newWikiUser ? newWikiUser.id : null;
      }

      if (wikijsUserId) {
        this.logger.log(`ready to update wikiUser id - ${wikijsUserId} , email : ${email}`);
        await tx.user.update({
          where: { id: botBuilderUserId },
          data: { wikijsId: wikijsUserId },
        });
      } else {
        this.logger.log(`Wiki js user id is not found -> ${email}`);
      }
      return wikijsUserId;
    } catch (err) {
      this.logger.error(err, 'failed to create wikijs user');
      throw new BadRequestException('Failed to create wikijs user');
    }
  }

  private async assignUserToGroup(wikijsGroupId: number, wikijsUserId: number): Promise<void> {
    try {
      const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        undefined,
        FeatureFlagKey.ENABLE_GEN_KB,
      );
      if (!featureFlag! || !featureFlag.isEnabled) {
        return;
      }
      const mutation = gql`
        mutation ($groupId: Int!, $userId: Int!) {
          groups {
            assignUser(groupId: $groupId, userId: $userId) {
              responseResult {
                succeeded
                errorCode
                slug
                message
              }
            }
          }
        }
      `;
      const variables = { groupId: wikijsGroupId, userId: wikijsUserId };
      const res: any = await this.graphqlClient.request(mutation, variables);
      this.logger.log('assign user in group', res);
    } catch (err) {
      throw new BadRequestException((err as any)?.response?.errors?.[0]?.message);
    }
  }

  private async unassignUserFromGroup(wikijsGroupId: number, wikijsUserId: number): Promise<void> {
    try {
      const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        undefined,
        FeatureFlagKey.ENABLE_GEN_KB,
      );
      if (!featureFlag! || !featureFlag.isEnabled) {
        return;
      }
      const mutation = gql`
        mutation ($groupId: Int!, $userId: Int!) {
          groups {
            unassignUser(groupId: $groupId, userId: $userId) {
              responseResult {
                succeeded
                errorCode
                slug
                message
              }
            }
          }
        }
      `;
      const variables = { groupId: wikijsGroupId, userId: wikijsUserId };
      const res: any = await this.graphqlClient.request(mutation, variables);
      this.logger.log('unassign user in group', res);
    } catch (err) {
      throw new BadRequestException((err as any)?.response?.errors?.[0]?.message);
    }
  }

  private async deleteWikijsGroup(wikijsGroupId: number): Promise<void> {
    const mutation = gql`
      mutation ($id: Int!) {
        groups {
          delete(id: $id) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
          }
        }
      }
    `;
    const variables = { id: wikijsGroupId };
    const data: any = await this.graphqlClient.request(mutation, variables);
    this.logger.debug(data.groups.delete.responseResult, 'deleteWikijsGroupRes');
  }

  private async deleteWikijsUser(wikijsUserId: number): Promise<void> {
    const mutation = gql`
      mutation ($id: Int!, $replaceId: Int!) {
        users {
          delete(id: $id, replaceId: $replaceId) {
            responseResult {
              succeeded
              errorCode
              slug
              message
            }
          }
        }
      }
    `;
    const variables = {
      id: wikijsUserId,
      replaceId: this.wikijsConfig.guestUserId,
    };
    const data: any = await this.graphqlClient.request(mutation, variables);
    this.logger.debug(data.users.delete.responseResult, 'deleteWikijsUserRes');
  }

  private async getUserIdsInGroup(wikijsGroupId: number): Promise<{ id: number }[]> {
    try {
      const query = gql`
        query ($groupId: Int!) {
          groups {
            single(id: $groupId) {
              id
              users {
                id
              }
            }
          }
        }
      `;
      const variables = {
        groupId: wikijsGroupId,
      };
      const data: any = await this.graphqlClient.request(query, variables);
      this.logger.log('get group detail res', data);
      if (!data?.groups?.single?.users) {
        throw new ApiException(ErrorCode.GET_KB_GROUP_DETAIL_FAILED);
      }
      return data.groups.single.users;
    } catch (err) {
      throw new ApiException(ErrorCode.GET_KB_GROUP_DETAIL_FAILED);
    }
  }

  // get Wikijs page
  private async getWikijsPage(path: string, locale = this.defaultLocale): Promise<any> {
    try {
      const singleByPathQuery = gql`
        query ($path: String!, $locale: String!) {
          pages {
            singleByPath(path: $path, locale: $locale) {
              id
              content
              description
              editor
              contentType
              title
              path
              scriptJs
              locale
            }
          }
        }
      `;

      const singleByPathQueryVariables = { path: path, locale: locale };

      return await this.graphqlClient.request(singleByPathQuery, singleByPathQueryVariables);
    } catch (err) {
      this.logger.error((err as any)?.message);
      throw new ApiException(ErrorCode.GET_KB_PAGE_NOT_EXIST);
    }
  }

  // conver Wikijs page
  private async convertPage(id: number, editor: string): Promise<any> {
    try {
      const convertPageMutation = gql`
        mutation ($id: Int!, $editor: String!) {
          pages {
            convert(id: $id, editor: $editor) {
              responseResult {
                succeeded
                errorCode
                slug
                message
              }
            }
          }
        }
      `;

      const convertPageVariables = {
        id: id,
        editor: editor,
      };

      const rst: any = await this.graphqlClient.request(convertPageMutation, convertPageVariables);

      if (!rst.pages?.convert?.responseResult?.succeeded) {
        throw new ApiException(ErrorCode.GET_KB_PAGE_NOT_EXIST);
      }
      return rst;
    } catch (err) {
      this.logger.error((err as any)?.message);
      throw new ApiException(ErrorCode.UPDATE_KB_PAGE_FAILED);
    }
  }

  // update Wikijs page
  private async updatePage(
    id: number,
    params?: {
      content?: string;
      description?: string;
      editor?: string;
      isPublished?: boolean;
      isPrivate?: boolean;
      locale?: string;
      path?: string;
      tags?: [];
      title?: string;
      scriptJs?: string;
      scriptCss?: string;
    },
  ): Promise<any> {
    try {
      const updatePageMutation = gql`
        mutation (
          $id: Int!
          $content: String
          $description: String
          $editor: String
          $isPublished: Boolean
          $isPrivate: Boolean
          $locale: String
          $path: String
          $tags: [String]
          $title: String
          $scriptJs: String
          $scriptCss: String
        ) {
          pages {
            update(
              id: $id
              content: $content
              description: $description
              editor: $editor
              isPublished: $isPublished
              isPrivate: $isPrivate
              locale: $locale
              path: $path
              tags: $tags
              title: $title
              scriptJs: $scriptJs
              scriptCss: $scriptCss
            ) {
              responseResult {
                succeeded
                errorCode
                slug
                message
              }
              page {
                id
                content
                description
                isPublished
                isPrivate
                path
                title
              }
            }
          }
        }
      `;

      const updatePageVariables = {
        id: id,
        content: params?.content,
        description: params?.description,
        editor: params?.editor,
        isPublished: params?.isPublished,
        isPrivate: params?.isPrivate,
        title: params?.title,
        path: params?.path,
        tags: params?.tags,
        scriptJs: params?.scriptJs,
        scriptCss: params?.scriptCss,
        locale: params?.locale,
      };

      const rst: any = await this.graphqlClient.request(updatePageMutation, updatePageVariables);

      if (!rst.pages.update?.responseResult?.succeeded) {
        throw new ApiException(ErrorCode.UPDATE_KB_PAGE_FAILED);
      }

      return rst;
    } catch (err) {
      throw new ApiException(ErrorCode.UPDATE_KB_PAGE_FAILED);
    }
  }

  private async syncUsers(): Promise<void> {
    try {
      // find all users without mapping
      const outSyncUsers = await this.prisma.user.findMany({
        where: this.outSyncUserWhere,
        include: { emails: true, memberships: true },
      });

      for (const outSyncUser of outSyncUsers) {
        await this.prisma.$transaction(async (tx) => {
          if (outSyncUser?.emails[0]?.email) {
            await this.createWikijsUser(
              tx,
              outSyncUser.id,
              outSyncUser.name,
              outSyncUser?.emails[0]?.email,
              [this.wikijsConfig.guestGroupId],
              outSyncUser.loginType,
              outSyncUser.password,
              outSyncUser.ccc,
              outSyncUser.staffId,
            );
          }
        }, this.defaultPrismaTransactionOption);
      }
    } catch (err) {
      this.logger.error(err, 'failed to sync user');
      throw new BadRequestException('Failed to sync user');
    }
  }

  private async unlinkUsers(): Promise<void> {
    try {
      const syncedUsers = await this.prisma.user.findMany({
        where: { NOT: { wikijsId: null } },
        select: { id: true, wikijsId: true },
      });

      for (const syncedUser of syncedUsers) {
        await this.prisma.$transaction(async (tx) => {
          await tx.user.update({
            where: { id: syncedUser.id },
            data: { wikijsId: null },
          });
          await this.deleteWikijsUser(syncedUser.wikijsId);
        }, this.defaultPrismaTransactionOption);
      }
    } catch (err) {
      this.logger.error(err, 'failed to unlink user');
      throw new BadRequestException('Failed to unlink user');
    }
  }

  // @SkipIfNoClient()
  // async createQnaPage(
  //   botBuilderGroupId: number,
  //   question: string,
  //   answer: string,
  // ): Promise<void> {
  //   //TODO: low priority / necessary?
  // }

  @SkipIfNoClient()
  async handleCreateBotBuilderUser(
    botBuilderUserId: number,
    botBuilderUserName: string,
    loginType: LoginType,
    email?: string,
    passwordRaw?: string,
    ccc?: string,
    staffId?: string,
  ): Promise<void> {
    const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      undefined,
      FeatureFlagKey.ENABLE_GEN_KB,
    );
    if (!featureFlag! || !featureFlag.isEnabled) {
      return;
    }
    await this.prisma.$transaction(async (tx) => {
      if (email == null) {
        const botBuilderUser = await tx.user.findUnique({
          where: { id: botBuilderUserId },
          include: { emails: true },
        });
        email = botBuilderUser.emails[0].emailSafe;
      }
      await this.createWikijsUser(
        tx,
        botBuilderUserId,
        botBuilderUserName,
        email,
        [this.wikijsConfig.guestGroupId],
        loginType,
        passwordRaw,
        ccc,
        staffId,
      );
    }, this.defaultPrismaTransactionOption);
  }

  @SkipIfNoClient()
  // should be able to patch out sync data
  async sync(): Promise<void> {
    if (this.isSyncing) {
      throw new ApiException(ErrorCode.KB_ALREADY_SYNCING);
    }
    this.isSyncing = true;
    try {
      await this.syncUsers();
    } finally {
      this.isSyncing = false;
    }
  }

  @SkipIfNoClient()
  async unlink(): Promise<void> {
    if (this.isSyncing) {
      throw new ApiException(ErrorCode.KB_ALREADY_SYNCING);
    }
    this.isSyncing = true;
    try {
      await this.unlinkUsers();
    } finally {
      this.isSyncing = false;
    }
  }

  async getSyncStatus() {
    return await this.prisma.$transaction(async (tx) => {
      const numOfOutSyncUsers = await tx.user.count({
        where: this.outSyncUserWhere,
      });
      return {
        numOfOutSyncUsers,
        isReady: this.graphqlClient != null,
        isSyncing: this.isSyncing,
      };
    }, this.defaultPrismaTransactionOption);
  }

  @SkipIfNoClient()
  async getEnabledStatus(groupId: number): Promise<boolean> {
    try {
      const group = await this.groupService.getGroup(groupId, {});
      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
      const { knowledge_base } = await this.llmBackendService.getModel(
        group.env.toLowerCase(),
        llmModel.modelId,
      );
      return knowledge_base?.enable ?? false;
    } catch (err) {
      this.logger.error(err, ErrorCode.GET_KB_STATUS_FAILED);
      throw new ApiException(ErrorCode.GET_KB_STATUS_FAILED);
    }
  }

  @SkipIfNoClient()
  async enableWikiJS(groupId: number) {
    try {
      const groupWikijsList = await this.prisma.groupWikijs.findMany({
        where: { groupId },
      });
      const group = await this.groupService.getGroup(groupId, {});
      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);

      const membership = await this.prisma.membership.findFirst({
        where: { groupId, Role: { systemName: SystemName.GROUP_OWNER } },
        include: { group: true, user: true, Role: true },
      });

      const { email } = await this.prisma.email.findFirst({
        where: { userId: membership.userId },
      });

      // Create resource on wikijs if there are no wikijs id found
      if (groupWikijsList.length === 0) {
        this.logger.log(`Created wikijs groups  -${groupId} ${group.name}`);

        const genkbCreateRst = await this.createGenkb(groupId, group.env, group.name, email);

        if (!genkbCreateRst.responseResult.succeeded) {
          if (genkbCreateRst.responseResult.errorCode !== this.KbExistsCode) {
            throw new ApiException(ErrorCode.CHANGE_KB_STATUS_FAILED);
          }
          // Enable wikijs on bot level
          this.logger.log('Enabling wikijs on bot level');
          return await this.llmBackendService.enableKb(group.env, llmModel.modelId, true);
        }

        const editorGroupId = genkbCreateRst.kb?.kbGroups.find(
          (item) => item.groupType === 'EDITOR',
        )?.groupId;
        const viewerGroupId = genkbCreateRst.kb?.kbGroups.find(
          (item) => item.groupType === 'VIEWER',
        )?.groupId;

        await this.prisma.$transaction([
          this.prisma.groupWikijs.create({
            data: {
              role: 'VIEWER',
              wikijsId: viewerGroupId,
              groupId,
            },
          }),
          this.prisma.groupWikijs.create({
            data: {
              role: 'EDITOR',
              wikijsId: editorGroupId,
              groupId,
            },
          }),
        ]);
        this.logger.log(
          `Created wikijs groups successfully ! editorGroupId: ${editorGroupId} viewerGroupId: ${viewerGroupId}`,
        );
      }

      const genkbRst = await this.findGenkb(groupId);

      const editorGroupId = genkbRst?.kbGroups.find((item) => item.groupType === 'EDITOR')?.groupId;
      this.logger.log(`editor groupId: ${editorGroupId}`);

      // check whether api key token is passed to gpt service or not
      const { knowledge_base } = await this.llmBackendService.getModel(
        group.env.toLowerCase(),
        llmModel.modelId,
      );
      if (!knowledge_base?.token) {
        this.logger.log(`Created api key: editorGroupId: ${editorGroupId}`);
        await this.createGroupAPIKeyAndPassToLlmBackend(
          groupId,
          llmModel.modelId,
          group.env,
          editorGroupId,
        );
        this.logger.log(`Created api key successfully, editorGroupId : ${editorGroupId}`);
      }

      // Enable wikijs on bot level
      this.logger.log('Enabling wikijs on bot level');
      return await this.llmBackendService.enableKb(group.env, llmModel.modelId, true);
    } catch (err) {
      this.logger.error(err, 'failed to enable wikijs');
      throw new ApiException(ErrorCode.CHANGE_KB_STATUS_FAILED);
    }
  }

  @SkipIfNoClient()
  async disableWikiJS(groupId: number) {
    try {
      // Disable wikijs on bot level
      const group = await this.groupService.getGroup(groupId, {});
      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
      return await this.llmBackendService.enableKb(group.env, llmModel.modelId, false);
    } catch (err) {
      this.logger.error(err, 'failed to disable wikijs');
      throw new ApiException(ErrorCode.CHANGE_KB_STATUS_FAILED);
    }
  }

  @SkipIfNoClient()
  async createMembership(data: CreateWikijsMembershipDTO, groupId: number) {
    try {
      return await this.prisma.$transaction(async (tx) => {
        let wikijsUserId;
        // get wikijs of user and group
        const wikiJSGroup = await tx.groupWikijs.findFirst({ where: { groupId, role: data.role } });
        const user = await tx.user.findFirst({
          where: { emails: { some: { email: data.email } } },
        });
        if (!user) {
          throw new ApiException(ErrorCode.USER_NOT_EXIST);
        }
        const membershipCount = await tx.membership.count({ where: { userId: user.id, groupId } });
        if (membershipCount === 0) {
          throw new ApiException(ErrorCode.MEMBERSHIP_NOT_EXIST);
        }
        // async the user to wikijs
        if (!user.wikijsId) {
          wikijsUserId = await this.createWikijsUser(
            tx,
            user.id,
            user.name.trim() !== '' ? user.name : user.id.toString(),
            data.email,
            [this.wikijsConfig.guestGroupId],
            user.loginType,
          );
        } else {
          wikijsUserId = user.wikijsId;
        }
        // assign user into group by using wikijs id
        await this.assignUserToGroup(wikiJSGroup.wikijsId, wikijsUserId);
        return { ...user, wikiJSGroupId: wikiJSGroup.wikijsId, role: data.role };
      });
    } catch (err) {
      this.logger.error(err, 'failed to create wikijs membership');
      throw new BadRequestException(err);
    }
  }

  @SkipIfNoClient()
  async deleteMembership(data: DeleteWikiMembershipDTO, groupId: number) {
    try {
      // get wikijs of user and group
      const user = await this.prisma.user.findUnique({ where: { id: data.userId } });
      if (!user || !user.wikijsId) {
        throw new ApiException(ErrorCode.USER_NOT_EXIST);
      }
      for (const role of data.roles) {
        const wikiJSGroup = await this.prisma.groupWikijs.findFirst({ where: { groupId, role } });
        await this.unassignUserFromGroup(wikiJSGroup.wikijsId, user.wikijsId);
      }
      return { ...user, roles: data.roles };
    } catch (err) {
      this.logger.error(err, 'failed to delete wikijs membership');
      throw new BadRequestException(err);
    }
  }

  @SkipIfNoClient()
  async getUsersInGroupById(groupId: number) {
    try {
      // get wikijs group ids
      const users = [];
      const wikiJSGroups = await this.prisma.groupWikijs.findMany({ where: { groupId } });
      for (const wikiJsGroup of wikiJSGroups) {
        const userIds = await this.getUserIdsInGroup(wikiJsGroup.wikijsId);

        for (const userId of userIds) {
          // get back user information from wikijs user id
          const user = await this.prisma.user.findFirst({
            where: { wikijsId: userId.id },
            include: { emails: true },
          });
          users.push({ ...user, wikiJSGroupId: wikiJsGroup.wikijsId, role: wikiJsGroup.role });
        }
      }
      return users;
    } catch (err) {
      this.logger.error(err, 'failed to get wikijs memberships');
      throw new BadRequestException(err);
    }
  }

  async isGroupInWikijs(groupId: number, wikijsGroupIds: number[]) {
    const groupWikijs = await this.prisma.groupWikijs.findFirst({
      where: {
        groupId,
        wikijsId: {
          in: wikijsGroupIds,
        },
      },
    });
    if (!groupWikijs) throw new ApiException(ErrorCode.GROUP_WIKIJS_NOT_FOUND);
    return true;
  }
  @SkipIfNoClient()
  async updateGenKbChatBot(groupId: number) {
    const chatbotPath = `home/${groupId}`;
    const existingPage = await this.getWikijsPage(chatbotPath);
    let outdated = false;

    if (existingPage.pages.singleByPath.editor != this.defaultEditor) {
      //convert page
      await this.convertPage(existingPage.pages.singleByPath.id, this.defaultEditor);
    }

    if (existingPage.pages.singleByPath.contentType != 'html') {
      outdated = true;
    }
    const existingJs = existingPage.pages.singleByPath.scriptJs;
    if (existingJs !== WIKIJS_CHATBOT_JAVASCRIPT) {
      outdated = true;
    }

    if (!outdated) {
      return;
    }

    const rst = await this.updatePage(existingPage.pages.singleByPath.id, {
      content: WIKIJS_CHATBOT_CONTENT,
      description: '',
      scriptJs: WIKIJS_CHATBOT_JAVASCRIPT,
      scriptCss: WIKIJS_CHATBOT_CSS,
      isPublished: true,
      editor: this.defaultEditor,
      tags: [],
    });
    this.logger.log(rst);
  }

  @SkipIfNoClient()
  async updateWikiJSLocalPassword(email: string, password: string) {
    const featureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      undefined,
      FeatureFlagKey.ENABLE_GEN_KB,
    );
    if (!featureFlag! || !featureFlag.isEnabled) {
      return;
    }
    try {
      const wikijsUser = await this.searchWikijsUser(email, 'local');

      if (!wikijsUser) {
        const user = await this.prisma.user.findFirst({
          where: { emails: { some: { email } } },
          include: { emails: true },
        });
        if (!user) {
          // try to create account if not exists
          await this.prisma.$transaction(async (tx) => {
            await this.createWikijsUser(
              tx,
              user.id,
              user.name,
              user?.emails[0]?.email,
              [this.wikijsConfig.guestGroupId],
              LoginType.LOCAL,
              password,
              user.ccc,
              user.staffId,
            );
          }, this.defaultPrismaTransactionOption);
        }
      } else {
        // patch password
        await this.syncWikiJsUserPassword(wikijsUser.id, password);
      }
    } catch (err) {
      this.logger.error(err, 'failed to update user password');
      throw new BadRequestException(err);
    }
  }

  @SkipIfNoClient()
  async listWikiPeer(groupId: number) {
    try {
      const group = await this.groupService.getGroup(groupId, {});
      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
      const llmModelResponse = await this.llmBackendService.getModel(
        group.env.toLowerCase(),
        llmModel.modelId,
      );

      return this.toWikiPeerKbDto(llmModelResponse?.knowledge_base);
    } catch (err) {
      this.logger.error(err, 'failed to list wiki peer group');
      if (err instanceof ApiException) {
        throw err; // Rethrow original exception
      }
      throw new ApiException(ErrorCode.KB_PEER_NOT_FOUND);
    }
  }

  @SkipIfNoClient()
  async listGenKB(groupId: number) {
    try {
      const group = await this.groupService.getGroup(groupId, {});
      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
      const llmModelResponse = await this.llmBackendService.getModel(
        group.env.toLowerCase(),
        llmModel.modelId,
      );

      return this.toGenKbDto(group, llmModelResponse?.knowledge_base);
    } catch (err) {
      this.logger.error(err, 'failed to list wiki peer group');
      if (err instanceof ApiException) {
        throw err; // Rethrow original exception
      }
      throw new ApiException(ErrorCode.KB_PEER_NOT_FOUND);
    }
  }

  @SkipIfNoClient()
  async appendWikiPeer(groupId: number, wikiPeerDto: WikiPeerDTO) {
    try {
      // validate peer group
      await this.groupService.getGroup(wikiPeerDto.kbId, {});
      const group = await this.groupService.getGroup(groupId, {});

      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
      const { knowledge_base } = await this.llmBackendService.getModel(
        group.env.toLowerCase(),
        llmModel.modelId,
      );
      const kbPeers = knowledge_base?.peer_kbs || [];

      const kbPeer: LLMModelKnowledgeBasePeer = {
        kb_id: wikiPeerDto.kbId,
        token: wikiPeerDto.token,
        description: wikiPeerDto?.description ?? '',
        name: wikiPeerDto?.name ?? '',
      };

      if (kbPeers) {
        // append to kb_peers if kb_id not exists
        const kbPeerIndex = kbPeers.findIndex((peer) => peer.kb_id === wikiPeerDto.kbId);
        if (kbPeerIndex === -1) {
          kbPeers.push(kbPeer);
        } else {
          kbPeers[kbPeerIndex] = kbPeer;
        }
      } else {
        kbPeers.push(kbPeer);
      }

      const llmModelResponse = await this.llmBackendService.updateKbPeerApiKey(
        group.env,
        llmModel.modelId,
        kbPeers,
      );

      return this.toWikiPeerKbDto(llmModelResponse?.knowledge_base);
    } catch (err) {
      this.logger.error(err, 'failed to add genkb peer');
      if (err instanceof ApiException) {
        throw err; // Rethrow original exception
      }
      throw new ApiException(ErrorCode.ADD_KB_PEER_NOT_FAILED);
    }
  }

  @SkipIfNoClient()
  async removeWikiPeer(groupId: number, kbId: number) {
    try {
      // Disable wikijs on bot level
      // validate peer group
      await this.groupService.getGroup(kbId, {});
      const group = await this.groupService.getGroup(groupId, {});
      const llmModel = await this.llmModelsService.getLLMModelByGroupId(groupId);
      const { knowledge_base } = await this.llmBackendService.getModel(
        group.env.toLowerCase(),
        llmModel.modelId,
      );

      if (!knowledge_base?.peer_kbs) {
        throw new ApiException(ErrorCode.KB_PEER_NOT_FOUND);
      }

      // delete certain kb_peer if kb_id exists
      const kbPeerIndex = knowledge_base.peer_kbs.findIndex((peer) => peer.kb_id === kbId);

      if (kbPeerIndex === -1) {
        throw new ApiException(ErrorCode.KB_PEER_NOT_FOUND);
      }

      const kbPeers = knowledge_base.peer_kbs.filter((_, index) => index !== kbPeerIndex);

      // update peer_kbs
      const llmModelResponse = await this.llmBackendService.updateKbPeerApiKey(
        group.env,
        llmModel.modelId,
        kbPeers,
      );

      return this.toWikiPeerKbDto(llmModelResponse?.knowledge_base);
    } catch (err) {
      this.logger.error(err, 'failed to remove genkb peers');
      if (err instanceof ApiException) {
        throw err; // Rethrow original exception
      }
      throw new ApiException(ErrorCode.KB_PEER_NOT_FOUND);
    }
  }

  private toWikiPeerKbDto(knowledgeBase: LLMModelKnowledgeBase): WikiPeerKbDTO {
    if (!knowledgeBase || !knowledgeBase?.peer_kbs) {
      return {
        peerKbs: null,
      };
    }

    const peerKbDtos: WikiPeerDTO[] = knowledgeBase.peer_kbs.map((item) => {
      return {
        kbId: item.kb_id,
        token: item.token,
        description: item?.description ?? '',
        name: item?.name ?? '',
      };
    });

    return {
      peerKbs: peerKbDtos,
    };
  }

  private toGenKbDto(group: GroupInfo, knowledgeBase: LLMModelKnowledgeBase): GenKBPeerDTO {
    if (!knowledgeBase) {
      return {
        genkb: null,
        peerKbs: null,
      };
    }

    const genKb: GenKBDTO = knowledgeBase?.enable
      ? {
          kbId: group.id,
          description: '',
          name: group.name,
          default: true,
        }
      : null;

    const peerKbDtos: GenKBDTO[] =
      knowledgeBase?.peer_kbs && knowledgeBase?.peer_kbs.length > 0
        ? knowledgeBase?.peer_kbs.map((item) => {
            const wikijsPlayload = this.tokensService.decode<WikijsAccessTokenPayload>(item.token);
            const expiredAt = wikijsPlayload?.exp ? new Date(wikijsPlayload.exp * 1000) : null;

            return {
              kbId: item.kb_id,
              description: item?.description ?? '',
              name: item?.name ?? '',
              default: false,
              expiredAt: expiredAt,
            };
          })
        : null;

    return {
      genkb: genKb,
      peerKbs: peerKbDtos,
    };
  }
}
