import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsString } from 'class-validator';

export enum BatchAppWhitelistAction {
  ADD = 'add',
  REMOVE = 'remove',
}

class AppWhitelistItem {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  appType: string;

  @IsEnum(BatchAppWhitelistAction)
  action: BatchAppWhitelistAction;
}

export class BatchAppWhitelistDto {
  @ApiProperty({ type: [AppWhitelistItem] })
  @IsArray()
  @Type(() => AppWhitelistItem)
  items: AppWhitelistItem[];
}
