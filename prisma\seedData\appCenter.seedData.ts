import { AppType, LaunchType, Prisma } from '@prisma/client';

// all data can using the https://www.npmjs.com/package/jsonpath jsonpath to request . the current content you will get group info, user info, appResource info and app info.

export const appList: Prisma.AppCreateInput[] = [
  {
    name: 'N8N',
    clientId: 'oauth2_proxy',
    tokenCookiesName: 'tokens',
    appType: AppType.INDEPENDENCE,
    launchType: LaunchType.REDIRECT,
    needCreateKeyCloakResource: false,
    launchBaseUrl: process.env?.['N8N_BASE_LAUNCH_URL'] ?? '',
    whitelistControl: true,
    createParamConfig: {
      url: 'http://n8n-1/webhook/app-center-n8n-creation',
      method: 'POST',
      headers: {
        'x-api-key': process.env?.['N8N_APP_CENTER_API_KEY'],
        'Content-type': 'application/json',
      },
      data: {
        email: 'user.emails[0].email',
        groupId: 'group.id',
      },
    },
  },
  {
    name: 'Document AI (Invited users only)',
    clientId: 'document-ai',
    tokenCookiesName: 'tokens',
    appType: AppType.BOT_TOOL,
    launchType: LaunchType.REDIRECT,
    needCreateKeyCloakResource: true,
    launchBaseUrl: process.env?.['DOCUMENT_AI_BASE_LAUNCH_URL'] ?? '',
    createParamConfig: {
      url: process.env?.['DOCUMENT_AI_APP_CENTER_WEBHOOK']+"?name=Document+AI&clientId=document-ai&applicationType=Document+AI",
      method: 'POST',
      headers: {
        'x-api-key': process.env?.['DOCUMENT_AI_APP_CENTER_API_KEY'] ?? '',
        'Content-type': 'application/json',
      },
      data: {
        groupId: 'group.id',
        groupName: 'group.name',
        groupEnv: 'group.env',
        groupType: 'group.groupType',
      },
    },
    removeParamConfig: {
      url: process.env?.['DOCUMENT_AI_APP_CENTER_WEBHOOK']+"?name=Document+AI&clientId=document-ai&applicationType=Document+AI",
      method: 'DELETE',
      headers: {
        'x-api-key': process.env?.['DOCUMENT_AI_APP_CENTER_API_KEY'] ?? '',
        'Content-type': 'application/json',
      },
      data: {
        groupId: 'group.id',
        groupName: 'group.name',
        groupEnv: 'group.env',
        groupType: 'group.groupType',
      },
    },	
  },
  {
    name: 'Tender Bot (Invited users only)',
    clientId: 'document-ai',
    tokenCookiesName: 'tokens',
    appType: AppType.BOT_TOOL,
    launchType: LaunchType.REDIRECT,
    needCreateKeyCloakResource: true,
    launchBaseUrl: process.env?.['DOCUMENT_AI_BASE_LAUNCH_URL'] ?? '',
    createParamConfig: {
      url: process.env?.['DOCUMENT_AI_APP_CENTER_WEBHOOK']+"?name=Tender+Bot&clientId=document-ai-tender-bot&applicationType=Tender+Bot",
      method: 'POST',
      headers: {
        'x-api-key': process.env?.['DOCUMENT_AI_APP_CENTER_API_KEY'] ?? '',
        'Content-type': 'application/json',
      },
      data: {
        groupId: 'group.id',
        groupName: 'group.name',
        groupEnv: 'group.env',
        groupType: 'group.groupType',
      },
    },
    removeParamConfig: {
      url: process.env?.['DOCUMENT_AI_APP_CENTER_WEBHOOK']+"?name=Tender+Bot&clientId=document-ai-tender-bot&applicationType=Tender+Bot",
      method: 'DELETE',
      headers: {
        'x-api-key': process.env?.['DOCUMENT_AI_APP_CENTER_API_KEY'] ?? '',
        'Content-type': 'application/json',
      },
      data: {
        groupId: 'group.id',
        groupName: 'group.name',
        groupEnv: 'group.env',
        groupType: 'group.groupType',
      },
    },	
  },
  {
    name: 'Sales Tender Bot (Invited users only)',
    clientId: 'document-ai',
    tokenCookiesName: 'tokens',
    appType: AppType.BOT_TOOL,
    launchType: LaunchType.REDIRECT,
    needCreateKeyCloakResource: true,
    launchBaseUrl: process.env?.['DOCUMENT_AI_BASE_LAUNCH_URL'] ?? '',
    createParamConfig: {
      url: process.env?.['DOCUMENT_AI_APP_CENTER_WEBHOOK']+"?name=Sales+Tender+Bot&clientId=document-ai-sales-tender-bot&applicationType=Sales+Tender+Bot",
      method: 'POST',
      headers: {
        'x-api-key': process.env?.['DOCUMENT_AI_APP_CENTER_API_KEY'] ?? '',
        'Content-type': 'application/json',
      },
      data: {
        groupId: 'group.id',
        groupName: 'group.name',
        groupEnv: 'group.env',
        groupType: 'group.groupType',
      },
    },
    removeParamConfig: {
      url: process.env?.['DOCUMENT_AI_APP_CENTER_WEBHOOK']+"?name=Sales+Tender+Bot&clientId=document-ai-sales-tender-bot&applicationType=Sales+Tender+Bot",
      method: 'DELETE',
      headers: {
        'x-api-key': process.env?.['DOCUMENT_AI_APP_CENTER_API_KEY'] ?? '',
        'Content-type': 'application/json',
      },
      data: {
        groupId: 'group.id',
        groupName: 'group.name',
        groupEnv: 'group.env',
        groupType: 'group.groupType',
      },
    },	
  },  
];
