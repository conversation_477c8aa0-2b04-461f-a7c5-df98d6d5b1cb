import { MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import configuration from './config/configuration';
import { AuditLogger } from './interceptors/audit-log.interceptor';
import { ApiLoggerMiddleware } from './middleware/api-logger.middleware';
import { JsonBodyMiddleware } from './middleware/json-body.middleware';
import { RawBodyMiddleware } from './middleware/raw-body.middleware';
import { ApiKeysModule } from './modules/api-keys/api-keys.module';
import { ApprovedSubnetsModule } from './modules/approved-subnets/approved-subnets.module';
import { AuditLogsModule } from './modules/audit-logs/audit-logs.module';
import { AuthModule } from './modules/auth/auth.module';
import { ScopesGuard } from './modules/auth/scope.guard';
import { StaartAuthGuard } from './modules/auth/staart-auth.guard';
import { DomainsModule } from './modules/domains/domains.module';
import { GroupsModule } from './modules/groups/groups.module';
import { MembershipsModule } from './modules/memberships/memberships.module';
import { SessionsModule } from './modules/sessions/sessions.module';
import { UsersModule } from './modules/users/users.module';
import { MessageTemplateModule } from './modules/message-template/message-template.module';
import { WebhooksModule } from './modules/webhooks/webhooks.module';
import { CloudinaryModule } from './providers/cloudinary/cloudinary.module';
import { DnsModule } from './providers/dns/dns.module';
import { ElasticSearchModule } from './providers/elasticsearch/elasticsearch.module';
import { GeolocationModule } from './providers/geolocation/geolocation.module';
import { GitHubModule } from './providers/github/github.module';
import { GoogleMapsModule } from './providers/google-maps/google-maps.module';
import { MailModule } from './providers/mail/mail.module';
import { PuppeteerModule } from './providers/puppeteer/puppeteer.module';
import { PrismaModule } from './providers/prisma/prisma.module';
import { S3Module } from './providers/s3/s3.module';
import { RedisModule } from './providers/redis/redis.module';
import { SlackModule } from './providers/slack/slack.module';
import { TasksModule } from './providers/tasks/tasks.module';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { MetricsModule } from './modules/metrics/metrics.module';
import { MetaModule } from './modules/meta/meta.module';
import { LLMModelsModule } from './modules/llm-models/llm-models.module';
import { HealthCheckModule } from './modules/health-check/health-check.module';
import { EmbeddingsModule } from './modules/embeddings/embeddings.module';
import { RolesModule } from './modules/roles/roles.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { LlmEnginesModule } from './modules/llm-engines/llm-engines.module';
import { QueueModule } from 'src/providers/queue/queue.module';
import { FileHistoryModule } from './modules/file-history/file-history.module';
import { AiResourcesModule } from './modules/ai-resources/ai-resources.module';
import { VersionModule } from './modules/version/version.module';
import { BatchesModule } from './modules/batches/batches.module';
import { FeatureFlagModule } from './modules/feature-flags/feature-flags.module';
import { LoggerModule } from 'nestjs-pino';
import { ChangeManagementModule } from './modules/change-management/change-management.module';
import { InternationalizationModule } from './modules/internationalization/internationalization.module';
import { ChatSessionsModule } from './modules/chat-sessions/chat-sessions.module';
import { FlowsModule } from './modules/flows/flows.module';
import { FlowBotsModule } from './modules/flow-bots/flow-bots.module';
import { ApiResourceModule } from './modules/api-resource/api-resource.module';
import { FlowBotRequestsModule } from './modules/flow-bot-requests/flow-bot-requests.module';
import { MutilpleLevelFeatureFlagsModule } from './modules/mutilple-level-feature-flags/mutilple-level-feature-flags.module';
import { RedisCacheModule } from './modules/redis-cache/redis-cache.module';

import { pinoOptionConfig } from './config/pino-logger.config';
import { SummaryModule } from './modules/summary/summary.module';
import { ScopeMapGuard } from './modules/auth/scope-map.guard';
import { FeatureFlagGuard } from './modules/feature-flags/feature-flags.guard';
import { InjectResponseHeaderMiddleware } from './middleware/inject-res-header.middleware';
import { OptionsModule } from './modules/options/options.module';
import { ChatFilesModule } from './modules/chat-files/chat-files.module';
import { AlertModule } from './modules/alert/alert.module';
import { BotSecurityModule } from './modules/bot-security/bot-security.module';
import { InsightModule } from './modules/insight/insight.module';
import { RateLimitModule } from './providers/rate-limit/rate-limit.module';
import { TestCaseModule } from './modules/test-case/test-case.module';
import { AutoTestModule } from './providers/test-case/auto-test.module';
import { ExcelModel } from './providers/excel/excel.model';
import { UserGroupModule } from './modules/user-group/user-group.module';
import { BroadcastModule } from './modules/broadcast/broadcast.module';
import { NotificationBackendModule } from './providers/notification-backend/notification-backend.module';
import { UserNotificationModule } from './modules/user-notification/user-notification.module';
import { LabelsModule } from './modules/labels/labels.module';
import { PlansModule } from './modules/plans/plans.module';
import { QuotaModule } from './modules/quotas/quota.module';
import { ModelPriceModule } from './modules/model-price/model-price.module';
import { GroupNotificationModule } from './modules/group-notification/group-notification.module';
import { BullMqModule } from './providers/bullmq/bullmq.module';
import { OpenAIModule } from './modules/open-ai/open-ai.module';
import { ShareChatModule } from './modules/share-chat/share-chat.module';
import { AppCenterModule } from './modules/app-center/app-center.module';
import { TestModule } from './modules/test/test.module';
import { CategoryModule } from './modules/category/category.module';
import { GroupSettingsModule } from './modules/group-settings/group-settings.module';
import { AppWhitelistModule } from './modules/app-whitelist/app-whitelist.module';
import { SchedulerJobModule } from './modules/scheduler-job/scheduler-job.module';
import { FileConversionCallbackModule } from './modules/file-conversion-callbacks/file-conversion-callbacks.module';

// import { MulterModule } from '@nestjs/platform-express';

// import { MulterHelper } from './helpers/multer';
// import { diskStorage } from 'multer';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
    }),
    BullMqModule,
    ScheduleModule.forRoot(),
    PrismaModule,
    TasksModule,
    UsersModule,
    AuthModule,
    MailModule,
    OptionsModule,
    SessionsModule,
    GroupsModule,
    ApiKeysModule,
    ApprovedSubnetsModule,
    DomainsModule,
    DnsModule,
    GeolocationModule,
    MembershipsModule,
    AuditLogsModule,
    WebhooksModule,
    ElasticSearchModule,
    SlackModule,
    S3Module,
    QueueModule,
    CloudinaryModule,
    GitHubModule,
    GoogleMapsModule,
    PuppeteerModule,
    MetricsModule,
    MetaModule,
    LLMModelsModule,
    HealthCheckModule,
    EmbeddingsModule,
    RolesModule,
    PermissionsModule,
    LlmEnginesModule,
    FileHistoryModule,
    AiResourcesModule,
    MessageTemplateModule,
    VersionModule,
    BatchesModule,
    FeatureFlagModule,
    ChangeManagementModule,
    InternationalizationModule,
    ChatSessionsModule,
    FlowsModule,
    FlowBotsModule,
    ApiResourceModule,
    FlowBotRequestsModule,
    SummaryModule,
    RedisCacheModule,
    RedisModule,
    MutilpleLevelFeatureFlagsModule,
    ChatFilesModule,
    AlertModule,
    BotSecurityModule,
    InsightModule,
    RateLimitModule,
    AutoTestModule,
    TestCaseModule,
    ExcelModel,
    UserGroupModule,
    BroadcastModule,
    NotificationBackendModule,
    UserNotificationModule,
    LabelsModule,
    PlansModule,
    QuotaModule,
    ModelPriceModule,
    GroupNotificationModule,
    OpenAIModule,
    ShareChatModule,
    AppCenterModule,
    TestModule,
    CategoryModule,
    GroupSettingsModule,
    AppWhitelistModule,
    SchedulerJobModule,
    FileConversionCallbackModule,
    // MulterModule.register({
    //   storage: diskStorage({
    //     destination: MulterHelper.destination,
    //     filename: MulterHelper.filenameHandler,
    //   }),
    // }),
    LoggerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        return { pinoHttp: pinoOptionConfig(configService.get('NODE_ENV')) };
      },
    }),
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: StaartAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ScopesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ScopeMapGuard,
    },
    {
      provide: APP_GUARD,
      useClass: FeatureFlagGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: AuditLogger,
    },
  ],
})
export class AppModule implements NestModule {
  public configure(consumer: MiddlewareConsumer): void {
    consumer
      .apply(RawBodyMiddleware)
      .forRoutes({
        path: '/webhooks/stripe',
        method: RequestMethod.POST,
      })
      .apply(InjectResponseHeaderMiddleware)
      .forRoutes('*')
      .apply(JsonBodyMiddleware)
      .forRoutes('*')
      .apply(ApiLoggerMiddleware)
      .forRoutes('*');
  }
}
