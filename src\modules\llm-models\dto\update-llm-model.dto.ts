import { OmitType } from '@nestjs/swagger';
import { CreateLlmModelDto } from './create-llm-model.dto';

import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';
import { LlmModelParamsDto } from './llm-model-params.dto';
import { PatchLabelsDto } from 'src/modules/labels/dto/patch-labels.dto';

export class UpdateLlmModelDto extends OmitType(CreateLlmModelDto, ['name', 'groupId'] as const) {
  @IsOptional()
  @IsNumber()
  llmEngineId: number;

  @ValidateNested()
  @Type(() => LlmModelParamsDto)
  @IsObject()
  @IsOptional()
  parameters?: LlmModelParamsDto;

  @IsOptional()
  @IsString()
  modelEngine: string;

  @IsBoolean()
  @IsOptional()
  showInTeams?: boolean;

  @IsOptional()
  @IsBoolean()
  active: boolean;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  messageTemplateId?: number;

  @IsString()
  @IsOptional()
  typeDefinition?: string;

  @IsOptional()
  @IsBoolean()
  showReference?: boolean;

  @IsBoolean()
  @IsOptional()
  makeLiveToPublic?: boolean;

  @IsBoolean()
  @IsOptional()
  showInOutLook?: boolean;
}

export class UpdatePublicLlmModelDto {
  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  entityLabels?: PatchLabelsDto[];

  @IsNumber()
  @IsOptional()
  publicOrder?: number;

  @IsString()
  @IsOptional()
  publicBotGuideDetails?: string;

  @IsBoolean()
  @IsOptional()
  official?: boolean;
}
export class BatchUpdatePublicLlmModelDto {
  @IsOptional()
  ordering: number;

  @IsString()
  botName: string;

  @IsNumber()
  botId: number;

  @IsBoolean()
  @IsOptional()
  official?: boolean;

  @Type(() => PatchLabelsDto)
  @IsOptional()
  categories?: PatchLabelsDto[];

  @Type(() => PatchLabelsDto)
  @IsOptional()
  labels?: PatchLabelsDto[];

  @IsString()
  @IsOptional()
  publicBotGuideDetails?: string;
}

export class BatchUpdateDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchUpdatePublicLlmModelDto)
  llmModels: BatchUpdatePublicLlmModelDto[];
}
