import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { ConfigService } from '@nestjs/config';
import { Configuration } from 'src/config/configuration.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { InsightTemplateDto } from './insight.dto';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';

@Injectable()
export class InsightTemplateService {
  private logger = new Logger(InsightTemplateService.name);
  axios?: AxiosInstance;
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private featureFlagService: FeatureFlagService,
  ) {
    const config = this.configService.get<Configuration['insightGenerator']>('insightGenerator');
    if (config) {
      this.axios = axios.create({
        baseURL: config.host,
        timeout: config.timeout,
      });
    } else {
      this.logger.error('No LLM backend URL set.');
      throw new Error('No LLM Backend URL set.');
    }
  }

  async getInsightTemplates(groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight templates  - ${groupId}`);
      const res = await this.axios.get(`/insight-generator/${groupId}/templates?${queryStr}`);
      const getList = res.data.list.map(async (item) => {
        const creator = await this.prisma.user.findUnique({
          where: { id: item.createdBy },
          select: { name: true, id: true },
        });
        let updatedBy = null;
        if (item.updatedBy) {
          updatedBy = await this.prisma.user.findUnique({
            where: { id: item.updatedBy },
            select: { name: true, id: true },
          });
        }
        return { ...item, createdBy: creator, updatedBy };
      });
      const list = await Promise.all(getList);
      return { list, count: res.data.count };
    } catch (err) {
      this.logger.error(err, `getting insight templates failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getDefaultInsightTemplate(groupId: number) {
    try {
      this.logger.log(`getting default insight template`);
      const defaultTemplate = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
        groupId,
        FeatureFlagKey.INSIGHT_GENERATOR_DEFAULT_INSIGHT_TEMPLATE,
      );
      return defaultTemplate;
    } catch (err) {
      this.logger.error(err, `getting default insight template failed`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightTemplateById(id: number, groupId: number) {
    try {
      this.logger.log(`getting insight template by id  - ${id}`);
      const res = await this.axios.get(`/insight-generator/${groupId}/template/${id}`);
      const creator = await this.prisma.user.findUnique({
        where: { id: res.data.createdBy },
        select: { name: true, id: true },
      });
      let updatedBy = null;
      if (res.data.updatedBy) {
        updatedBy = await this.prisma.user.findUnique({
          where: { id: res.data.updatedBy },
          select: { name: true, id: true },
        });
      }
      return { ...res.data, createdBy: creator, updatedBy };
    } catch (err) {
      this.logger.error(err, `getting insight template by id failed - ${id}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async createTemplate(
    groupId: number,
    userId: number,
    createInsightTemplateDto: InsightTemplateDto,
  ) {
    try {
      this.logger.log(`creating insight template  - ${groupId}`);
      const res = await this.axios.post(`/insight-generator/${groupId}/template`, {
        ...createInsightTemplateDto,
        createdBy: userId,
      });
      return res.data;
    } catch (err) {
      this.logger.error(err, `creating insight template failed - ${groupId}`);
      throw new ApiException(ErrorCode.CREATE_INSIGHT_FAILED);
    }
  }

  async updateTemplate(
    groupId: number,
    id: number,
    userId: number,
    updateInsightTemplateDto: InsightTemplateDto,
  ) {
    try {
      this.logger.log(`updating insight template  - ${groupId}`);
      const res = await this.axios.patch(`/insight-generator/${groupId}/template/${id}`, {
        ...updateInsightTemplateDto,
        updatedBy: userId,
      });

      return res.data;
    } catch (err) {
      this.logger.error(err, `updating insight template failed - ${groupId}`);
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async deleteTemplate(groupId: number, id: number) {
    try {
      this.logger.log(`deleting insight template  - ${groupId}`);
      const res = await this.axios.delete(`/insight-generator/${groupId}/template/${id}`);
      return res.data;
    } catch (err) {
      this.logger.error(err, `deleting insight template failed - ${groupId}`);
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }
}
