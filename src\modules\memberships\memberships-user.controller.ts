import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
} from '@nestjs/common';
import { Membership, Environment, Prisma, GroupType } from '@prisma/client';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { Scopes } from '../auth/scope.decorator';
import { CreateGroupDto } from '../groups/groups.dto';
import { ExtendedMembership, MembershipsService } from './memberships.service';
import _ from 'lodash';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { LastUsedGroupsDto } from './memberships.dto';
import { NewWherePipe } from 'src/pipes/new-where.pipe';
import { Public } from '../auth/public.decorator';

export type EnvSpecificMembership = Record<'TEST' | 'PROD', Membership>;
export type CreateGroupResponse = {
  id: number;
  name: string;
} & EnvSpecificMembership;

@Controller('users/:userId/memberships')
@ApiBearerAuth('bearer-auth')
@ApiTags('User Membership')
export class UserMembershipController {
  logger = new Logger(UserMembershipController.name);
  constructor(private membershipsService: MembershipsService) {}

  @Post('/bot')
  @Scopes('system:create-bot')
  async createBot(
    @Param('userId', ParseIntPipe) userId: number,
    @Body() data: CreateGroupDto,
  ): Promise<Membership[]> {
    const response = await this.membershipsService.createBotAndUserMembership(userId, data);
    return response;
  }

  @Post('/flow')
  @Scopes('system:create-flow')
  async createFlow(
    @Param('userId', ParseIntPipe) userId: number,
    @Body() data: CreateGroupDto,
  ): Promise<Membership[]> {
    const testGroupMembership = await this.membershipsService.createFlowAndUserMembership(
      userId,
      FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST,
      {
        ...data,
        env: Environment.TEST,
      },
    );
    const prodGroupMembership = await this.membershipsService.createFlowAndUserMembership(
      userId,
      FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD,
      {
        ...data,
        env: Environment.PROD,
      },
    );
    await this.membershipsService.updateGroupPairId(
      testGroupMembership.group.id,
      prodGroupMembership.group.id,
    );
    return [testGroupMembership, prodGroupMembership];
  }

  @Post('/insight')
  @Scopes('system:create-insight')
  async createInsightGenerator(
    @Param('userId', ParseIntPipe) userId: number,
    @Body() data: CreateGroupDto,
  ): Promise<Membership[]> {
    const testGroupMembership =
      await this.membershipsService.createInsightGeneratorAndUserMembership(
        userId,
        FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST,
        {
          ...data,
          env: Environment.TEST,
        },
      );
    const prodGroupMembership =
      await this.membershipsService.createInsightGeneratorAndUserMembership(
        userId,
        FeatureFlagKey.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD,
        {
          ...data,
          env: Environment.PROD,
        },
      );
    await this.membershipsService.updateGroupPairId(
      testGroupMembership.group.id,
      prodGroupMembership.group.id,
    );
    return [testGroupMembership, prodGroupMembership];
  }

  /** Get memberships for a user */
  @Get()
  @Scopes('user-{userId}:read-membership')
  async getAll(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', NewWherePipe) where?: Prisma.MembershipWhereInput,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<{
    list: Expose<ExtendedMembership>[];
    count: number;
    lastUsedGroups: LastUsedGroupsDto;
  }> {
    const newWhere: Record<string, any> = {
      ...where,
      user: {
        ...(where?.user ?? {}),
        id: userId,
      },
      group: {
        ...(where?.group ?? {}),
        isDeprecated: false,
      },
    };
    const list = await this.membershipsService.getMemberships({
      skip,
      take,
      orderBy,
      where: newWhere,
    });
    const lastUsedGroups = await this.membershipsService.getLastUsedGroups(userId);
    const count = await this.membershipsService.getMembershipsCount(newWhere);
    return { list, count, lastUsedGroups };
  }

  @Get(':env/list')
  @Scopes('user-{userId}:read-membership')
  async getList(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('env') env: Environment,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ): Promise<Expose<ExtendedMembership>[]> {
    const newWhere: Record<string, any> = {
      user: { 
        id: userId 
      }, 
      group: {
        env,
        isDeprecated: false,
      },
    };
    const memberships = await this.membershipsService.getMemberships({
      where: newWhere,
      orderBy,
    });

    return memberships;
  }

  /** Get a membership for a user */
  @Get(':id')
  @Scopes('user-{userId}:read-membership')
  async get(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Membership>> {
    return this.membershipsService.getUserMembership(userId, id);
  }

  /** Delete a membership for a user */
  @Delete(':id')
  @Scopes('user-{userId}:delete-membership')
  async remove(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Expose<Membership>> {
    return this.membershipsService.deleteUserMembership(userId, id);
  }
}
