APP_NAME: HKT GPT AI Bot Builder - <env>
AL<PERSON>OW_DISPOSABLE_EMAILS: 'true'
AWS_S3_ENCRYPTION_KEY_ARN: <key create in kms, for s3 encrytion>
AWS_S3_PROFILE_PICTURE_BUCKET: gpt-saas-dev
AWS_S3_REGION: ap-east-1
AWS_S3_STATIC_FILES_BUCKET_PROD: bot-builder-files-<env>-live
AWS_S3_STATIC_FILES_BUCKET_TEST: bot-builder-files-<env>-test
BRUTE_FREE_RETRIES: '50'
BRUTE_LIFETIME: '300000'
CACHE_CHECK_PERIOD: '1000'
CACHE_TTL: '600'
DB_HOST: <DB_HOST>
DB_NAME: botbuilderdb
DB_PORT: <port of the db>
DB_PROVIDER: postgresql
DB_USER: botbuilder
DISALLOW_OPEN_CORS: 'false'
ELASTIC<PERSON>ARCH_FAIL_RETRIES: '1'
ELASTICSEARCH_NODES: >-
  <opensearch vpc path>
EMAIL_FROM: <EMAIL>
EMAIL_HOST: smtp.mandrillapp.com
EMAIL_NAME: HKT GPT AI Bot Builder - <env> - Account Management
EMAIL_PORT: '587'
EMAIL_SECURE: 'true'
EMAIL_USER: hktgpt2023
FRONTEND_URL: <bot-builder-frontend-url>
GITHUB_CLIENT_ID: github-oauth2-client-id
GITHUB_CLIENT_SECRET: oauth2-client-secret
GOOGLE_CLIENT_ID: google-oauth2-client-id
GOOGLE_CLIENT_SECRET: oauth2-client-secret
MAXMIND_ACCOUNT_ID: '857445'
MICROSOFT_CLIENT_ID: microsoft-oauth2-client-id
MICROSOFT_CLIENT_SECRET: oauth2-client-secret
NODE_ENV: development
OPENAI_BACKEND_URL: <lambda gpt-service url>
PORT: '3001'
PUPPETEER_SKIP_DOWNLOAD: 'true'
RATE_LIMIT_MAX: '1000'
RATE_LIMIT_TIME: '60000'
REDIS_TLS: boolean
SENTRY_DSN: >-
  https://<EMAIL>/**************** #currently no use
SPEED_LIMIT_COUNT: '500'
SPEED_LIMIT_DELAY: '100'
SPEED_LIMIT_TIME: '600000'
STRIPE_PRODUCT_ID: stripe-product-id
STRIPE_SECRET_KEY: stripe-test-api-key
TOKEN_EXPIRY_API_KEY_MAX: '**************'
TOKEN_EXPIRY_APPROVE_LOCATION: 10m
TOKEN_EXPIRY_EMAIL_VERIFICATION: 7d
TOKEN_EXPIRY_LOGIN: 15m
TOKEN_EXPIRY_PASSWORD_RESET: 1d
TOKEN_EXPIRY_REFRESH: 30d
TRACKING_DELETE_OLD_LOGS: 'true'
TRACKING_DELETE_OLD_LOGS_DAYS: 'true'
TRACKING_INDEX: bot-builder-logs
TRACKING_MODE: all
GRAVITEE_ORG_ID: DEFAULT
GRAVITEE_ENV_ID: DEFAULT
GRAVITEE_HOST: http://ec2-18-162-136-95.ap-east-1.compute.amazonaws.com:8083
BOT_API_ID: 7a6f5904-d18b-4302-af59-04d18b53024e
API_PATH_PATTERN: "/groupId/(.*)/(.*)/(.*)"
AWS_SQS_REGION: ap-east-1
AWS_SQS_GEN_RES_LIVE_QUEUE_URL: https://sqs.ap-east-1.amazonaws.com/432797229265/imaging-stable-dffision-live
AWS_SQS_GEN_RES_TEST_QUEUE_URL: https://sqs.ap-east-1.amazonaws.com/432797229265/imaging-stable-dffision-test
AWS_S3_LOG_FILES_BUCKET_TEST: <The bucket name of log file test>
AWS_S3_LOG_FILES_BUCKET_PROD: <The bucket name of log file live>
AWS_SQS_LOG_FILES_QUEUE_URL: <The url of log file queue>
ACCESS_TOKEN_EXPIRY: 15m
REFRESH_TOKEN_EXPIRY: 1h
CONTEXT_PATH: v1
HKT_EMAIL_SUFFIX: pccw.com
VERIFICATION_CODE_EXPIRY_DAY: 1
WIKIJS_HOST: http://localhost:3005
WIKIJS_SSO_PROVIDER_KEY: d8882ded-6113-4c40-a0fe-dae0308d3252
WIKIJS_GUEST_GROUP_ID: 3 # default group for user, id 3 suppose stand for default group
WIKIJS_TOKEN_ISSUER: urn:wiki.js
SSO_HKT_URL: https://uiauth.hkt.com/auth/realms/HKT_LDAP/protocol/openid-connect
SSO_HKT_USER_INFO_ENDPOINT: /userinfo
SSO_HKT_HEALTH_CHECK_ENDPOINT: /certs
WIKIJS_GENKB_CHAT_HOST_CONTEXT: https://djwsp25c6gf5s.cloudfront.net/v1
SWAGGER_USER: swagger basic auth user
SWAGGER_PASSWORD: swagger basic auth password
FLOWISE_BACKEND_URL: http://localhost:8080
FLOWISE_API_LOG: false # to control print all call flowise api log
FLOWISE_API_LOG_INCLUDE_HEADER: false # to control print all call flowise api show header into the  log
FLOWISE_API_KEY_HEADER: "flowise-integration-shared-secret"
BACKEND_ENV: current env e.g. PROD, DEV/SIT, LOCAL
BASE_URL:  A string value for the currently url
LOG_FILE_ROTATE_SUFFIX: ".txt" # to control log file rotate suffix
LOG_FILE_BASE_PATH: '/tmp/' # to control log file dir path
# B: Bites ,K: KiloBites M: MegaBytes, G: GigaBytes
LOG_FILE_ROTATE_SIZE: "50M" # to control max log file size 
LOG_FILE_MAX_FILE_HISTORY: 5 # to control max log file number 
CHANGE_MANAGEMENT_APPROVAL_TRANSACTION_TIMEOUT: 6000 # timeout for Change Management approval's prisma transaction
MAX_UPLOADED_FILES_PER_USER: '10000' # to control max upload files number
CONFIG_SERVICE_GET_CONFIG_URL: to load external config, not required in local
POSTMAN_MONITOR_GROUP_ID: The group ID for Postman Monitors testing
POSTMAN_MONITOR_SCOPES: The permissions that allowed for Postman Monitors API Key to be used
NOTIFICATION_BACKEND_URL: notification backend url
APP_CENTER_CONFIG: ''
KEYCLOAK_BASE_URL: ''
KEYCLOAK_REALM: ''
KEYCLOAK_ADMIN_USERNAME: ''
KEYCLOAK_ADMIN_PASSWORD: ''
KEYCLOAK_GROUP_ROLE_MAPPING_KEY: ''
KEYCLOAK_BACKEND_CLIENT_SECRET: ''
KEYCLOAK_BACKEND_CLIENT_ID: ''