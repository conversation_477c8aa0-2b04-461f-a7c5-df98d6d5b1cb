import { IsString, <PERSON>NotEmpty, IsInt, IsOptional } from 'class-validator';
import { CreateGroupDto } from 'src/modules/groups/groups.dto';

export class CreateBotToolsDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  appType: string;

  @IsNotEmpty()
  @IsInt()
  groupId: number;
}

export class CreateAppDto extends CreateGroupDto {
  @IsNotEmpty()
  @IsString()
  appType: string;
}
