-- Update ChatSession settings for ApiResources that are enabled
UPDATE "ChatSession"
SET "chatSetting" = jsonb_set(
            "chatSetting" #- '{dataSource,connectApi}',
            '{dataSource,connectApi}',
            '{"enable": true}'::jsonb
    )
WHERE "groupId" IN (
    SELECT "groupId"
    FROM "ApiResource"
    WHERE "enable" = true
) and "chatSetting" is not null;

-- Update LLMModel settings for ApiResources that are enabled
update "LLMModel"
set parameters = jsonb_set(
            "parameters" #- '{dataSource,connectApi}',
            '{dataSource,connectApi}',
            '{"enable": true}'::jsonb
    )
WHERE "groupId" IN (
    SELECT "groupId"
    FROM "ApiResource"
    WHERE "enable" = true
) and "parameters" is not null ;