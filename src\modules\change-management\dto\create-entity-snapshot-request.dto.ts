import { ApiProperty } from '@nestjs/swagger';
import { SnapshotEntityType } from '@prisma/client';
import { IsEnum, IsString, ValidateNested } from 'class-validator';

export class CreateEntitySnapshotRequestDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: SnapshotEntityType })
  @IsEnum(SnapshotEntityType)
  entityType: SnapshotEntityType;

  @ApiProperty()
  @IsString()
  entityId: string;
}
