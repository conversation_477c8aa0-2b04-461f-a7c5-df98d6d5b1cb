import { Body, Controller, Param, ParseIntPipe, Post } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { Scopes } from '../auth/scope.decorator';
import { ModelFileUploadDto } from './insight.dto';
import { InsightInputFileService } from './inputFile.service';

@Controller('insights/input-file')
export class InsightInputFileController {
  logger = new Logger(InsightInputFileController.name);
  constructor(private insightInputFileService: InsightInputFileService) {}

  @Scopes('group-{groupId}:write-insight')
  @Post('/:groupId/model-files')
  async uploadInsightModelFiles(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() body: ModelFileUploadDto,
  ) {
    return await this.insightInputFileService.uploadInsightModelFiles(groupId, body);
  }
}
