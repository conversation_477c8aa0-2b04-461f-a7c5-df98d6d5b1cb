import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';

import { Scopes } from '../auth/scope.decorator';
import { SchedulerJobService } from './scheduler-job.service';
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { NewWherePipe } from 'src/pipes/new-where.pipe';
import {
  CreateSchedulerJobDto,
  SchedulerJobWhereDto,
  UpdateSchedulerJobDto,
} from './scheduler-job.dto';
import { UserRequest } from '../auth/auth.interface';
import { SchedulerJobStatus } from '@prisma/client';

@Controller('groups/:groupId/scheduler-job')
@ApiBearerAuth('bearer-auth')
@ApiTags('Scheduler Job')
export class SchedulerJobController {
  constructor(private schedulerJobService: SchedulerJobService) {}

  // get scheduler jobs
  @Get()
  @Scopes('group-{groupId}:read-scheduler-job', 'group-*:read-scheduler-job')
  async findAllByGroupId(
    @Req() request: UserRequest,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', NewWherePipe) where?: SchedulerJobWhereDto,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
    @Query('includeDeleted', OptionalIntPipe) includeDeleted?: boolean,
  ) {
    const list = await this.schedulerJobService.getSchedulerJobs({
      request,
      groupId,
      skip,
      take,
      orderBy,
      where,
      includeDeleted,
    });
    const count = await this.schedulerJobService.getSchedulerJobsCount(
      request,
      groupId,
      where,
      includeDeleted,
    );
    return { list, count };
  }

  // create scheduler job
  @Post()
  @Scopes('group-{groupId}:write-scheduler-job', 'group-*:write-scheduler-job')
  async create(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() createSchedulerJobDto: CreateSchedulerJobDto,
    @Req() request: UserRequest,
  ) {
    return await this.schedulerJobService.createSchedulerJob(
      groupId,
      createSchedulerJobDto,
      request,
    );
  }

  // update scheduler job
  @Patch(':id')
  @Scopes('group-{groupId}:write-scheduler-job', 'group-*:write-scheduler-job')
  async update(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSchedulerJobDto: UpdateSchedulerJobDto,
    @Req() request: UserRequest,
  ) {
    return await this.schedulerJobService.updateSchedulerJob(id, updateSchedulerJobDto, request);
  }

  // delete scheduler job
  @Delete(':id')
  @Scopes('group-{groupId}:write-scheduler-job', 'group-*:write-scheduler-job')
  async remove(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Req() request: UserRequest,
  ) {
    return await this.schedulerJobService.deleteSchedulerJob(id, request);
  }

  // active/paused/completed scheduler job
  @Patch(':id/status/:status')
  @Scopes(
    'group-{groupId}:write-scheduler-job',
    'group-*:write-scheduler-job',
    'update-scheduler-job-status-internal',
  )
  async updateStatus(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
    @Param('status') status: SchedulerJobStatus,
    @Req() request: UserRequest,
  ) {
    return await this.schedulerJobService.updateSchedulerJobStatus(id, status, request);
  }
}
