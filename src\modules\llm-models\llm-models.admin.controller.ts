import {
  Get,
  Query,
  Patch,
  Param,
  ParseIntPipe,
  Req,
  Body,
  Injectable,
  Controller,
  Put,
  Post,
  Res,
} from '@nestjs/common';
import { UserRequest } from '../auth/auth.interface';
import { LLMModelsService } from './llm-models.service';
import { BatchUpdateDto, UpdatePublicLlmModelDto } from './dto/update-llm-model.dto';
import { Scopes } from '../auth/scope.decorator';
import { ApiBearerAuth } from '@nestjs/swagger';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { PrismaPaginationDto } from 'src/providers/prisma/prisma.interface';
import { LLMModel } from '@prisma/client';
import { PublicLLMModelWhereDto } from './dto/list-llm-model.dto';
import { Response } from 'express';
import { Readable } from 'stream';

@Injectable()
@Controller('llm-models/admin')
@ApiBearerAuth('bearer-auth')
export class LLMModelsAdminController {
  constructor(private readonly lLMModelsService: LLMModelsService) {}

  @Put('/:groupId/public/enable')
  @Scopes('group-*:read-info')
  @AuditLog('enable public bot')
  public async enablePublicBot(@Param('groupId', ParseIntPipe) groupId: number) {
    const updated = await this.lLMModelsService.changePublicBotStatus(groupId, true);
    return updated;
  }

  @Put('/:groupId/public/disable')
  @Scopes('group-*:read-info')
  @AuditLog('disable public bot')
  public async disablePublicBot(@Param('groupId', ParseIntPipe) groupId: number) {
    const updated = await this.lLMModelsService.changePublicBotStatus(groupId, false);
    return updated;
  }

  @Patch('/public/:llmModelId')
  @Scopes('group-*:read-info')
  public async updatePublicBot(
    @Param('llmModelId', ParseIntPipe) llmModelId: number,
    @Req() req: UserRequest,
    @Body() data: UpdatePublicLlmModelDto,
  ) {
    const updated = await this.lLMModelsService.updatePublicBot(llmModelId, req, data);
    return updated;
  }

  @Get('public')
  @Scopes('group-*:read-info')
  public async getPublicGroup(@Query() page: PublicLLMModelWhereDto) {
    const publicGroups = await this.lLMModelsService.getAdminPublicLLMModels(page);
    return publicGroups;
  }

  @Post('/batch')
  @Scopes('group-*:read-info')
  @AuditLog('batch update public llm models')
  public async batchUpdatePublicLlmModels(@Body() data: BatchUpdateDto, @Req() req: UserRequest) {
    return this.lLMModelsService.batchUpdatePublicLlmModels(data, req);
  }

  @Get('/export')
  @Scopes('group-*:read-info')
  public async exportLlmModels(@Res() res: Response) {
    const jsonData = await this.lLMModelsService.exportLlmModels();
    const stream = Readable.from(JSON.stringify(jsonData, null, 2));
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=llm-models.json');
    stream.pipe(res);
  }
}
