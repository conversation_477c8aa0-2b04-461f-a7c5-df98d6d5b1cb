import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { MessageTemplate, Prisma, TemplateAccessLevel } from '@prisma/client';
import { CursorPipe } from '../../pipes/cursor.pipe';
import { OptionalIntPipe } from '../../pipes/optional-int.pipe';
import { OrderByPipe } from '../../pipes/order-by.pipe';
import { WherePipe } from '../../pipes/where.pipe';
import { Expose } from '../../providers/prisma/prisma.interface';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import {
  BatchUploadDto,
  CreateMessageTemplateDto,
  UpdateMessageTemplateDto,
} from './message-template.dto';
import { MessageTemplateService } from './message-template.service';
import { Public } from '../auth/public.decorator';
import papa from 'papaparse';
import { Readable } from 'stream';
import { Response } from 'express';

@Controller('message-templates')
@ApiBearerAuth('bearer-auth')
@ApiTags('System Message Template')
export class SystemMessageTemplateController {
  constructor(private messageTemplateService: MessageTemplateService) {}

  /** Create message templates */
  @Post()
  @AuditLog('create-message-template')
  @Scopes('system:write-message-template')
  async create(
    @Body() data: CreateMessageTemplateDto,
    @Req() request: UserRequest,
  ): Promise<MessageTemplate> {
    data.accessLevel = TemplateAccessLevel.SYSTEM;
    return await this.messageTemplateService.createMessageTemplate(data, request);
  }

  @Get('/public')
  @Public()
  async getPublicMessageTemplate(
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const publicMessageTemplate = await this.messageTemplateService.getPublicMessageTemplate(
      where,
      skip,
      take,
      orderBy,
    );
    return publicMessageTemplate;
  }

  /** Get message templates */
  @Get()
  @Scopes('system:read-message-template')
  async getAll(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
    @Query('type') type?: 'GROUP' | 'SYSTEM' | 'ALL',
  ): Promise<{ list: Expose<MessageTemplate[]>; count: number }> {
    const _orderBy = orderBy ?? { systemSequence: Prisma.SortOrder.asc };
    const list = await this.messageTemplateService.getMessageTemplates(
      { skip, take, orderBy: _orderBy, where },
      undefined,
      undefined,
      type,
    );
    const count = await this.messageTemplateService.getMessageTemplatesCount(
      where,
      undefined,
      undefined,
      type,
    );
    return {
      list,
      count,
    };
  }

  /** Get a message template */
  @Get(':messageTemplateId')
  @Scopes('system:read-message-template')
  async get(
    @Param('messageTemplateId', ParseIntPipe) id: number,
  ): Promise<Expose<MessageTemplate>> {
    return await this.messageTemplateService.getMessageTemplate(id);
  }

  /** Update a message template */
  @Patch(':messageTemplateId')
  @AuditLog('update-message-template')
  @Scopes('system:write-message-template')
  async update(
    @Param('messageTemplateId', ParseIntPipe) id: number,
    @Body() data: UpdateMessageTemplateDto,
    @Req() request: UserRequest,
  ): Promise<Expose<MessageTemplate>> {
    return await this.messageTemplateService.updateMessageTemplate(id, data, request);
  }

  /** Delete a message template */
  @Delete(':messageTemplateId')
  @AuditLog('delete-message-template')
  @Scopes('system:delete-message-template')
  async remove(@Param('messageTemplateId', ParseIntPipe) id: number) {
    return await this.messageTemplateService.deleteMessageTemplate(id);
  }

  /** Batch upload message templates */
  @Post('/batch')
  @Scopes('system:write-message-template')
  async createBatch(@Body() data: BatchUploadDto, @Req() request: UserRequest) {
    return await this.messageTemplateService.createBatch(data, request);
  }

  /** Export system message templates as JSON */
  @Get('/batch/exportMessageTemplates')
  @Scopes('system:write-message-template')
  async exportMessageTemplates(
    @Query('type') type: 'GROUP' | 'SYSTEM' | 'ALL',
    @Res() res: Response,
  ) {
    const jsonData = await this.messageTemplateService.exportMessageTemplates(type);
    const stream = Readable.from(JSON.stringify(jsonData, null, 2));
    stream.pipe(res);
  }
}
