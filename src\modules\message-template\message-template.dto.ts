import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsPositive,
  IsBoolean,
  ValidateNested,
  IsArray,
  IsNotEmpty,
} from 'class-validator';

import { TemplateAccessLevel } from '@prisma/client';
import { Transform, Type } from 'class-transformer';
import { PatchLabelsDto } from '../labels/dto/patch-labels.dto';

export class CreateMessageTemplateDto {
  @IsString()
  @MaxLength(50)
  title: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  sequence?: number;

  @IsString()
  @IsOptional()
  botInstruction: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  systemSequence?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  groupSequence?: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  messageContent: string;

  @IsBoolean()
  @IsOptional()
  isRecommended: boolean;

  @IsOptional()
  accessLevel: TemplateAccessLevel;

  @IsOptional()
  groupId: number;

  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  entityLabels?: PatchLabelsDto[];

  @IsOptional()
  templateGuideDetails: string;

  @IsString()
  slug: string;
}

export class UpdateMessageTemplateDto {
  @IsString()
  @MaxLength(50)
  @IsOptional()
  title?: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  systemSequence?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  groupSequence?: number;

  @IsString()
  @IsOptional()
  botInstruction?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  messageContent?: string;

  @IsBoolean()
  @IsOptional()
  isRecommended?: boolean;

  @IsOptional()
  accessLevel: TemplateAccessLevel;

  @IsOptional()
  groupId: number;
  @Type(() => PatchLabelsDto)
  @ValidateNested({ each: true })
  @IsOptional()
  entityLabels?: PatchLabelsDto[];

  @IsOptional()
  templateGuideDetails: string;

  @IsString()
  slug: string;
}

export class BatchUploadMessageTemDto {
  @IsOptional()
  ordering?: number;

  @IsOptional()
  templateName?: string;

  @IsOptional()
  templateInstruction?: string;

  @IsOptional()
  isActive?: boolean;

  @IsOptional()
  isRecommended?: boolean;

  @Type(() => PatchLabelsDto)
  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (error) {
        throw new Error('Invalid JSON string for categories');
      }
    }
    return value;
  })
  categories?: PatchLabelsDto[];

  @IsOptional()
  botId?: string;

  @IsOptional()
  public?: boolean;

  @Type(() => PatchLabelsDto)
  @IsOptional()
  @Transform((value) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (error) {
        throw new Error('Invalid JSON string for labels');
      }
    }
    return value;
  })
  labels?: PatchLabelsDto[];

  @IsOptional()
  templateDescription?: string;

  @IsOptional()
  templateGuideDetails?: string;

  @IsString()
  slug: string;
}
export class BatchUploadDto {
  @IsArray()
  @Type(() => String)
  @IsOptional()
  deleteSlugs?: string[];

  @IsArray()
  @Type(() => BatchUploadMessageTemDto)
  @IsOptional()
  messageTemplateList?: BatchUploadMessageTemDto[];
}
