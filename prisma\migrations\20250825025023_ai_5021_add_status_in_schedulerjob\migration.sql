-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "SchedulerJobStatus" ADD VALUE 'COMPLETED';
ALTER TYPE "SchedulerJobStatus" ADD VALUE 'DELETED';

-- CreateIndex
CREATE INDEX "SchedulerJob_groupId_featureType_status_idx" ON "SchedulerJob"("groupId", "featureType", "status");