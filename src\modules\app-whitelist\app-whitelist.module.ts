import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { KeycloakModule } from '../../providers/keycloak/keycloak.module';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { UsersModule } from '../users/users.module';
import { AppWhitelistController } from './app-whitelist.controller';
import { AppWhitelistService } from './app-whitelist.service';
import { AppCenterModule } from '../app-center/app-center.module';

@Module({
  imports: [PrismaModule, KeycloakModule, UsersModule, ConfigModule, AppCenterModule],
  providers: [AppWhitelistService],
  exports: [AppWhitelistService],
  controllers: [AppWhitelistController],
})
export class AppWhitelistModule {}
