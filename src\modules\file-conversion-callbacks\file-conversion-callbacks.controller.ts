import { <PERSON>, Post, Body, Logger } from '@nestjs/common';
import { LLMModelsService } from '../llm-models/llm-models.service';
import { CallbackPayloadDto } from './dto/file-conversion-callbacks.dto';
import { FileConversionService } from 'src/providers/file-conversion/file-conversion.service';
import { Scopes } from '../auth/scope.decorator';

@Controller('file-conversion-callbacks')
export class FileConversionCallbackController {
  private logger = new Logger(FileConversionCallbackController.name);

  constructor(
    private readonly llmModelsService: LLMModelsService,
    private readonly fileConversionService: FileConversionService,
  ) {}

  @Post('callback')
  @Scopes('file-conversion-callback')
  async handleConversionCallback(@Body() payload: CallbackPayloadDto) {
    this.logger.log(
      `Received file conversion callback for docId: ${payload.docId}, jobId: ${payload.jobId}, status: ${payload.status}`,
    );

    try {
      if (payload.status === 'SUCCESS') {
        const downloadUrl = await this.fileConversionService.getConvertedFileUrl(payload.jobId);
        await this.llmModelsService.handleConvertedFile(
          payload,
          'SUCCESS',
          downloadUrl,
          payload.errorMessage,
        );
      } else {
        await this.llmModelsService.handleConvertedFile(
          payload,
          'FAILED',
          undefined,
          payload.errorMessage,
        );
      }
      return { status: 'success' };
    } catch (error) {
      this.logger.error(
        `Error handling file conversion callback for docId: ${payload.docId}`,
        error instanceof Error ? error.stack : String(error),
      );
      // Optionally, you might want to ensure the status is FAILED in the DB if the callback handler itself fails.
      await this.llmModelsService.handleConvertedFile(
        payload,
        'FAILED',
        undefined,
        'Callback handler failed to process the request.',
      );
      throw error;
    }
  }
}
