import { Environment, LlmEngineType, ResourceEntityType, ResourceSubsciberType, SystemName } from '@prisma/client';

export interface BotResourceCategorySeed {
  name: string;
  key: string;
  subscriberTypes: ResourceSubsciberType[];
  link?: 'LLM_ENGINE';
}
export interface BotResourceSeed {
  resourceKey: string;
  resourceName: string;
  subscriberTypes: ResourceSubsciberType[];
  description: string;
  resourceCategory: string;
  resourceEntityType?: ResourceEntityType;
  resourceEntityKey?: string;
  permissionKey?: string;
  quotaRuleKey?: string;
  link?:  'LLM_ENGINE' | 'CHAT_CHANNEL';
}

export interface BotResourcePlanSeed {
  planKey: string;
  planName: string;
  resourceKey: string;
  description: string;
  groupEnvs: Environment[];
  planRolesRequired?: SystemName[];
  isDefault: boolean;
  isDisabledPlan: boolean;
  quotaRuleKey?: string;
  quotaValue?: number;
  permissionKey?: string;
  link?: 'LLM_ENGINE'  | 'CHAT_CHANNEL';
  engineType?: LlmEngineType;
}

export const chatChannelType = ['API_KEY', 'PLAYGROUND', 'TEAMS', 'GEN_KB', 'OUTLOOK'].map(
  (channel) => ({
    name: channel
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' '),
    key: channel.toLowerCase().replace('_', '-'),
    slug: channel,
  }),
);

export const botResourceCategories: BotResourceCategorySeed[] = [
  {
    name: 'LLM Engine - {{platform}}',
    key: 'llm-engine-{{platform}}',
    subscriberTypes: [
      ResourceSubsciberType.BOT,
      ResourceSubsciberType.INSIGHT,
      ResourceSubsciberType.APP,
    ],
    link: 'LLM_ENGINE',
  },
  {
    name: 'Rate Limit',
    key: 'rate-limit',
    subscriberTypes: [ResourceSubsciberType.BOT, ResourceSubsciberType.APP],
  },
];

export const botResourcesList: BotResourceSeed[] = [
  {
    resourceKey: 'llm-engine-{{engine}}',
    resourceName: '{{engine}}',
    subscriberTypes: [
      ResourceSubsciberType.BOT,
      ResourceSubsciberType.INSIGHT,
      ResourceSubsciberType.APP,
    ],
    description: 'The quota of using {{engine}}',
    resourceCategory: 'llm-engine-{{platform}}',
    resourceEntityType: 'LLM_ENGINE',
    resourceEntityKey: '{{engine}}',
    permissionKey: 'group-{groupId}:llm-engine-{{engine}}',
    quotaRuleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    link: 'LLM_ENGINE',
  },
  {
    resourceKey: 'rate-limit-{{channel}}',
    resourceName: 'Rate limit - {{channel}}',
    quotaRuleKey: 'group-{groupId}:rate-limit-{{channel}}-quota',
    subscriberTypes: [ResourceSubsciberType.BOT, ResourceSubsciberType.APP],
    description: 'The rate limit setting of chatting through {{channel}}',
    resourceCategory: 'rate-limit',
    link: 'CHAT_CHANNEL',
  },
];

export const botResourcePlans: BotResourcePlanSeed[] = [
  {
    planKey: 'disable-llm-engine-{{engine}}',
    planName: 'Disable {{engine}}',
    resourceKey: 'llm-engine-{{engine}}',
    description: 'To disable {{engine}}',
    groupEnvs: Object.values(Environment),
    isDefault: false,
    isDisabledPlan: true,
    link: 'LLM_ENGINE',
  },
  {
    planKey: 'llm-engine-{{engine}}-plan-1',
    planName: '5M Token Max Per Month',
    resourceKey: 'llm-engine-{{engine}}',
    description: 'To allow maximum {{engine}} 5 millions token usage per month',
    quotaRuleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    quotaValue: 5000000,
    groupEnvs: Object.values(Environment),
    isDefault: false,
    isDisabledPlan: false,
    permissionKey: 'group-{groupId}:llm-engine-{{engine}}',
    link: 'LLM_ENGINE',
    engineType: LlmEngineType.TEXT,
  },
  {
    planKey: 'llm-engine-{{engine}}-plan-2',
    planName: '1M Token Max Per Month',
    resourceKey: 'llm-engine-{{engine}}',
    description: 'To allow maximum {{engine}} 1 million token usage per month',
    quotaRuleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    quotaValue: 1000000,
    groupEnvs: Object.values(Environment),
    isDefault: true,
    isDisabledPlan: false,
    permissionKey: 'group-{groupId}:llm-engine-{{engine}}',
    link: 'LLM_ENGINE',
    engineType: LlmEngineType.TEXT,
  },
  {
    planKey: 'llm-engine-{{engine}}-plan-1',
    planName: '3000 Images Max Per Month',
    resourceKey: 'llm-engine-{{engine}}',
    description: 'To allow maximum {{engine}} 3000 images usage per month',
    quotaRuleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    quotaValue: 3000,
    groupEnvs: Object.values(Environment),
    isDefault: false,
    isDisabledPlan: false,
    permissionKey: 'group-{groupId}:llm-engine-{{engine}}',
    link: 'LLM_ENGINE',
    engineType: LlmEngineType.IMAGE,
  },
  {
    planKey: 'llm-engine-{{engine}}-plan-2',
    planName: '1000 Images Max Per Month',
    resourceKey: 'llm-engine-{{engine}}',
    description: 'To allow maximum {{engine}} 1000 images usage per month',
    quotaRuleKey: 'group-{groupId}:llm-engine-{{engine}}-quota',
    quotaValue: 1000,
    groupEnvs: Object.values(Environment),
    isDefault: true,
    isDisabledPlan: false,
    permissionKey: 'group-{groupId}:llm-engine-{{engine}}',
    link: 'LLM_ENGINE',
    engineType: LlmEngineType.IMAGE,
  },
  {
    planKey: 'rate-limit-{{channel}}-plan-1',
    planName: '50 chats per hour',
    resourceKey: 'rate-limit-{{channel}}',
    description: 'To allow maximum 50 chats per hour by using {{channel}}',
    quotaRuleKey: 'group-{groupId}:rate-limit-{{channel}}-quota',
    quotaValue: 50,
    groupEnvs: Object.values(Environment),
    isDefault: true,
    isDisabledPlan: false,
    link: 'CHAT_CHANNEL',
  },
  {
    planKey: 'rate-limit-{{channel}}-plan-2',
    planName: '100 chats per hour',
    resourceKey: 'rate-limit-{{channel}}',
    description: 'To allow maximum 100 chats per hour by using {{channel}}',
    quotaRuleKey: 'group-{groupId}:rate-limit-{{channel}}-quota',
    quotaValue: 100,
    groupEnvs: Object.values(Environment),
    isDefault: false,
    isDisabledPlan: false,
    link: 'CHAT_CHANNEL',
  },
];
