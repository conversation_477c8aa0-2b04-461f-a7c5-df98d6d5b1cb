import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { SchedulerJobService } from './scheduler-job.service';
import { SchedulerJobController } from './scheduler-job.controller';
import { GroupsModule } from '../groups/groups.module';
import { BullMqModule } from 'src/providers/bullmq/bullmq.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';

@Module({
  imports: [PrismaModule, ConfigModule, GroupsModule, BullMqModule, FeatureFlagModule],
  controllers: [SchedulerJobController],
  providers: [SchedulerJobService],
  exports: [SchedulerJobService],
})
export class SchedulerJobModule {}
