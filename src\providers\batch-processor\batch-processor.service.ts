import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios'; // Added AxiosResponse
import { Readable } from 'stream'; // Added Readable
import { Configuration } from 'src/config/configuration.interface'; // Assuming this interface exists
import FormData from 'form-data';
import { PrismaService } from '../prisma/prisma.service';
import { AccessTokenParsed } from 'src/modules/auth/auth.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ListBatchProcessesDto } from 'src/modules/groups/dto/list-batch-processes.dto';
import { ListBatchFilesDto } from 'src/modules/groups/dto/list-batch-files.dto'; // Added

@Injectable()
export class BatchProcessorService {
  private readonly logger = new Logger(BatchProcessorService.name);
  private readonly client: AxiosInstance;
  private readonly batchProcessorBaseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    const batchProcessorConfig = this.configService.get<
      Configuration['batchProcessor'] // Assuming this config structure
    >('batchProcessor');

    if (!batchProcessorConfig || !batchProcessorConfig.baseUrl) {
      throw new Error('Batch Processor base URL is not configured');
    }

    this.batchProcessorBaseUrl = batchProcessorConfig.baseUrl;

    this.client = axios.create({ baseURL: this.batchProcessorBaseUrl });

    this.client.interceptors.request.use((req) => {
      this.logger.debug(
        `Sending request to Batch Processor: ${req.method?.toUpperCase()} ${req.url}`,
      );
      // this.logger.debug(`Request  ${JSON.stringify(req.data)}`);
      return req;
    });

    this.client.interceptors.response.use(
      (res) => {
        this.logger.debug(
          `Received response from Batch Processor: ${res.status} ${res.config.url}`,
        );
        return res;
      },
      (error: AxiosError) => {
        this.logger.error(
          `Error response from Batch Processor: ${error.response?.status} ${error.config?.url}`,
          error.stack,
        );
        // Re-throw or handle specific errors
        return Promise.reject(error);
      },
    );
  }

  // --- Methods for Batch Processor API Interaction ---

  /**
   * Uploads a batch file.
   * @param groupId - The ID of the group.
   * @param file - The file object (e.g., from Multer).
   * @returns Promise<AxiosResponse<unknown>> - Axios response from Batch Processor.
   */
  async uploadBatchFile(
    groupId: number,
    file: Express.Multer.File,
  ): Promise<AxiosResponse<unknown>> {
    const url = `/groups/${groupId}/batch-files`; // Adjust endpoint as needed
    const formData = new FormData();
    formData.append('file', file.buffer, file.originalname);
    // Add other form data fields if required by the Batch Processor API
    this.logger.log(`Uploading batch file for group ${groupId}...`);
    try {
      const response = await this.client.post<unknown>(url, formData, {
        headers: formData.getHeaders(),
      });
      return response;
    } catch (error) {
      this.logger.error(`Failed to upload batch file for group ${groupId}`, error);
      throw this.extractBatchProcessorError(error as AxiosError);
    }
  }

  /**
   * Downloads a batch file content.
   * @param groupId - The ID of the group.
   * @param batchFileId - The ID of the batch file.
   * @returns Promise<AxiosResponse<Readable>> - Axios response containing the file stream.
   */
  async downloadBatchFile(groupId: number, batchFileId: number): Promise<AxiosResponse<Readable>> {
    const url = `/groups/${groupId}/batch-files/${batchFileId}/contents`; // Adjust endpoint
    this.logger.log(`Downloading batch file ${batchFileId} for group ${groupId}...`);
    const response = await this.client.get<Readable>(url, { responseType: 'stream' });
    return response;
  }

  /**
   * Deletes a batch file.
   * @param groupId - The ID of the group.
   * @param batchFileId - The ID of the batch file.
   * @returns Promise<void>
   */
  async deleteBatchFile(groupId: number, batchFileId: number): Promise<void> {
    const url = `/groups/${groupId}/batch-files/${batchFileId}`; // Adjust endpoint
    this.logger.log(`Deleting batch file ${batchFileId} for group ${groupId}...`);
    await this.client.delete<void>(url);
  }

  /**
   * Lists batch files for a group with pagination and filtering.
   * @param groupId - The ID of the group.
   * @param queryParams - DTO containing pagination and sorting options.
   * @returns Promise<AxiosResponse<unknown>> - Axios response from Batch Processor.
   */
  async listBatchFiles(
    groupId: number,
    queryParams: ListBatchFilesDto,
  ): Promise<AxiosResponse<unknown>> {
    const url = `/groups/${groupId}/batch-files`;
    this.logger.log(
      `Listing batch files for group ${groupId} with params: ${JSON.stringify(queryParams)}`,
    );
    const response = await this.client.get<unknown>(url, {
      params: queryParams,
    });
    return response;
  }

  /**
   * Gets metadata for a specific batch file.
   * @param groupId - The ID of the group.
   * @param batchFileId - The ID of the batch file.
   * @returns Promise<AxiosResponse<unknown>> - Axios response from Batch Processor.
   */
  async getBatchFileMetadata(
    groupId: number,
    batchFileId: number,
  ): Promise<AxiosResponse<unknown>> {
    // Note: The downloadBatchFile method already exists and fetches content.
    // This assumes the Batch Processor has a separate endpoint for metadata only.
    // If not, the existing downloadBatchFile might need adjustment or this method might call that endpoint
    // and extract metadata, or the Batch Processor needs a new endpoint.
    // Assuming a new endpoint `/groups/{groupId}/batch-files/{batchFileId}/metadata` exists for now.
    const url = `/groups/${groupId}/batch-files/${batchFileId}`;
    this.logger.log(`Getting metadata for batch file ${batchFileId} in group ${groupId}...`);
    const response = await this.client.get<unknown>(url);
    return response;
  }

  /**
   * Creates a new batch process.
   * @param groupId - The ID of the group.
   * @param batchFileId - The ID of the input batch file.
   * @param provider - The provider for the batch process (e.g., 'AZURE_OPENAI').
   * @returns Promise<AxiosResponse<unknown>> - Axios response from Batch Processor.
   */
  async createBatchProcess(
    groupId: number,
    batchFileId: number,
    provider: string,
    endpoint: string,
    completionWindow: string,
    user: AccessTokenParsed,
    model?: string,
  ): Promise<AxiosResponse<unknown>> {
    const url = `/groups/${groupId}/batch-processes`;
    const groupInfo = await this.prisma.group.findUnique({ where: { id: groupId } });
    if (groupInfo === null) throw new ApiException(ErrorCode.GROUP_NOT_FOUND, { groupId });
    const data = {
      batchFileId,
      groupInfo,
      provider,
      endpoint,
      completionWindow,
      model,
      createdBy: user?.id ?? 1,
    };
    this.logger.log(`Creating batch process for group ${groupId} using file ${batchFileId}...`);
    try {
      const response = await this.client.post<unknown>(url, data);
      return response;
    } catch (error) {
      this.logger.error(`Error creating batch process for group ${groupId}: ${error}`);
      throw this.extractBatchProcessorError(error as AxiosError);
    }
  }

  /**
   * Gets the status and details of a batch process.
   * @param groupId - The ID of the group.
   * @param batchProcessId - The ID of the batch process.
   * @returns Promise<AxiosResponse<unknown>> - Axios response from Batch Processor.
   */
  async getBatchProcess(groupId: number, batchProcessId: number): Promise<AxiosResponse<unknown>> {
    const url = `/groups/${groupId}/batch-processes/${batchProcessId}`; // Adjust endpoint
    this.logger.log(`Getting status for batch process ${batchProcessId} in group ${groupId}...`);
    const response = await this.client.get<unknown>(url);
    return response;
  }

  /**
   * Downloads the output file of a completed batch process.
   * @param groupId - The ID of the group.
   * @param batchProcessId - The ID of the batch process.
   * @returns Promise<AxiosResponse<Readable>> - Axios response containing the output file stream.
   */
  async downloadBatchProcessOutput(
    groupId: number,
    batchProcessId: number,
  ): Promise<AxiosResponse<Readable>> {
    const url = `/groups/${groupId}/batch-processes/${batchProcessId}/output.jsonl`; // Adjust endpoint
    this.logger.log(
      `Downloading output for batch process ${batchProcessId} in group ${groupId}...`,
    );
    const response = await this.client.get<Readable>(url, { responseType: 'stream' });
    return response;
  }

  /**
   * Downloads the error file of a completed/failed batch process.
   * @param groupId - The ID of the group.
   * @param batchProcessId - The ID of the batch process.
   * @returns Promise<AxiosResponse<Readable>> - Axios response containing the error file stream.
   */
  async downloadBatchProcessError(
    groupId: number,
    batchProcessId: number,
  ): Promise<AxiosResponse<Readable>> {
    const url = `/groups/${groupId}/batch-processes/${batchProcessId}/error.jsonl`; // Adjust endpoint
    this.logger.log(
      `Downloading error file for batch process ${batchProcessId} in group ${groupId}...`,
    );
    const response = await this.client.get<Readable>(url, { responseType: 'stream' });
    return response;
  }

  /**
   * Cancels a batch process.
   * @param groupId - The ID of the group.
   * @param batchProcessId - The ID of the batch process.
   * @returns Promise<AxiosResponse<Readable>>
   */
  async cancelBatchProcess(groupId: number, batchProcessId: number): Promise<unknown> {
    const url = `/groups/${groupId}/batch-processes/${batchProcessId}`; // Adjust endpoint
    this.logger.log(`Cancelling batch process ${batchProcessId} in group ${groupId}...`);
    const response = await this.client.delete(url);
    return response;
  }

  /**
   * Lists batch processes for a group with pagination and filtering.
   * @param groupId - The ID of the group.
   * @param queryParams - DTO containing pagination, sorting, and filtering options.
   * @returns Promise<AxiosResponse<unknown>> - Axios response from Batch Processor.
   */
  async listBatchProcesses(
    groupId: number,
    queryParams: ListBatchProcessesDto,
  ): Promise<AxiosResponse<unknown>> {
    const url = `/groups/${groupId}/batch-processes`;
    this.logger.log(
      `Listing batch processes for group ${groupId} with params: ${JSON.stringify(queryParams)}`,
    );
    // Ensure skip and take are handled correctly if needed by the downstream service
    // If the downstream service expects 'page' and 'limit', a transformation might be needed here.
    // Assuming downstream service accepts skip, take, sortBy, sortOrder, status directly.
    const response = await this.client.get<unknown>(url, {
      params: queryParams, // Pass DTO as query parameters
    });
    return response;
  }

  extractBatchProcessorError(error: AxiosError): ApiException {
    if (error.response && error.response.data) {
      const { code, message, extra } = (error.response.data as any).error ;
      return new ApiException(`${code}: ${message}`, extra);
    }
    throw error;
  }
}
