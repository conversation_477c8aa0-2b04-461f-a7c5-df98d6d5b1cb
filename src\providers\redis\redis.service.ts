import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import { Configuration } from '../../config/configuration.interface';
import { FEATURE_FLAG_TYPE, MODEL_FEATURE_FLAG, PROJECT_NAME } from './redis.constants';

@Injectable()
export class RedisService {
  client: Redis;
  
  private logger = new Logger(RedisService.name);

  constructor(private configService: ConfigService) {
    const config = this.configService.get<Configuration['redis']>('redis');

    this.client = this.createRedisClient(config.url, true, 'Redis client');

  }

  private createRedisClient(url: string, clearCache: boolean, type: string): Redis {
    const client = new Redis(url, {
      enableOfflineQueue: false,
      enableReadyCheck: true,
      retryStrategy(times) {
        const delay = times <= 40 ? times * 50 : 2000;
        return delay;
      },
    });

    client
      .on('error', (err) => this.logger.error(err, `[RedisService][init] ${type} connect error.`))
      .on('ready', () => {
        this.logger.log(`[RedisService][init] ${type} connected`);
        if (clearCache) {
          const keyPattern = `${PROJECT_NAME}:${MODEL_FEATURE_FLAG}:${FEATURE_FLAG_TYPE.SYSTEM}:*`;
          this.batchClearCache(keyPattern).catch((error: Error) => {
            this.logger.error(`batchClearCache Error ${error.message}`, error.stack);
          });
        }
      });

    return client;
  }

  async getOrSet<T>(key: string, valueFunction: () => Promise<unknown>, ttl?: number): Promise<T> {
    let valueStored;
    try {
      valueStored = await this.client.get(key);
    } catch (err) {
      // failed to connect to redis, return the valueFunction immediately without set to redis
      // ** note that if the valueFunction is very heavy and high loading, the redis server down could cause fatal impact to the backend server in this case
      // value will be underfined
      this.logger.error(err, `[RedisService][getOrSet] Failed to get key '${key}'.`);
    }

    if (valueStored !== undefined && valueStored !== null) {
      return JSON.parse(valueStored);
    }

    const value = (await valueFunction()) as T;
    if (value === undefined) return value;

    try {
      if (ttl) {
        await this.client.set(key, JSON.stringify(value), 'EX', ttl);
      } else {
        await this.client.set(key, JSON.stringify(value));
      }
    } catch (err) {
      this.logger.error(err, `[RedisService][getOrSet] Failed to set key '${key}'.`);
    }

    return value;
  }

  async clearCache(key: string): Promise<number> {
    try {
      return await this.client.del(key);
    } catch (err) {
      this.logger.error(err, `[RedisService][clearCache] Failed to del key '${key}'`);
      return 0;
    }
  }

  async batchClearCache(keyPattern: string): Promise<number> {
    try {
      const keys = await this.client.keys(keyPattern);
      if (keys.length < 1) return 0;
      return await this.client.del(keys);
    } catch (err) {
      this.logger.error(
        err,
        `[RedisService][batchClearCache] Failed to get or del keys for pattern '${keyPattern}'`,
      );
      return 0;
    }
  }

  async createCache(key: string, value: unknown, ttl?: number): Promise<string> {
    const valueToStore = JSON.stringify(value);
    try {
      const isKeyExist = await this.client.exists(key);
      if (!isKeyExist) {
        if (ttl) {
          await this.client.set(key, valueToStore, 'EX', ttl);
        } else {
          await this.client.set(key, valueToStore);
        }
      } else {
        this.logger.error(`[RedisService][createCache] Key ${key} already exists`);
      }
    } catch (err) {
      this.logger.error(err, `[RedisService][createCache] Failed to set key '${key}'`);
    }
    return valueToStore;
  }

  async getCache<T>(key: string): Promise<T | undefined> {
    let valueStored;
    try {
      valueStored = await this.client.get(key);
    } catch (err) {
      this.logger.error(err, `[RedisService][getCache] Failed to get key '${key}'`);
      return undefined;
    }

    return typeof valueStored === 'string' ? JSON.parse(valueStored) : valueStored;
  }

}
