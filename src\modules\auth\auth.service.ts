import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import type { Prisma } from '@prisma/client';
import {
  Email,
  LoginType,
  ResourceSubsciberType,
  User,
  VerificationStatus,
  VerificationType,
} from '@prisma/client';

import { compare, hash } from 'bcryptjs';
import { createHash } from 'crypto';
import { authenticator } from 'otplib';
import { createRandomBytes, createDigest } from '@otplib/plugin-crypto';
import { keyEncoder, keyDecoder } from '@otplib/plugin-thirty-two';
import randomColor from 'randomcolor';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { safeEmail } from '../../helpers/safe-email';
import { GeolocationService } from '../../providers/geolocation/geolocation.service';
import { MailService } from '../../providers/mail/mail.service';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { PwnedService } from '../../providers/pwned/pwned.service';
import {
  EMAIL_VERIFY_TOKEN,
  LOGIN_ACCESS_TOKEN,
  LOGIN_REFRESH_TOKEN,
  MERGE_ACCOUNTS_TOKEN,
} from '../../providers/tokens/tokens.constants';
import { TokensService } from '../../providers/tokens/tokens.service';
import { ApprovedSubnetsService } from '../approved-subnets/approved-subnets.service';
import {
  AccessTokenClaims,
  RefreshTokenClaims,
  TokenResponse,
  OutLookResponse,
} from './auth.interface';
import { SsoService } from '../../providers/sso/sso.service';
import { KeycloakExchangeCodeDto, RegisterDto, ResetPasswordDto } from './auth.dto';
import { AzureSSOService } from '../../providers/sso/azure-sso.service';
import { ScopeService } from '../scope/scope.service';
import { SignOptions } from 'jsonwebtoken';
import { WikijsService } from '../wikijs/wikijs.service';
import { addDays } from 'date-fns';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import { RedisService } from 'src/providers/redis/redis.service';
import UAParser from 'ua-parser-js';
import {
  GROUP_MEMBERSHIP_SCOPE_KEY,
  GROUP_RESOURCE_SCOPE_KEY,
  USER_SCOPE_KEY,
} from 'src/providers/redis/redis.constants';
import { LastUsedGroupsService } from '../../providers/last-used-groups/last-used-groups.service';
import { KeycloakService, KeycloakTokenResponse } from 'src/providers/keycloak/keycloak.service';
import { Response } from 'express';

@Injectable()
export class AuthService {
  authenticator: typeof authenticator;
  logger = new Logger(AuthService.name);

  constructor(
    private redis: RedisService,
    private prisma: PrismaService,
    private email: MailService,
    private configService: ConfigService,
    private pwnedService: PwnedService,
    private tokensService: TokensService,
    private geolocationService: GeolocationService,
    private approvedSubnetsService: ApprovedSubnetsService,
    private ssoService: SsoService,
    private scopeService: ScopeService,
    private wikijsService: WikijsService,
    private azureSSOService: AzureSSOService,
    private featureFlagService: FeatureFlagService,
    private lastUsedGroupsService: LastUsedGroupsService,
    private keycloakService: KeycloakService,
  ) {
    this.authenticator = authenticator.create({
      window: [
        this.configService.get<number>('security.totpWindowPast') ?? 0,
        this.configService.get<number>('security.totpWindowFuture') ?? 0,
      ],
      keyEncoder,
      keyDecoder,
      createDigest,
      createRandomBytes,
    });
  }

  async loginWithKeycloak(
    dto: KeycloakExchangeCodeDto,
    // ipAddress and userAgent are not used if we only return Keycloak's response
    // ipAddress: string,
    // userAgent: string,
  ): Promise<KeycloakTokenResponse> {
    this.logger.log(
      `Attempting Keycloak token exchange with code, redirect URI: ${dto.redirect_uri}`, // Updated to use dto.redirect_uri
    );
    // Directly return the response from KeycloakService, passing all required DTO fields
    return this.keycloakService.getAccessTokenFromCode(
      dto.code,
      dto.grant_type,
      dto.client_id,
      dto.redirect_uri,
      dto.code_verifier,
    );
  }

  async login(
    ipAddress: string,
    userAgent: string,
    email: string,
    loginType: LoginType,
    password?: string,
    hktToken?: string,
    azureToken?: string,
    refreshToken?: string,
  ): Promise<TokenResponse> {
    if (hktToken && loginType === LoginType.HKT) {
      return this.loginUserWithHktToken(ipAddress, userAgent, hktToken, email, refreshToken);
    } else if (email) {
      this.logger.log(`user login email: ${email}`);
      const user = await this.prisma.user.findFirst({
        where: {
          emails: {
            some: {
              email: { equals: email, mode: 'insensitive' },
              emailSafe: { equals: email, mode: 'insensitive' },
            },
          },
        },
        include: {
          emails: true,
          prefersEmail: true,
        },
      });
      if (!user) {
        this.logger.error(`user login email: ${email} ->> user not found`);
        throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
      }

      if (!user.active) {
        this.logger.error(`user login email: ${email} ->> user not active`);
        throw new ApiException(ErrorCode.INACTIVE_USER);
      }

      if (loginType === LoginType.LOCAL) {
        this.logger.log(
          `user login id: ${user.id}, email: ${email} , staff id: ${user.staffId} by local`,
        );
        return this.loginUserWithPassword(ipAddress, userAgent, user, password);
      }

      if (loginType === LoginType.Azure) {
        this.logger.log(
          `user login id: ${user.id} , , email: ${email} , staff id: ${user.staffId} by azure`,
        );
        return this.loginUserWithAzureAD(ipAddress, userAgent, user, azureToken);
      }
    }

    return null;
  }

  async loginByTeamsBot(
    ipAddress: string,
    userAgent: string,
    email: string,
  ): Promise<TokenResponse> {
    this.logger.log(
      `user loginByTeamsBot email: ${email} idAddress: ${ipAddress}: userAgent: ${userAgent}`,
    );
    const user = await this.prisma.user.findFirst({
      where: { emails: { some: { email, emailSafe: email } } },
      include: {
        emails: true,
        prefersEmail: true,
      },
    });

    if (!user) {
      this.logger.error(`user login email: ${email} ->> user not found`);
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }

    if (!user.active) {
      this.logger.error(`user login email: ${email} ->> user not active`);
      throw new ApiException(ErrorCode.INACTIVE_USER);
    }

    if (!user.emails.find((i) => i.email === email)?.isVerified) {
      this.logger.error(`user login email: ${email} ->> user email is not verified`);
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }

    this.logger.log(`user loginByTeamsBot email: ${email}`);
    return this.loginResponse(ipAddress, userAgent, user);
  }

  //use Azure AD sso token login
  async loginUserWithAzureAD(
    ipAddress: string,
    userAgent: string,
    user: User & {
      emails: Email[];
      prefersEmail: Email;
    },
    azureToken: string,
  ) {
    if (!azureToken) {
      throw new ApiException(ErrorCode.INVAILD_SSO_TOKEN);
    } else {
      this.logger.log('login by azure token');
    }

    const tokenInfo = await this.azureSSOService.getUserInfoFormToken(azureToken);

    if (
      tokenInfo.error ||
      !user.emails.map((email) => email.emailSafe).includes(tokenInfo.mail.toLowerCase())
    ) {
      throw new ApiException(ErrorCode.INVAILD_SSO_TOKEN);
    }

    return this.loginResponse(ipAddress, userAgent, user);
  }

  async loginUserWithPassword(ipAddress: string, userAgent: string, user: User, password: string) {
    if (
      !(await this.featureFlagService.getOne(FeatureFlagKey.ENABLE_USERNAME_PASSWORD_LOGIN))
        ?.isEnabled
    ) {
      this.logger.error(`user login id: ${user.id} ->> user is not allowed to use local login`);
      throw new ApiException(ErrorCode.INVALID_LOGIN_TYPE);
    }

    if (password.length < 8) {
      throw new ApiException(ErrorCode.INVALID_PASSWORD_LENGTH);
    }
    const isMatch = await compare(password, user.password);
    if (!isMatch) {
      this.logger.error(`user local login name: ${user.name} ->> invalid password`);
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }
    return this.loginResponse(ipAddress, userAgent, user);
  }

  async register(ipAddress: string, _data: RegisterDto, setVerified = true): Promise<Expose<User>> {
    const { email, origin, ...data } = _data;
    const emailSafe = safeEmail(email);
    const testUser = await this.prisma.user.findFirst({
      where: { emails: { some: { emailSafe } } },
    });
    if (testUser) throw new ApiException(ErrorCode.EMAIL_USER_CONFLICT);
    const ignorePwnedPassword = !!data.ignorePwnedPassword;
    delete data.ignorePwnedPassword;

    if (data.name)
      data.name = data.name
        .split(' ')
        .map((word, index) =>
          index === 0 || index === data.name.split(' ').length
            ? (word.charAt(0) ?? '').toUpperCase() + (word.slice(1) ?? '').toLowerCase()
            : word,
        )
        .join(' ');
    if (data.password)
      data.password = await this.hashAndValidatePassword(data.password, ignorePwnedPassword);

    if (!data.gender) {
      data.gender = 'UNKNOWN';
      // try {
      //   const prediction = await axios.get<{
      //     name: string;
      //     gender: 'male' | 'female';
      //     probability: number;
      //     count: number;
      //   }>(`https://api.genderize.io/?name=${data.name.split(' ')[0]}`);
      //   if (
      //     prediction.data.probability > 0.5 &&
      //     prediction.data.gender === 'male'
      //   )
      //     data.gender = 'MALE';
      //   if (
      //     prediction.data.probability > 0.5 &&
      //     prediction.data.gender === 'female'
      //   )
      //     data.gender = 'FEMALE';
      // } catch (error) {
      //   this.logger.log(error);
      //   throw error;
      // }
    }

    let id: number | undefined = undefined;
    while (!id) {
      id = Number(`10${await this.tokensService.generateRandomString(6, 'numeric')}`);
      const users = await this.prisma.user.findMany({ where: { id }, take: 1 });
      if (users.length) id = undefined;
    }

    try {
      const user = await this.prisma.user.create({
        data: {
          ...data,
          // id,
          roleId: 0,
          createdAt: new Date(),
          emails: {
            create: {
              email,
              emailSafe,
            },
          },
          // emails: {
          //   create: { email: email, emailSafe },
          // },
        },
        include: { emails: { select: { id: true, emailSafe: true } } },
      });
      if (user.emails[0]?.id) {
        await this.prisma.user.update({
          where: { id: user.id },
          data: { prefersEmail: { connect: { id: user.emails[0].id } } },
        });

        // HKT(or null) no need verification, so create wikijs user here
        if (user.loginType === LoginType.HKT || user.loginType == null) {
          await this.wikijsService.handleCreateBotBuilderUser(
            user.id,
            user.name,
            user.loginType,
            user.emails[0].emailSafe,
            null,
            user.ccc,
            user.staffId,
          );
        }
      }
      // In testing, we auto-approve the email
      if (setVerified || process.env['TEST']) {
        const emailId = user.emails[0]?.id;
        if (emailId)
          await this.prisma.email.update({
            where: { id: emailId },
            data: { isVerified: true },
          });
      } else await this.sendEmailVerification(email, false, origin);

      await this.approvedSubnetsService.approveNewSubnet(user.id, ipAddress);

      return this.prisma.expose(user);
    } catch (error) {
      this.logger.error(error, 'Error in user registration');
    }
  }

  async sendEmailVerification(email: string, resend = false, origin?: string) {
    const emailSafe = safeEmail(email);
    const emailDetails = await this.prisma.email.findFirst({
      where: { emailSafe },
      include: { user: true },
    });
    if (!emailDetails) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    if (emailDetails.isVerified) throw new ApiException(ErrorCode.EMAIL_VERIFIED_CONFLICT);
    await this.email.send({
      to: `"${emailDetails.user.name}" <${email}>`,
      template: resend ? 'auth/resend-email-verification' : 'auth/email-verification',
      data: {
        name: emailDetails.user.name,
        days: 7,
        link: `${
          origin ?? this.configService.get<string>('frontendUrl')
        }/auth/link/verify-email?token=${this.tokensService.signJwt(
          EMAIL_VERIFY_TOKEN,
          { id: emailDetails.id },
          '7h',
        )}`,
      },
    });
    return { queued: true };
  }

  async refresh(refreshToken: string, groupId?: number): Promise<TokenResponse> {
    if (!refreshToken) throw new ApiException(ErrorCode.NO_TOKEN_PROVIDED);

    try {
      const { id: userId } = this.tokensService.verify<RefreshTokenClaims>(
        LOGIN_REFRESH_TOKEN,
        refreshToken,
      );
      const session = await this.prisma.session.findFirst({
        where: {
          token: refreshToken,
        },
      });
      if (!session) {
        this.logger.error(`Session not found - ${groupId}`);
        throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
      }
      const user = await this.prisma.user.findUnique({ where: { id: userId } });
      const { token: newAccessToken, scopes } = await this.generateAccessToken(user, groupId);
      let appCenterRefreshToken;
      const appsCrossSsoLogin = (
        await this.featureFlagService.getOne(FeatureFlagKey.AUTH_ENABLE_CROSS_APPS_SSO_LOGIN)
      )?.isEnabled;
      if (session.appCenterRefreshToken && session.loginType == 'HKT' && appsCrossSsoLogin) {
        const token = await this.keycloakService.refreshToken(session.appCenterRefreshToken);
        appCenterRefreshToken = token?.refresh_token;
      }
      const newRefreshToken = await this.generateRefreshToken(userId);
      await this.prisma.session.update({
        where: { id: session.id },
        data: {
          token: newRefreshToken,
          appCenterRefreshToken: appCenterRefreshToken,
          updatedAt: new Date(),
        },
      });
      if (groupId) {
        await this.addLastUsedGroup(userId, groupId);
      }
      return {
        scopes,
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      };
    } catch (NotFoundError) {
      this.logger.error(NotFoundError, `failed to refresh token - ${groupId}`);
      throw new ApiException(ErrorCode.INVALID_TOKEN);
    }
  }

  async logout(refreshToken: string, response: Response): Promise<void> {
    // validate the refresh token
    if (!refreshToken) throw new ApiException(ErrorCode.NO_TOKEN_PROVIDED);
    const { id: userId } = this.tokensService.verify<RefreshTokenClaims>(
      LOGIN_REFRESH_TOKEN,
      refreshToken,
    );
    const session = await this.prisma.session.findFirst({
      where: {
        token: refreshToken,
      },
    });
    if (!session) {
      throw new ApiException(ErrorCode.SESSION_NOT_FOUND);
    }
    // clean the session and the related scope
    await this.prisma.session.delete({ where: { id: session.id } });
    await this.redis.batchClearCache(
      USER_SCOPE_KEY.replace('{USER_ID}', userId.toString()).replace('{GROUP_ID}', '*'),
    );
    await this.redis.batchClearCache(
      GROUP_MEMBERSHIP_SCOPE_KEY.replace('{GROUP_ID}', '*').replace('{USER_ID}', userId.toString()),
    );
    const needRemoveTokenName = await this.prisma.app.findFirst({
      select: { tokenCookiesName: true },
    });
    response.clearCookie(needRemoveTokenName.tokenCookiesName, {
      httpOnly: true,
      secure: true,
      sameSite: 'none',
      path: '/',
      domain: this.configService.get('frontendDomain'),
      maxAge: 3600000,
    });
  }

  async forgetPassword(email: string) {
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          emails: { some: { email } },
        },
        include: {
          emails: true,
        },
      });
      if (user) {
        const verificationCode = await this.tokensService.generateRandomString(8, 'numeric');
        const expiryDay = this.configService.get<number>('kYCVerification.expiryDay');
        const frontendUrl = this.configService.get<string>('frontendUrl');
        await this.email.send({
          to: `"${user.name}" <${user.emails[0].email}>`,
          template: 'auth/password-reset',
          data: {
            name: user.name,
            link: `${frontendUrl}/auth/reset-password/${verificationCode}`,
            days: expiryDay,
          },
        });
        await this.prisma.kYCVerification.upsert({
          where: {
            userId_type: {
              userId: user.id,
              type: VerificationType.RESET_PASSWORD,
            },
          },
          create: {
            code: verificationCode,
            expiredAt: addDays(new Date(), expiryDay),
            type: VerificationType.RESET_PASSWORD,
            status: VerificationStatus.PENDING,
            userId: user.id,
          },
          update: {
            code: verificationCode,
            expiredAt: addDays(new Date(), expiryDay),
            status: VerificationStatus.PENDING,
          },
        });
      }
    } catch (err) {
      this.logger.error(err, 'Failed to forget password');
      throw new ApiException(ErrorCode.FAIL_TO_FORGET_PASSWORD);
    }
  }

  async resetPassword(data: ResetPasswordDto) {
    if (data.password !== data.confirmedPassword) {
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }
    if (data.password.length < 8) {
      throw new ApiException(ErrorCode.INVALID_PASSWORD_LENGTH);
    }
    const hashedPassword = await this.hashAndValidatePassword(data.password, true);
    const kYCVerification = await this.prisma.$transaction(async (tx) => {
      const res = await tx.kYCVerification.update({
        data: {
          verifiedAt: new Date(),
          status: VerificationStatus.VERIFY_SUCCESS,
        },
        where: {
          code: data.token,
        },
      });

      const currentUser = await this.prisma.user.findUnique({
        where: { id: res.userId },
      });

      await tx.user.update({
        data: {
          password: hashedPassword,
        },
        where: { id: res.userId },
      });

      await this.wikijsService.syncWikiJsUserPassword(currentUser.wikijsId, data.password);

      return res;
    });
    return kYCVerification;
  }

  async loginUserWithHktToken(
    ipAddress: string,
    userAgent: string,
    hktToken: string,
    email: string,
    refreshToken: string,
  ): Promise<TokenResponse> {
    if (!hktToken) {
      this.logger.error(`user hkt login failed ->> invalid hkt sso token`);
      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }
    if (!(await this.featureFlagService.getOne(FeatureFlagKey.ENABLE_HKT_SSO_LOGIN))?.isEnabled) {
      this.logger.error(` user is not allowed to use hkt login`);
      throw new ApiException(ErrorCode.INVALID_LOGIN_TYPE);
    }
    const appCenterSsoLogin = (
      await this.featureFlagService.getOne(FeatureFlagKey.AUTH_ENABLE_CROSS_APPS_SSO_LOGIN)
    )?.isEnabled;
    const refreshTokenRecord = appCenterSsoLogin ? refreshToken : undefined;
    let tokenInfo;
    if (appCenterSsoLogin) {
      tokenInfo = await this.keycloakService.extractUserFromToken(hktToken);
    } else {
      tokenInfo = await this.ssoService.extractUserFromToken(hktToken);
    }
    if (tokenInfo.error == 'invalid_token') {
      this.logger.error(`user hkt login failed ->> invalid hkt sso token`);

      throw new ApiException(ErrorCode.INVALID_CREDENTIALS);
    }
    let user = null;
    // find user by hkt staff id
    this.logger.log(`HKT Login - find user by staff id - ${tokenInfo.preferred_username}`);
    user = await this.prisma.user.findFirst({
      where: {
        loginProviderUniqueKey: {
          equals: tokenInfo.preferred_username,
          mode: 'insensitive',
        },
        loginType: 'HKT',
      },
      include: {
        emails: true,
        prefersEmail: true,
      },
    });
    // if user found by staff id and email not matched token info email , update the token email to found user
    if (user && user?.emails && !user.emails.includes(tokenInfo.email)) {
      this.logger.log(`HKT Login - update account  by email - ${tokenInfo.email}`);
      await this.prisma.email.updateMany({
        where: { userId: user.id },
        data: { email: tokenInfo.email, emailSafe: tokenInfo.email },
      });
    }

    // if failed to find user by staff id, find by email and update back the staff id
    if (!user && email) {
      this.logger.log(`HKT Login - find user by user email - ${email}`);
      user = await this.prisma.user.findFirst({
        where: { emails: { some: { email, emailSafe: email } } },
        include: {
          emails: true,
          prefersEmail: true,
        },
      });
      this.logger.log(
        `HKT Login - update login provider unique key  by staff id - ${tokenInfo.preferred_username}`,
      );
      await this.prisma.user.update({
        data: {
          staffId: tokenInfo.preferred_username.toUpperCase(),
          loginProviderUniqueKey: tokenInfo.preferred_username.toUpperCase(),
        },
        where: {
          id: user.id,
        },
      });
    }

    if (!user) {
      this.logger.error(
        `user is logging by hkt account , staff id : ${tokenInfo.preferred_username} ->> user not found on system`,
      );
      throw new ApiException(
        ErrorCode.INVALID_LOGIN_STAFF_ID.replace('{staffId}', tokenInfo.preferred_username),
      );
    }
    if (!user.active) {
      this.logger.error(`user login staff id  by hkt- ${user.staffId} ->> user not active`);
      throw new ApiException(ErrorCode.INACTIVE_USER);
    }

    return this.loginResponse(ipAddress, userAgent, user, refreshTokenRecord);
  }

  private async generateAccessToken(
    user: User,
    groupId?: number,
  ): Promise<{ scopes: string[]; token: string }> {
    const accessTokenExpiry = this.configService.get<string>('security.accessTokenExpiry');
    const role = await this.prisma.role.findUnique({ where: { id: user.roleId } });

    const payload: AccessTokenClaims = {
      id: user.id,
      groupId,
      role: role.systemName,
    };
    const signOptions: SignOptions = {
      algorithm: 'RS256',
      issuer: 'bot-builder',
    };

    const token = this.tokensService.signJwt(
      LOGIN_ACCESS_TOKEN,
      payload,
      accessTokenExpiry,
      signOptions,
    );
    // save the scopes into cache
    const scopes = await this.getScopes(user.id, groupId);

    return { scopes, token };
  }

  private async generateRefreshToken(userId: number, groupId?: number): Promise<string> {
    const payload: RefreshTokenClaims = {
      id: userId,
      groupId,
    };

    const signOptions: SignOptions = {
      algorithm: 'RS256',
      issuer: 'bot-builder',
    };
    const loginTokenExpiryTime = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      undefined,
      FeatureFlagKey.LOGIN_TOKEN_EXPIRY_TIME,
    );
    const expiryTime =
      (loginTokenExpiryTime?.metaData?.['refreshToken'] as number) ??
      this.configService.get<string>('security.refreshTokenExpiry');
    // create token
    const token = this.tokensService.signJwt(LOGIN_REFRESH_TOKEN, payload, expiryTime, signOptions);

    return token;
  }

  private async loginResponse(
    ipAddress: string,
    userAgent: string,
    user: User,
    appCenterRefreshToken?: string,
  ): Promise<TokenResponse> {
    const ua = new UAParser(userAgent);
    let location;
    if (ipAddress === '::1') {
      location = null;
    } else {
      location = await this.geolocationService.getLocation(ipAddress);
    }
    const refreshToken = await this.generateRefreshToken(user.id, 0);
    const { token: accessToken, scopes } = await this.generateAccessToken(user);
    // update last login date of the user
    await this.prisma.user.update({ where: { id: user.id }, data: { lastLoginDate: new Date() } });
    await this.prisma.session.create({
      data: {
        token: refreshToken,
        ipAddress,
        city: location?.city?.names?.en,
        region: location?.subdivisions?.pop()?.names?.en,
        timezone: location?.location?.timeZone,
        countryCode: location?.country?.isoCode,
        userAgent,
        browser:
          `${ua.getBrowser().name ?? ''} ${ua.getBrowser().version ?? ''}`.trim() || undefined,
        operatingSystem:
          `${ua.getOS().name ?? ''} ${ua.getOS().version ?? ''}`
            .replace('Mac OS', 'macOS')
            .trim() || undefined,
        user: { connect: { id: user.id } },
        appCenterRefreshToken,
        updatedAt: new Date(),
      },
    });
    this.logger.log(`user ${user.name} , id: ${user.id} login successfully`);
    return {
      scopes,
      accessToken,
      refreshToken,
    };
  }

  // private async mfaResponse(
  //   user: User & {
  //     prefersEmail: Email;
  //   },
  //   forceMethod?: MfaMethod,
  // ): Promise<TotpTokenResponse> {
  //   const mfaTokenPayload: MfaTokenPayload = {
  //     type: user.twoFactorMethod,
  //     id: user.id,
  //   };
  //   const totpToken = this.tokensService.signJwt(
  //     MULTI_FACTOR_TOKEN,
  //     mfaTokenPayload,
  //     this.configService.get<string>('security.mfaTokenExpiry'),
  //   );
  //   if (user.twoFactorMethod === 'EMAIL' || forceMethod === 'EMAIL') {
  //     this.email.send({
  //       to: `"${user.name}" <${user.prefersEmail.email}>`,
  //       template: 'auth/login-link',
  //       data: {
  //         name: user.name,
  //         minutes: parseInt(this.configService.get<string>('security.mfaTokenExpiry') ?? ''),
  //         link: `${this.configService.get<string>(
  //           'frontendUrl',
  //         )}/auth/link/login%2Ftoken?token=${this.tokensService.signJwt(
  //           EMAIL_MFA_TOKEN,
  //           { id: user.id },
  //           '30m',
  //         )}`,
  //       },
  //     });
  //   } else if (user.twoFactorMethod === 'SMS' || forceMethod === 'SMS') {
  //     if (!user.twoFactorPhone) throw new ApiException(ErrorCode.MFA_PHONE_NOT_FOUND);
  //     this.twilioService.send({
  //       to: user.twoFactorPhone,
  //       body: `${this.getOneTimePassword(user.twoFactorSecret)} is your ${
  //         this.configService.get<string>('meta.appName') ?? ''
  //       } verification code.`,
  //     });
  //   }
  //   return {
  //     totpToken,
  //     type: forceMethod || user.twoFactorMethod,
  //     multiFactorRequired: true,
  //   };
  // }

  // private async checkLoginSubnet(
  //   ipAddress: string,
  //   _: string, // userAgent
  //   checkLocationOnLogin: boolean,
  //   id: number,
  //   origin?: string,
  // ): Promise<void> {
  //   if (!checkLocationOnLogin) return;
  //   const subnet = anonymize(ipAddress);
  //   const previousSubnets = await this.prisma.approvedSubnet.findMany({
  //     where: { user: { id } },
  //   });
  //   let isApproved = false;
  //   for await (const item of previousSubnets) {
  //     if (!isApproved) if (await compare(subnet, item.subnet)) isApproved = true;
  //   }
  //   if (!isApproved) {
  //     const user = await this.prisma.user.findUnique({
  //       where: { id },
  //       select: { name: true, prefersEmail: true, checkLocationOnLogin: true },
  //     });
  //     if (!user) throw new ApiException(ErrorCode.USER_NOT_FOUND);
  //     if (!user.checkLocationOnLogin) return;
  //     const location = await this.geolocationService.getLocation(ipAddress);
  //     const locationName =
  //       [
  //         location?.city?.names?.en,
  //         (location?.subdivisions ?? [])[0]?.names?.en,
  //         location?.country?.names?.en,
  //       ]
  //         .filter((i) => i)
  //         .join(', ') || 'Unknown location';
  //     if (user.prefersEmail)
  //       this.email.send({
  //         to: `"${user.name}" <${user.prefersEmail.email}>`,
  //         template: 'auth/approve-subnet',
  //         data: {
  //           name: user.name,
  //           locationName,
  //           minutes: 30,
  //           link: `${
  //             origin ?? this.configService.get<string>('frontendUrl')
  //           }/auth/link/approve-subnet?token=${this.tokensService.signJwt(
  //             APPROVE_SUBNET_TOKEN,
  //             { id },
  //             '30m',
  //           )}`,
  //         },
  //       });
  //     throw new ApiException(ErrorCode.UNVERIFIED_LOCATION);
  //   }
  // }

  async hashAndValidatePassword(password: string, ignorePwnedPassword: boolean): Promise<string> {
    if (!ignorePwnedPassword) {
      if (!this.configService.get<boolean>('security.passwordPwnedCheck'))
        return await hash(password, this.configService.get<number>('security.saltRounds') ?? 10);
      if (!(await this.pwnedService.isPasswordSafe(password)))
        throw new ApiException(ErrorCode.COMPROMISED_PASSWORD);
    }
    return await hash(password, this.configService.get<number>('security.saltRounds') ?? 10);
  }

  /** Get logging in scopes for a user */
  async getScopes(userId: number, groupId?: number): Promise<string[]> {
    const user = await this.prisma.user.findUnique({
      select: { roleId: true, id: true, active: true },
      where: { id: userId },
    });
    if (!user.active) return [];
    // get user scope
    const userCacheKey = USER_SCOPE_KEY.replace('{USER_ID}', userId.toString());
    const userPermissions = await this.redis.getOrSet<string[]>(
      userCacheKey,
      async () => {
        // Add user permissions
        const userPermissions = await this.scopeService.getUserPermissions(user.id);
        // Add user additional permissions from subscribed plan
        const additionalUserPermissions = await this.scopeService.getResourcePlanPermissions(
          user.id,
          ResourceSubsciberType.USER,
        );
        // Add system permissions
        const systemPermissions = await this.scopeService.getSystemPermissions(user.roleId);
        return [...userPermissions, ...additionalUserPermissions, ...systemPermissions];
      },
      60 * 60,
    );

    if (groupId) {
      // get group membership permissions
      const groupMembershipCacheKey = GROUP_MEMBERSHIP_SCOPE_KEY.replace(
        '{GROUP_ID}',
        groupId.toString(),
      ).replace('{USER_ID}', userId.toString());

      const groupMembershipPermissions = await this.redis.getOrSet<string[]>(
        groupMembershipCacheKey,
        async () => {
          const group = await this.prisma.group.findUnique({
            where: { id: groupId },
          });
          const membership = await this.prisma.membership.findFirst({
            where: { user: { id: userId }, groupId: groupId },
            select: {
              id: true,
              group: { select: { id: true } },
              roleId: true,
            },
          });
          // Add group permissions
          if (membership != null) {
            const groupPermissions = await this.scopeService.getGroupPermissions(
              membership.roleId,
              group,
            );
            // added bot review nomination related permissions
            const botReviewNominationPermissions =
              await this.scopeService.getBotNorminationPermission(groupId, membership.id);
            return [...groupPermissions, ...botReviewNominationPermissions];
          } else {
            return undefined;
          }
        },
        60 * 60,
      );
      // get group resource scopes
      const groupResourceScopeCacheKey = GROUP_RESOURCE_SCOPE_KEY.replace(
        '{GROUP_ID}',
        groupId.toString(),
      );
      const groupResourceScopes = await this.redis.getOrSet<string[]>(
        groupResourceScopeCacheKey,
        async () => {
          const group = await this.prisma.group.findUnique({
            where: { id: groupId },
          });
          return await this.scopeService.getResourcePlanPermissions(
            group.id,
            group.groupType as ResourceSubsciberType,
            group.env,
          );
        },
        60 * 60,
      );
      return [...userPermissions, ...(groupMembershipPermissions ?? []), ...groupResourceScopes];
    } else {
      return userPermissions;
    }
  }

  private async recursivelyGetSubgroupIds(groupId: number) {
    const subgroups = await this.prisma.group.findMany({
      where: { parent: { id: groupId } },
      select: {
        id: true,
        parent: { select: { id: true } },
        subgroups: { select: { id: true } },
      },
    });
    const ids = subgroups.map((i) => i.id);
    for await (const group of subgroups) {
      for await (const subgroup of group.subgroups) {
        const recurisiveIds = await this.recursivelyGetSubgroupIds(subgroup.id);
        ids.push(...recurisiveIds);
      }
    }
    return ids;
  }

  async mergeUsers(token: string): Promise<{ success: true }> {
    let baseUserId: number | undefined = undefined;
    let mergeUserId: number | undefined = undefined;
    try {
      const result = this.tokensService.verify<{
        baseUserId: number;
        mergeUserId: number;
      }>(MERGE_ACCOUNTS_TOKEN, token);
      baseUserId = result.baseUserId;
      mergeUserId = result.mergeUserId;
    } catch (error) {}
    if (!baseUserId || !mergeUserId) throw new ApiException(ErrorCode.USER_NOT_FOUND);
    return this.merge(baseUserId, mergeUserId);
  }

  private async merge(baseUserId: number, mergeUserId: number): Promise<{ success: true }> {
    const baseUser = await this.prisma.user.findUnique({
      where: { id: baseUserId },
    });
    const mergeUser = await this.prisma.user.findUnique({
      where: { id: mergeUserId },
    });
    if (!baseUser || !mergeUser) throw new ApiException(ErrorCode.USER_NOT_FOUND);

    const combinedUser: Record<string, any> = {};
    [
      'checkLocationOnLogin',
      'countryCode',
      'gender',
      'name',
      'notificationEmails',
      'active',
      'prefersLanguage',
      'prefersColorScheme',
      'prefersReducedMotion',
      'role',
      'timezone',
      'twoFactorMethod',
      'twoFactorPhone',
      'twoFactorSecret',
      'attributes',
    ].forEach((key) => {
      if (mergeUser[key] != null) combinedUser[key] = mergeUser[key];
    });
    await this.prisma.user.update({
      where: { id: baseUserId },
      data: combinedUser,
    });

    // for await (const dataType of [
    //   this.prisma.membership as Prisma.EmailDelegate,
    //   this.prisma.email as Prisma.EmailDelegate,
    //   this.prisma.session as Prisma.EmailDelegate,
    //   this.prisma.approvedSubnet as Prisma.EmailDelegate,
    //   this.prisma.backupCode as Prisma.EmailDelegate,
    //   this.prisma.identity as Prisma.EmailDelegate,
    //   this.prisma.auditLog as Prisma.EmailDelegate,
    //   this.prisma.apiKey as Prisma.EmailDelegate,
    // ] as Prisma.EmailDelegate[]) {
    //   for await (const item of await (dataType as Prisma.EmailDelegate).findMany(
    //     {
    //       where: { user: { id: mergeUserId } },
    //       select: { id: true },
    //     },
    //   ))
    //     await (dataType as Prisma.EmailDelegate).update({
    //       where: { id: item.id },
    //       data: { user: { connect: { id: baseUserId } } },
    //     });
    // }

    // await this.prisma.user.delete({ where: { id: mergeUser.id } });
    return { success: true };
  }

  // insert the last used group in db
  async addLastUsedGroup(userId: number, groupId: number) {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
    });
    // select the last used group by userId and groupType
    const lastUsedGroups = await this.lastUsedGroupsService.getLastUsedGroupsByGroupType(
      userId,
      group.groupType,
    );
    // if the last used group is not exist, create a new one
    if (!lastUsedGroups) {
      await this.lastUsedGroupsService.createLastUsedGroups(userId, group.groupType, groupId);
    } else {
      // if the last used group is exist, update the last used group
      const maxNum = this.configService.get<number>('lastUsedGroups.maxNum');
      // if have the same groupId, remove
      const newLastUsedHistory = lastUsedGroups.lastUsedHistory.filter(
        (item) => item['groupId'] !== groupId,
      );
      // if lastUsedHistory > maxNum, remove the exceed
      const oldLastUsed = newLastUsedHistory.slice(0, maxNum - 1);
      await this.lastUsedGroupsService.updateLastUsedGroups(userId, group.groupType, [
        { groupId, lastAccessTime: new Date() },
        ...oldLastUsed,
      ]);
    }
  }

  async userEmailExist(
    ipAddress: string,
    userAgent: string,
    userEmail: string,
  ): Promise<OutLookResponse> {
    const email = safeEmail(userEmail);
    const user = await this.prisma.user.findFirst({
      where: { emails: { some: { email, emailSafe: email } } },
      include: {
        emails: true,
        prefersEmail: true,
      },
    });

    if (user && !user.active) {
      this.logger.error(`user login email: ${email} ->> user not active`);
      throw new ApiException(ErrorCode.INACTIVE_USER);
    }

    let token: TokenResponse | undefined;

    if (user) {
      token = await this.loginResponse(ipAddress, userAgent, user);
    }

    return {
      accessToken: token?.accessToken,
      refreshToken: token?.refreshToken,
      userExist: !!user,
      userId: user?.id,
    };
  }
}
