import { ApiKeyAuth, BasicAuth } from '@elastic/elasticsearch/lib/pool';
import Stripe from 'stripe';

export interface Configuration {
  backendEnv: string;
  maxRemarksCount: number;
  hktEmailSuffix: string;
  frontendUrl: string;
  frontendDomain: string;
  meta: {
    appName: string;
    domainVerificationFile: string;
  };

  caching: {
    geolocationLruSize: number;
    apiKeyLruSize: number;
    apiKeyTtl: number;
  };

  rateLimit: {
    points: number;
    duration: number;
    sendEmailUrl: string;
    rateLimitEX: number;
    rateLimitTimeOut: number;
  };

  security: {
    saltRounds: number;
    jwtSecret: string;
    totpWindowPast: number;
    totpWindowFuture: number;
    mfaTokenExpiry: string;
    mergeUsersTokenExpiry: string;
    accessTokenExpiry: string;
    refreshTokenExpiry: string;
    passwordPwnedCheck: boolean;
    unusedRefreshTokenExpiryDays: number;
    inactiveUserDeleteDays: number;
    privateKey: string | Buffer;
    publicKey: string | Buffer;
  };

  email: {
    name: string;
    from: string;
    retries: number;
    ses?: {
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
    };
    transport?: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
  };

  elasticSearch: {
    nodes: string[];
    retries: number;
    auth?: BasicAuth | ApiKeyAuth;
    aws?: {
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
    };
    ca: string;
  };

  webhooks: {
    retries: number;
  };

  sms: {
    retries: number;
    twilioAccountSid: string;
    twilioAuthToken: string;
  };

  payments: {
    stripeApiKey: string;
    stripeProductId: string;
    stripeEndpointSecret: string;
    paymentMethodTypes: Array<Stripe.Checkout.SessionCreateParams.PaymentMethodType>;
  };

  tracking: {
    mode: 'all' | 'api-key' | 'user' | 'api-key-or-user';
    index: string;
    deleteOldLogs: boolean;
    deleteOldLogsDays: number;
  };

  slack: {
    token: string;
    slackApiUrl?: string;
    rejectRateLimitedCalls?: boolean;
    retries: number;
  };

  gcs: {
    accessKey: string;
    bucket: string;
  };

  s3: {
    accessKeyId: string;
    secretAccessKey: string;
    provider: string;
    region: string;
    endpoint?: string;
    profilePictureBucket?: string;
    profilePictureCdnHostname?: string;
    staticFilesBuckets: {
      PROD: string;
      TEST: string;
    };
    reportFilesBuckets: string;
    staticFilesMaxSize?: number;
    kmsEncryptionKeyArn?: string;
    piiReportBuckets: {
      PROD: string;
      TEST: string;
    };
    dailyReportBuckets: {
      PROD: string;
      TEST: string;
    };
    publicFilesBuckets: string;
    staticFilesBucket: string;
  };

  queue: {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    provider: string;
    fileQueueUrl: string;
    dataProcessQueueUrl: {
      PROD: string;
      TEST: string;
    };
  };

  cloudinary: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
  };

  github: {
    auth: string;
    userAgent?: string;
  };

  googleMaps: {
    apiKey: string;
  };

  gravatar: {
    enabled: boolean;
  };
  llm: {
    backend: string;
    backend_timeout: number;
    backend_api_key: string;
  };
  ssoHkt: {
    userInfo: string;
    healthCheck: string;
  };
  ssoAzure: {
    userInfo: string;
  };

  secrets: {
    embeddings: string;
    aesKey: string;
  };

  gravitee: {
    orgId: string;
    envId: string;
    graviteeHost: string;
    botApiId: string;
    graviteeApiKey: string;
    apiPathPattern: string;
  };

  aiResource: {
    callbackKey: string;
    genResQueue: {
      live: string;
      test: string;
    };
  };

  kYCVerification: {
    expiryDay: number;
  };

  wikijs: {
    host: string;
    apiKey: string;
    guestGroupId: number;
    guestUserId: number;
    ssoProviderKey: string;
    defaultPassword: string;
    token: {
      issuer: string;
      publicKey: string | Buffer;
    };
    genKBChatHostContext: string;
  };

  flow: {
    backend: string;
    backendTimeout: number;
    api_log: boolean;
    api_log_include_header: boolean;
    apiKeyHeader: string;
    apiKey: string;
    enableChatDebug: boolean;
  };

  cronJob: {
    apiKey: string;
  };

  changeManagement: {
    approvalTransactionTimeout: number;
  };

  redis: {
    url: string;
    tls: boolean;
    bullMqUrl: string;
  };

  postman: {
    apiKey: string;
    groupId: number;
    scopes: string[];
  };

  llmGuard: {
    backendPrefix: string;
    promptPrefix: string;
    fullScanPiiVersion: number;
  };

  chatWithData: {
    clientUrl: string;
    authKey: string;
    timeout: number;
  };
  internalChat: {
    authKey: string;
  };
  insightGenerator: {
    host: string;
    timeout: number;
  };
  jobQueue: {
    backendPrefix: string;
    reScanPiiCallbackUrl: string;
    authKey: string;
  };

  splitFileSize: number;

  defaultPriorityLimit: number;
  autoTest: {
    authKey: string;
    clientUrl: string;
    timeout: number;
  };

  scanMalware: {
    backendPrefix: string;
    callbackUrl: string;
    reScanCallbackUrl: string;
    chatFileCallbackUrl: string;
  };

  litellm: {
    baseUrl: string;
    apiKey: string;
  };

  notification: {
    authKey: string;
    clientUrl: string;
    timeout: number;
  };

  lastUsedGroups: {
    maxNum: number;
  };

  n8n: {
    baseUrl: string;
  };

  batchProcessor: {
    baseUrl: string;
    apiKey: string;
  };

  llmEngines: {
    configHide: boolean;
  };

  appCenterKeycloak: {
    baseUrl: string;
    realm: string;
    adminUsername: string;
    adminPassword: string;
    groupRolesMappingKey: string;
    clientSecret: string;
    clientId: string;
    issuer: string;
  };
  
  documentAi: {
    authKey: string;
  };  

  fileConversion: {
    url: string;
    callbackUrl: string;
    callbackSecretKeyName: string;
    apiKey: string;
  };
}

export interface AppCenterScope {
  name: string;
  iconUri: string;
  displayName?: string;
}

export interface AppCenterPolicy {
  name: string;
  description: string;
  type: 'client' | 'scope';
  logic: 'POSITIVE' | 'NEGATIVE';
  decisionStrategy: 'UNANIMOUS' | 'AFFIRMATIVE' | 'CONSENSUS';
  config: {
    clients?: string;
    scopes?: string;
    applyPolicies?: string[];
  };
}
