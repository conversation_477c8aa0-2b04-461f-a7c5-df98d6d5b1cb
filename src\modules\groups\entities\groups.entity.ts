import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import {
  $Enums,
  FileClassification,
  FileConversionStatus,
  FileStatus,
  HasPromptInjection,
  HistoricalFileMalwareScan,
  ModelFile,
  PiiFileStatus,
  ScanMalwareStatus,
} from '@prisma/client';
import { Exclude } from 'class-transformer';

export class ModelFileEntity implements ModelFile {
  @ApiProperty({ description: 'id' })
  id: number;

  @ApiProperty({ description: 'File name' })
  filename: string;

  @Exclude()
  filetype: string;

  @Exclude()
  fileExt: string;

  @Exclude()
  s3Path: string;

  @Exclude()
  gcsPath: string;

  @Exclude()
  summary: string;

  @Exclude()
  description: string;

  @Exclude()
  modelId: number;

  @ApiProperty({ description: 'Group id' })
  groupId: number;

  @ApiProperty({ description: 'Upload user id' })
  uploaderId: number;

  @ApiProperty({ description: 'Creation time' })
  createdAt: Date;

  @ApiProperty({ description: 'Update time' })
  updatedAt: Date;

  @ApiProperty({ description: 'Document id' })
  docId: string;

  @ApiProperty({ description: 'File status', enum: FileStatus })
  status: FileStatus;

  @ApiProperty({ description: 'Error message if file has error' })
  errorMsg: string;

  @ApiProperty()
  isApproved: boolean;

  @ApiProperty()
  fileClassification: FileClassification;

  constructor(partial: Partial<ModelFileEntity>) {
    Object.assign(this, partial);
  }
  @ApiProperty({ description: 'Current version(YYYYMMDD) scanned by malware' })
  scanMalwareVersion: string;

  @ApiProperty({ description: 'malware scan error message' })
  scanMalwareErrorMsg: string;

  @ApiProperty({ description: 'firstScanReport has PromptInjection' })
  hasPromptInjection: HasPromptInjection;
  fileSize: number;

  @ApiProperty({ description: 'fullScanReportPath' })
  fullScanReportPath: string;

  @ApiProperty({ description: 'fullScanReport Creation time' })
  fullScanReportCreatedAt: Date;

  @ApiProperty({ description: 'fullScanReport Update time' })
  fullScanReportUpdatedAt: Date;

  @ApiProperty({ description: 'firstScanReport errCode' })
  errCode: string;

  @ApiProperty({ description: 'firstScanReport hasPii' })
  hasPii: string;

  @ApiProperty({ description: 'firstScanReport detectedPii' })
  detectedPii: string;

  @ApiProperty({ description: 'piiFileStatus' })
  piiFileStatus: PiiFileStatus;

  @ApiProperty({ description: 'button permision List' })
  buttonList: string[];

  @ApiProperty({ description: 'status column alias' })
  statusExt?: string;

  @ApiProperty({ description: 'piiFileStatus' })
  scanMalwareStatus: ScanMalwareStatus;

  @ApiProperty({ description: 'piiFileStatus' })
  malwareRating: string;

  @ApiHideProperty()
  deletedAt: Date;
  @ApiProperty({ description: 'indexing Start Date' })
  indexingStartDate: Date;

  @ApiProperty({ description: 'indexing End Date' })
  historicalFileMalwareScan: HistoricalFileMalwareScan[];

  @ApiHideProperty()
  deletedBy: number;

  @ApiProperty({ description: 'fullScanReportVersion' })
  fullScanReportVersion: number;

  @ApiProperty({ description: 'rescanPiiErrorMsg' })
  rescanPiiErrorMsg: string;

  @ApiProperty({ description: 'file verify status' })
  verifyStatus: $Enums.FileVerifyStatus;

  @ApiProperty({ description: 'file verify error code' })
  verifyErrCode: string;

  @ApiProperty({ description: 'file verify error msg' })
  verifyErrorMsg: string;

  @ApiProperty({ description: 'if auto embedding the file after approval' })
  autoIndex: boolean;

  @ApiProperty({ description: 'bullmq id' })
  jobId: string;

  @ApiProperty({ description: 'job position' })
  position?: number;

  @ApiProperty({ description: 'original file path' })
  originalFilePath: string;

  @ApiProperty({ description: 'file conversion status' })
  fileConversionStatus: FileConversionStatus;

  @ApiProperty({ description: 'file conversion error message' })
  fileConversionErrorMsg: string;
}
