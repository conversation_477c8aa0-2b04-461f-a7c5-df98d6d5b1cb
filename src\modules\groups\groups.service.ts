import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import {
  ApiResourceStatus,
  Environment,
  Group,
  GroupType,
  Prisma,
  ResourceSubsciberType,
  SystemName,
} from '@prisma/client';
import randomColor from 'randomcolor';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { MutilpleLevelFeatureFlagsModelDto } from '../mutilple-level-feature-flags/mutilple-level-feature-flags-model.dto';
import {
  GroupInfo,
  SetBotResourcePlanActivateDto,
  GroupNameResponseDto,
  UpdateGroupDto,
  CreateBotResourcePlanDto,
  UpdateBotResourcePlanDto,
  SetBotResourcePlanDefaultDto,
} from './groups.dto';

import { chatChannelType } from 'prisma/seedData/botPlan.seedData';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { ChannelType } from '../llm-models/dto/chat-llm-model.dto';
import { CreateOrUpdateCustomPlanDto } from '../plans/plans.dto';
import { PlansService } from '../plans/plans.service';
import { ConfigService } from '@nestjs/config';
import { S3Service } from '../../providers/s3/s3.service';
import { ResizeImageService } from '../../providers/resize-image/resize-image.service';
import { UserRequest } from '../auth/auth.interface';
import { TokensService } from '../../providers/tokens/tokens.service';

@Injectable()
export class GroupsService {
  private logger = new Logger(GroupsService.name);
  constructor(
    private prisma: PrismaService,
    private plansService: PlansService,
    private featureFlagService: FeatureFlagService,
    private configService: ConfigService,
    private s3Service: S3Service,
    private resizeImageService: ResizeImageService,
    private tokensService: TokensService,
  ) {}

  async createGroup(
    userId: number,
    data: Omit<Omit<Prisma.GroupCreateInput, 'group'>, 'user'>,
    tx: Prisma.TransactionClient,
  ) {
    let initials = data.name.trim().substring(0, 2).toUpperCase();
    if (data.name.includes(' '))
      initials = data.name
        .split(' ')
        .map((i) => i.trim().substring(0, 1))
        .join('')
        .toUpperCase();
    data.profilePictureUrl =
      data.profilePictureUrl ??
      `https://ui-avatars.com/api/?name=${initials}&background=${randomColor({
        luminosity: 'light',
      }).replace('#', '')}&color=000000`;
    return tx.group.create({
      include: { memberships: { include: { group: true, Role: true } } },
      data: {
        ...data,
        memberships: {
          create: {
            user: { connect: { id: userId } },
            Role: {
              connect: {
                name_groupId_systemName: {
                  name: 'OWNER',
                  groupId: 0,
                  systemName: SystemName.GROUP_OWNER,
                },
              },
            },
          },
        },
        createdBy: {
          connect: {
            id: userId,
          },
        },
      },
    });
  }

  /**
   * @description check group is exist , if not exist will throw ApiException
   // eslint-disable-next-line prettier/prettier
   * @param id 
   * @returns {Boolean}
   */
  async isExist(id: number) {
    const isExist = await this.prisma.group.findUnique({ where: { id } });
    if (isExist) {
      return true;
    }
    throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
  }

  async getGroup(
    id: number,
    {
      select,
      include,
    }: {
      select?: Record<string, boolean>;
      include?: Record<string, boolean>;
    },
  ): Promise<Expose<GroupInfo>> {
    const group = await this.prisma.group.findUnique({
      where: { id },
      select,
      include,
    } as any);
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    const groupInfo = group as GroupInfo;
    groupInfo.isEnableConnectAPI = true;
    if (group.groupType === GroupType.BOT) {
      const isEnableConnectAPI = await this.prisma.apiResource.findFirst({
        where: {
          groupId: id,
          status: ApiResourceStatus.GENERATED,
          enable: true,
        },
      });
      groupInfo.isEnableConnectAPI = !!isEnableConnectAPI;
    }
    if (group.groupType == GroupType.APP) {
      const appResource = await this.prisma.appResource.findFirst({
        include: {
          appInfo: true,
        },
        where: {
          groupId: group.id,
        },
      });
      // for remove createParamConfig data
      appResource.appInfo = { ...appResource.appInfo, createParamConfig: undefined };
      groupInfo.AppResource = [appResource];
    }
    return groupInfo;
  }

  async getGroupRateLimitQuota(groupId: number, channel: ChannelType) {
    const channelKey = chatChannelType.find((chatChannel) => chatChannel.slug === channel).key;
    const resourceKey = `rate-limit-${channelKey}`;
    const subscribedRateLimitPlan = await this.plansService.getResourceSubscribedPlan(
      groupId,
      ResourceSubsciberType.BOT,
      resourceKey,
    );
    const quotaValue = await this.plansService.getQuotaValueFromPlan(subscribedRateLimitPlan.id);
    if (!quotaValue) {
      throw new ApiException(ErrorCode.GROUP_RATE_LIMIT_NOT_FOUND);
    }
    return { points: quotaValue.value };
  }

  async updateGroup(group: Group, data: Prisma.GroupUpdateInput): Promise<Expose<Group>> {
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    group = await this.prisma.group.update({
      where: { id: group.id },
      data,
    });
    return this.prisma.expose<Group>(group);
  }

  async replaceGroup(id: number, data: Prisma.GroupCreateInput): Promise<Expose<Group>> {
    const testGroup = await this.prisma.group.findUnique({
      where: { id },
    });
    if (!testGroup) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    const group = await this.prisma.group.update({
      where: { id },
      data,
    });
    return this.prisma.expose<Group>(group);
  }

  async getGroupTypeByGroupId(groupId: number): Promise<string> {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: {
        groupType: true,
      },
    });

    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    return group.groupType;
  }

  async getGroupEnvByGroupId(groupId: number): Promise<string> {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: {
        env: true,
      },
    });

    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    return group.env;
  }

  async getGroupResourceCategory(groupId: number) {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: { groupType: true },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    return await this.plansService.getResourceCategory(group.groupType as ResourceSubsciberType);
  }

  async getAllGroupResourceCategories() {
    return await this.plansService.getAllResourceCategory(true);
  }

  async getGroupResources(groupId: number) {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: { groupType: true },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    return await this.plansService.getResources(group.groupType as ResourceSubsciberType, groupId);
  }

  async getAllGroupResources(env: Environment) {
    return await this.plansService.getAllResources(true, env);
  }

  async getGroupResourcePlans(groupId: number) {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: { groupType: true, env: true },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);

    return await this.plansService.getResourcePlans({
      subscriberType: group.groupType as ResourceSubsciberType,
      subscriberId: groupId,
      groupEnv: group.env,
    });
  }

  async getAllGroupResourcePlans(env: Environment) {
    return await this.plansService.getAllResourcePlans(true, env);
  }

  async createPlanRequest(
    groupId: number,
    requesterId: number,
    subscribedPlanIds: number[],
    customPlans: CreateOrUpdateCustomPlanDto[],
  ) {
    const group = await this.prisma.group.findUnique({
      where: { id: groupId },
      select: { groupType: true },
    });
    if (!group) throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    return await this.plansService.createPlanRequest({
      subscribedPlanIds,
      customPlans,
      requesterId,
      subscriberId: groupId,
      subscriberType: group.groupType as ResourceSubsciberType,
    });
  }

  async createBotResourcePlan(data: CreateBotResourcePlanDto) {
    return await this.plansService.createResourcePlan(data);
  }

  async updateBotResourcePlan(data: UpdateBotResourcePlanDto) {
    return await this.plansService.updateResourcePlan(data);
  }

  async setBotResourcePlanDefault(data: SetBotResourcePlanDefaultDto) {
    return await this.plansService.setResourcePlanDefault(data);
  }

  async setBotResourcePlanActivate(data: SetBotResourcePlanActivateDto) {
    return await this.plansService.setResourcePlanActivate(data);
  }

  async getGroupsNames(
    skip?: number,
    take?: number,
    where?: Prisma.GroupWhereInput,
    orderBy?: Prisma.GroupOrderByWithRelationInput,
  ): Promise<GroupNameResponseDto[]> {
    let groups = await this.prisma.group.findMany({
      skip,
      take,
      where,
      orderBy,
      select: {
        id: true,
        name: true,
        env: true,
        groupType: true,
        llmModel: {
          select: {
            active: true,
          },
        },
      },
    });
    groups = groups.filter((group) => group.groupType === GroupType.FLOW || group.llmModel?.active);
    return groups.map((group) => ({
      id: group.id,
      name: group.name,
      env: group.env,
    }));
  }

  async deprecateGroup(id: number) {
    const group = await this.prisma.$transaction(async (tx) => {
      const group = await tx.group.update({ data: { isDeprecated: true }, where: { id } });
      await tx.userBookmark.deleteMany({ where: { entityId: id, entityType: 'GROUP' } });
      return group;
    });
    return group;
  }

  static featureFlagsMonthlyTokenLimitValidator(
    mutilpleLevelFeatureFlagsModelDto: MutilpleLevelFeatureFlagsModelDto,
  ) {
    if (!mutilpleLevelFeatureFlagsModelDto?.metaData) {
      throw new ApiException(ErrorCode.FEATURE_FLAG_METADATA_NOT_NULL);
    }

    if (!Object.keys(mutilpleLevelFeatureFlagsModelDto.metaData).includes('value')) {
      throw new ApiException(ErrorCode.FEATURE_FLAG_METADATA_VALUE_NOT_NULL);
    }

    if (!Number.isInteger(mutilpleLevelFeatureFlagsModelDto.metaData['value'])) {
      throw new ApiException(ErrorCode.FEATURE_FLAG_METADATA_VALUE_NOT_INT);
    }
  }

  async updateGroupAvatar(
    group: Group,
    file: Express.Multer.File,
    data: UpdateGroupDto,
  ): Promise<string> {
    if (file) {
      // user upload avatar
      // check file size > 1MB
      if (file.size > 1 * 1024 * 1024) throw new ApiException(ErrorCode.FILE_TOO_LARGE);
      const fileExtension = file.originalname.split('.').pop();
      // check file extension in ['jpg', 'png']
      if (!['jpg', 'png'].includes(fileExtension.toLowerCase())) {
        throw new ApiException(ErrorCode.INVALID_FILE_EXTENSION);
      }
      if (!this.configService.get<string>(`s3.publicFilesBuckets`))
        throw new InternalServerErrorException('Static file bucket not set');
      // remove old avatar
      await this.removeAvatarFile(group);
      // resize image to 256x256
      const newImage = await this.resizeImageService.resizeImage(file.buffer, 256, 256);
      // upload image to s3
      const { Location: profilePictureUrl } = await this.s3Service.uploadAndCacheControl(
        `avatar/picture-${
          group.id
        }-${this.tokensService.generateUuid()}.${fileExtension.toLowerCase()}`,
        file.originalname,
        newImage,
        file.mimetype,
        group.id,
        'max-age=600',
        this.configService.get<string>(`s3.publicFilesBuckets`),
        false,
      );
      return profilePictureUrl;
    } else if (!data.profilePictureUrl) {
      // remove old avatar
      await this.removeAvatarFile(group);
      // user remove avatar
      const initials = data.name.trim().substring(0, 2).toUpperCase();
      return `https://ui-avatars.com/api/?name=${initials}&background=${randomColor({
        luminosity: 'light',
      }).replace('#', '')}&color=000000`;
    }
    // user not upload avatar, keep old avatar
    return data.profilePictureUrl;
  }

  async userHasAccessOrIsSudo(userReq: UserRequest, groupId: number) {
    const isSudo = userReq.user.scopes.includes('group-*:read-playground');
    if (isSudo) {
      return true;
    }
    const isBotMember = await this.prisma.membership.count({
      where: {
        groupId: groupId,
        userId: userReq.user.id,
      },
    });
    return isBotMember >= 1;
  }

  async removeAvatarFile(group: Group) {
    const url =
      'https://' +
      `${this.configService.get<string>(
        's3.publicFilesBuckets',
      )}.s3.${this.configService.get<string>('s3.region')}.amazonaws.com/`;
    if (group.profilePictureUrl.startsWith(url)) {
      // get the path
      const s3Path = group.profilePictureUrl.replace(url, '');
      //remove file
      await this.s3Service.delete(
        `${this.configService.get<string>('s3.publicFilesBuckets')}`,
        s3Path,
      );
    }
  }
}
