import { Module } from '@nestjs/common';
import { OptionsService } from './options.service';
import { PrismaModule } from '../../providers/prisma/prisma.module';
import { OptionsController } from './options.controller';
import { ConfigModule } from '@nestjs/config';
import { PlansModule } from '../plans/plans.module';

@Module({
  imports: [PrismaModule, ConfigModule, PlansModule],
  controllers: [OptionsController],
  providers: [OptionsService],
  exports: [OptionsService],
})
export class OptionsModule {}
