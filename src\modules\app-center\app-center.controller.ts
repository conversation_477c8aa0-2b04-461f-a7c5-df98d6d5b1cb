import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { UserRequest } from '../auth/auth.interface';
import { ScopeMap } from '../auth/scope-map.decorator';
import { postReportScopeMap } from '../auth/scope-requirement-map.constants';
import { Scopes } from '../auth/scope.decorator';
import { AppCenterService } from './app-center.service';
import { CreateAppDto, CreateBotToolsDto } from './dto/create-app.dto';
import { BotToolsPaginationDto } from './dto/list-app.dto';
import { UpdateAppDto } from './dto/update-app.dto';
import { LaunchAppReqDto } from './dto/launch-app.dto';

@ApiTags('App Center - Apps')
@Controller('app-center/apps')
@UsePipes(
  new ValidationPipe({
    whitelist: true,
  }),
)
export class AppCenterController {
  constructor(private readonly appCenterService: AppCenterService) {}

  @Post(':groupId/bot-tools')
  @Scopes('group-{groupId}:write-bot-tools')
  async createBotToolsApp(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: UserRequest,
    @Body() createAppDto: CreateBotToolsDto,
  ) {
    createAppDto.groupId = groupId;
    return this.appCenterService.createBotToolsApp(createAppDto, req);
  }

  @Post('/')
  @ScopeMap(postReportScopeMap)
  async createIndependenceApp(@Body() createAppDto: CreateAppDto, @Req() req: UserRequest) {
    return this.appCenterService.createApp(createAppDto, req);
  }

  @Get(':groupId/bot-tools')
  @Scopes('group-{groupId}:read-bot-tools')
  async findAllBotToolsApps(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query() botToolsPageDto: BotToolsPaginationDto,
  ) {
    return this.appCenterService.findBotTools(botToolsPageDto, groupId);
  }

  @Get(':groupId/created/bot-tools')
  @Scopes('group-{groupId}:read-bot-tools')
  async findCreatedBotTools(@Param('groupId', ParseIntPipe) groupId: number) {
    return this.appCenterService.findAllCreatedBotToolsType(groupId);
  }

  @Get(':groupId/:id')
  @Scopes('group-{groupId}:read-bot-tools')
  async findOneApp(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.appCenterService.findOneAppResource(groupId, id);
  }

  @Patch(':groupId/bot-tools/:id')
  @Scopes('group-{groupId}:write-bot-tools')
  async updateApp(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Body() updateAppDto: UpdateAppDto,
  ) {
    return this.appCenterService.updateAppResource(id, updateAppDto, groupId);
  }

  @Delete(':groupId/bot-tools/:id')
  @Scopes('group-{groupId}:write-bot-tools')
  async removeApp(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: UserRequest,
  ) {
    return this.appCenterService.removeAppResource(id, groupId, req);
  }

  @Post(':groupId/bot-tools/launch/:id')
  @Scopes('group-{groupId}:read-bot-tools')
  async launchBotToolsApp(
    @Param('id', ParseIntPipe) id: number,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() req: UserRequest,
    @Body() data: LaunchAppReqDto,
  ) {
    return this.appCenterService.launchBotTool(id, groupId, response, req, data);
  }

  @Post(':groupId/app/launch')
  @Scopes('group-{groupId}:launch-app')
  async launchApp(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() req: UserRequest,
    @Body() data: LaunchAppReqDto,
  ) {
    return this.appCenterService.launchAppWithGroupId(groupId, response, req, data);
  }
}
