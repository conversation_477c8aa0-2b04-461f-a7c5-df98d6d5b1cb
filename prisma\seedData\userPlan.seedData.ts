import { Environment, ResourceEntityType, ResourceSubsciberType, SystemName } from '@prisma/client';

export interface UserResourceCategorySeed {
  name: string;
  key: string;
  subscriberTypes: ResourceSubsciberType[];
}
export interface UserResourceSeed {
  resourceKey: string;
  resourceName: string;
  subscriberTypes: ResourceSubsciberType[];
  description: string;
  resourceCategory: string;
  permissionKey?: string;
  resourceEntityType?: ResourceEntityType;
  resourceEntityKey?: string;
  quotaRuleKey?: string;
}

export interface UserResourcePlanSeed {
  planKey: string;
  planName: string;
  resourceKey: string;
  description: string;
  groupEnvs?: Environment[];
  planRolesRequired: SystemName[];
  isDefault: boolean;
  quotaRuleKey?: string;
  quotaValue?: number;
  isDisabledPlan: boolean;
  permissionKey?: string;
}

export const userResourceCategories: UserResourceCategorySeed[] = [
  { name: 'Agent', key: 'user-agent', subscriberTypes: [ResourceSubsciberType.USER] },
  { name: 'APP', key: 'user-app', subscriberTypes: [ResourceSubsciberType.USER] },
];

export const userResourcesList: UserResourceSeed[] = [
  {
    resourceKey: 'flow',
    resourceName: 'Flow',
    subscriberTypes: [ResourceSubsciberType.USER],
    description: 'Allow to create flow',
    resourceCategory: 'user-agent',
    permissionKey: 'system:create-flow',
  },
  {
    resourceKey: 'N8N',
    resourceName: 'N8N',
    subscriberTypes: [ResourceSubsciberType.USER],
    description: 'Allow to create n8n',
    resourceCategory: 'user-app',
    permissionKey: '',
  },
];

export const userResourcePlans: UserResourcePlanSeed[] = [
  {
    planKey: 'disable-flow',
    planName: 'Disable',
    resourceKey: 'flow',
    description: 'To disable creating flow',
    planRolesRequired: [SystemName.BOT_CREATOR],
    isDefault: true,
    isDisabledPlan: true,
  },
  {
    planKey: 'enable-flow',
    planName: 'Enable',
    resourceKey: 'flow',
    description: 'To enable creating flow',
    planRolesRequired: [SystemName.BOT_CREATOR],
    isDefault: false,
    isDisabledPlan: false,
    permissionKey: 'system:create-flow',
  },
  {
    planKey: 'disable-create-n8n',
    planName: 'Disable',
    resourceKey: 'N8N',
    description: 'To disable creating n8n',
    planRolesRequired: [SystemName.BOT_CREATOR],
    isDefault: true,
    isDisabledPlan: true,
  },
  {
    planKey: 'enable-create-n8n',
    planName: 'Enable',
    resourceKey: 'N8N',
    description: 'To enable creating n8n',
    planRolesRequired: [SystemName.BOT_CREATOR],
    isDefault: false,
    isDisabledPlan: false,
    permissionKey: 'system:create-app-n8n',
  },
];
