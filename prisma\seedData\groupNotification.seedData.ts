import {
  GroupNotificationChannel,
  GroupNotificationType,
  GroupNotificationRecipientRole,
} from '@prisma/client';
import { FeatureFlagKey } from '../../src/modules/feature-flags/feature-flags.constants';
export interface GroupNotificationSeed {
  title: string;
  groupNotificationName: string;
  channel: GroupNotificationChannel;
  type: GroupNotificationType;
  description: string;
  interval: string;
  defaultSilentPeriod?: number;
  defaultRecipientRoles: GroupNotificationRecipientRole[];
  enabledOverride?: boolean;
  defaultEnabledNotify?: boolean;
  featureFlagKey?: string;
}

export const groupNotificationSeedData: GroupNotificationSeed[] = [
  {
    title: 'Monthly Rating and Scoring Report',
    groupNotificationName: 'scoring-and-rating-monthly',
    channel: GroupNotificationChannel.EMAIL,
    type: GroupNotificationType.REPORT,
    description:
      "On the 1st of each month, The system will send a notification of  Rating Report to bot owner & admin If notifications are enabled. Help bot owner & admin regularly know their bots' usage rating and may take next action.",
    interval: 'Monthly',
    defaultRecipientRoles: [
      GroupNotificationRecipientRole.OWNER,
      GroupNotificationRecipientRole.ADMIN,
    ],
    enabledOverride: true,
    defaultEnabledNotify: false,
  },
  {
    title: 'Rate Limit Alert',
    groupNotificationName: 'rate-limit',
    channel: GroupNotificationChannel.EMAIL,
    type: GroupNotificationType.IMMEDIATE_ALERT,
    description:
      'The system will instantly trigger an alert notification to the bot owner/admin when the rate of API calls in your bot exceeds the quota per hour. There will be a 1-hour interval of silence before the next alert. ',
    interval: 'Instant',
    defaultRecipientRoles: [
      GroupNotificationRecipientRole.ADMIN,
      GroupNotificationRecipientRole.OWNER,
    ],
    enabledOverride: true,
    defaultEnabledNotify: true,
    defaultSilentPeriod: 1,
  },
  {
    title: '2nd Approval for New Upload File',
    groupNotificationName: 'file-process-approval-request',
    channel: GroupNotificationChannel.EMAIL,
    type: GroupNotificationType.IMMEDIATE_ALERT,
    description:
      'The system will instantly send an approval notification to the bot reviewer & designated approver when non-public domain new file passed first approval in your bot.',
    interval: 'Instant',
    defaultSilentPeriod: 0,
    defaultRecipientRoles: [GroupNotificationRecipientRole.REVIEWER_N_APPROVER],
    enabledOverride: true,
    defaultEnabledNotify: true,
    featureFlagKey: FeatureFlagKey.ENABLE_SECONDARY_APPROVAL,
  },
  {
    title: 'Monthly Token Limit',
    groupNotificationName: 'monthly-token',
    channel: GroupNotificationChannel.EMAIL,
    type: GroupNotificationType.REGULAR_ALERT,
    description:
      'The system will trigger a daily usage alert notification on demand to bot owners and admins when LLM monthly token usage in your bot reaches the high usage thresholds.',
    interval: 'Daily',
    defaultRecipientRoles: [
      GroupNotificationRecipientRole.ADMIN,
      GroupNotificationRecipientRole.OWNER,
    ],
    enabledOverride: true,
    defaultEnabledNotify: true,
    defaultSilentPeriod: 0,
  },
];
