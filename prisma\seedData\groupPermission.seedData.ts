import { Environment, GroupType, SystemName } from '@prisma/client';
import { GroupPermissionFeature } from '../constants/permission-feature';

export interface PermissionGroupSettingSeed {
  isApiKeyAllowed: boolean;
  groupTypes: GroupType[];
  isCustomRoleAllowed: boolean;
  isActiveOnly: boolean;
  featureKey: GroupPermissionFeature;
}

export interface GroupPermissionSeed {
  description: string;
  permissionKey: string;
  envs: Environment[];
  groupSetting: PermissionGroupSettingSeed;
  link?: 'LLM_ENGINE' | 'ROLE';
}

interface Role {
  name: string;
  permissions: string[];
  systemName: SystemName;
  order?: number;
  isCustomRoleTemplateAllowed?: boolean;
  groupTypes?: GroupType[];
}

export const groupPermissionList: GroupPermissionSeed[] = [
  {
    description: 'Create and update API keys',
    permissionKey: 'group-{groupId}:write-api-key',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.API_KEY,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read API keys',
    permissionKey: 'group-{groupId}:read-api-key',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.API_KEY,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read API key logs',
    permissionKey: 'group-{groupId}:read-api-key-logs',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.API_KEY,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read audit log',
    permissionKey: 'group-{groupId}:read-audit-log',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.AUDIT_LOG,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read group details',
    permissionKey: 'group-{groupId}:read-info',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.BASIC,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Update group details',
    permissionKey: 'group-{groupId}:write-info',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete group',
    permissionKey: 'group-{groupId}:delete',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Create and update memberships',
    permissionKey: 'group-{groupId}:write-membership',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.MEMBERSHIP,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read memberships',
    permissionKey: 'group-{groupId}:read-membership',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.MEMBERSHIP,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create and update a LLM model config',
    permissionKey: 'group-{groupId}:write-llm-model',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read a LLM model config',
    permissionKey: 'group-{groupId}:read-llm-model',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Use Playground',
    permissionKey: 'group-{groupId}:read-playground',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.APP],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BASIC,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Chat Session Data',
    permissionKey: 'group-{groupId}:read-chat-session',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write Chat Session Data',
    permissionKey: 'group-{groupId}:write-chat-session',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create and update model files',
    permissionKey: 'group-{groupId}:write-llm-model-files',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Upload model files',
    permissionKey: 'group-{groupId}:upload-llm-model-files',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Approve or process model files',
    permissionKey: 'group-{groupId}:approve-or-process-llm-model-files',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read model files',
    permissionKey: 'group-{groupId}:read-llm-model-files',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Download model files',
    permissionKey: 'group-{groupId}:download-llm-model-files',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Download security report',
    permissionKey: 'group-{groupId}:download-security-report',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.SECURITY_SCAN,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read security report',
    permissionKey: 'group-{groupId}:read-security-report',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.SECURITY_SCAN,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete model files',
    permissionKey: 'group-{groupId}:delete-llm-model-files',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Roles',
    permissionKey: 'group-{groupId}:read-role',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.ROLE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create and update Roles',
    permissionKey: 'group-{groupId}:write-role',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.ROLE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete Roles',
    permissionKey: 'group-{groupId}:delete-role',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.ROLE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Assign membership to all roles',
    permissionKey: 'group-{groupId}:role-*',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.ROLE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read AI generated resources',
    permissionKey: 'group-{groupId}:read-ai-resource',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.AI_RESOURCE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write AI generated resources',
    permissionKey: 'group-{groupId}:write-ai-resource',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.AI_RESOURCE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create and update webhooks',
    permissionKey: 'group-{groupId}:write-webhook',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.WEBHOOK,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read webhooks',
    permissionKey: 'group-{groupId}:read-webhook',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.WEBHOOK,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete webhooks',
    permissionKey: 'group-{groupId}:delete-webhook',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.WEBHOOK,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Execute App Webhooks',
    permissionKey: 'group-{groupId}:execute-app-webhook',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.APP],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.WEBHOOK,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write group message template',
    permissionKey: 'group-{groupId}:write-message-template',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.MESSAGE_TEMPLATE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read group message template',
    permissionKey: 'group-{groupId}:read-message-template',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.MESSAGE_TEMPLATE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete group message template',
    permissionKey: 'group-{groupId}:delete-message-template',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.MESSAGE_TEMPLATE,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Manage Playground',
    permissionKey: 'group-{groupId}:write-playground',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Dashboard',
    permissionKey: 'group-{groupId}:read-dashboard',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.DASHBOARD,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Export Logs',
    permissionKey: 'group-{groupId}:export-logs',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.CHAT_REPORT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Gen Kb',
    permissionKey: 'group-{groupId}:read-gen-kb',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.GEN_KB,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write Gen Kb',
    permissionKey: 'group-{groupId}:write-gen-kb',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GEN_KB,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read group entity snapshot',
    permissionKey: 'group-{groupId}:read-entity-snapshot',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.CHANGE_MANAGEMENT,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Create and update group entity snapshot',
    permissionKey: 'group-{groupId}:write-entity-snapshot',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.CHANGE_MANAGEMENT,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Delete group entity snapshot',
    permissionKey: 'group-{groupId}:delete-entity-snapshot',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.CHANGE_MANAGEMENT,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read group data promotion request',
    permissionKey: 'group-{groupId}:read-data-promotion-request',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.CHANGE_MANAGEMENT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create and update group data promotion request',
    permissionKey: 'group-{groupId}:write-data-promotion-request',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.CHANGE_MANAGEMENT,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Create and update flows',
    permissionKey: 'group-{groupId}:write-flow',
    groupSetting: {
      groupTypes: [GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read flows',
    permissionKey: 'group-{groupId}:read-flow',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete flows',
    permissionKey: 'group-{groupId}:delete-flow',
    groupSetting: {
      groupTypes: [GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Create and update flow bot request',
    permissionKey: 'group-{groupId}:write-flow-bot-request',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read flow bot request',
    permissionKey: 'group-{groupId}:read-flow-bot-request',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Use Flow Playground',
    permissionKey: 'group-{groupId}:read-flow-playground',
    groupSetting: {
      groupTypes: [GroupType.FLOW],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create and Update Resource',
    permissionKey: 'group-{groupId}:write-api-resource',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.CONNECT_API,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Delete Api Resource',
    permissionKey: 'group-{groupId}:delete-api-resource',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.CONNECT_API,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read Api Resource',
    permissionKey: 'group-{groupId}:read-api-resource',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.CONNECT_API,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete Api Resource Document',
    permissionKey: 'group-{groupId}:delete-api-resource-file',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.CONNECT_API,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Upload Api Resource Document',
    permissionKey: 'group-{groupId}:write-api-resource-file',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.CONNECT_API,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Read Flows Chat Debug',
    permissionKey: 'group-{groupId}:read-flow-debug',
    groupSetting: {
      groupTypes: [GroupType.FLOW],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Group Feature Flag',
    permissionKey: 'group-{groupId}:read-feature-flag',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.FEATURE_FLAG,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write Group Feature Flag',
    permissionKey: 'group-{groupId}:write-feature-flag',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.FEATURE_FLAG,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Delete Flow Bot Connection',
    permissionKey: 'group-{groupId}:delete-flow-bot',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.FLOW_SETTING,
    },
    envs: [Environment.TEST],
  },
  {
    description: 'Activate/Deactivate Group',
    permissionKey: 'group-{groupId}:activate',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: [Environment.TEST, Environment.PROD],
  },
  {
    description: 'Upload temporary chat file to playground',
    permissionKey: 'group-{groupId}:upload-chat-files',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: [Environment.TEST, Environment.PROD],
  },
  {
    description: 'Read resource plan',
    permissionKey: 'group-{groupId}:read-resource-plan',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.PLAN_SUB,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write resource plan',
    permissionKey: 'group-{groupId}:write-resource-plan',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.PLAN_SUB,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write bot review nomination',
    permissionKey: 'group-{groupId}:write-review-nomination',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.MEMBERSHIP,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read a LLM model basic info',
    permissionKey: 'group-{groupId}:read-llm-model-basic',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.BASIC,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read insight setting',
    permissionKey: 'group-{groupId}:read-insight',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read insight advanced setting',
    permissionKey: 'group-{groupId}:read-insight-advanced',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create/Update insight setting',
    permissionKey: 'group-{groupId}:write-insight',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read insight report',
    permissionKey: 'group-{groupId}:read-insight-report',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create/Update insight report',
    permissionKey: 'group-{groupId}:write-insight-report',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read insight template',
    permissionKey: 'group-{groupId}:read-insight-template',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create/Update insight template',
    permissionKey: 'group-{groupId}:write-insight-template',
    groupSetting: {
      groupTypes: [GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.INSIGHT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'read group test case',
    permissionKey: 'group-{groupId}:read-test-case',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.TEST_AUTOMATION,
    },
    envs: [Environment.TEST, Environment.PROD],
  },
  {
    description: 'write group test case',
    permissionKey: 'group-{groupId}:write-test-case',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.TEST_AUTOMATION,
    },
    envs: [Environment.TEST, Environment.PROD],
  },
  {
    description: 'write group llm model files tags',
    permissionKey: 'group-{groupId}:write-llm-model-files-tags',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.UPLOAD_DATA,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read group price report',
    permissionKey: 'group-{groupId}:read-group-price-report',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.BILLING_REPORT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Download group price report',
    permissionKey: 'group-{groupId}:download-group-price-report',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.INSIGHT, GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.BILLING_REPORT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'read group notifications config',
    permissionKey: 'group-{groupId}:read-notifications-config',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.NOTIFICATION,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'write group notifications config',
    permissionKey: 'group-{groupId}:write-notifications-config',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.NOTIFICATION,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Preview public bot',
    permissionKey: 'group-{groupId}:preview-public-bot',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.PUBLIC_BOT,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write global data source',
    permissionKey: 'group-{groupId}:write-global-data-source',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Batch File',
    permissionKey: 'group-{groupId}:read-batchfile',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BATCH_PROCESS,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create Batch File',
    permissionKey: 'group-{groupId}:create-batchfile',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BATCH_PROCESS,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Delete Batch File',
    permissionKey: 'group-{groupId}:delete-batchfile',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BATCH_PROCESS,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Batch Process',
    permissionKey: 'group-{groupId}:read-batch-process',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BATCH_PROCESS,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Create Batch Process',
    permissionKey: 'group-{groupId}:create-batch-process',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BATCH_PROCESS,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Cancel Batch File',
    permissionKey: 'group-{groupId}:cancel-batch-process',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.BATCH_PROCESS,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write Bot Tools',
    permissionKey: 'group-{groupId}:write-bot-tools',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.APP_CENTER,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Bot Tools',
    permissionKey: 'group-{groupId}:read-bot-tools',
    groupSetting: {
      groupTypes: [GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.APP_CENTER,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Launch App',
    permissionKey: 'group-{groupId}:launch-app',
    groupSetting: {
      groupTypes: [GroupType.APP],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.APP_CENTER,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Read Scheduler Job',
    permissionKey: 'group-{groupId}:read-scheduler-job',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.APP, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.SCHEDULER_JOB,
    },
    envs: Object.values(Environment),
  },
  {
    description: 'Write Scheduler Job',
    permissionKey: 'group-{groupId}:write-scheduler-job',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.APP, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.SCHEDULER_JOB,
    },
    envs: Object.values(Environment),
  },
];

export const additionalGroupPermissionList: GroupPermissionSeed[] = [
  {
    description: 'Allow to use {{engine}}',
    permissionKey: 'group-{groupId}:llm-engine-{{engine}}',
    groupSetting: {
      groupTypes: [GroupType.FLOW, GroupType.BOT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: false,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.GROUP_SETTING,
    },
    envs: Object.values(Environment),
    link: 'LLM_ENGINE',
  },
  {
    description: 'Assign membership to {{role}} role',
    permissionKey: 'group-{groupId}:role-{{role}}',
    groupSetting: {
      groupTypes: [GroupType.BOT, GroupType.FLOW, GroupType.INSIGHT],
      isApiKeyAllowed: false,
      isCustomRoleAllowed: true,
      isActiveOnly: false,
      featureKey: GroupPermissionFeature.ROLE,
    },
    envs: Object.values(Environment),
    link: 'ROLE',
  },
  {
    description: 'Execute App webhooks via API',
    permissionKey: 'group-{groupId}:execute-app-webhook',
    groupSetting: {
      groupTypes: [GroupType.APP],
      isApiKeyAllowed: true,
      isCustomRoleAllowed: true,
      isActiveOnly: true,
      featureKey: GroupPermissionFeature.WEBHOOK,
    },
    envs: Object.values(Environment),
  },
];

const memberPermissions: string[] = [
  'group-{groupId}:read-info',
  'group-{groupId}:read-playground',
  'group-{groupId}:read-chat-session',
  'group-{groupId}:write-chat-session',
  'group-{groupId}:read-ai-resource',
  'group-{groupId}:write-ai-resource',
  'group-{groupId}:read-message-template',
  'group-{groupId}:read-llm-model-files',
  'group-{groupId}:upload-llm-model-files',
  'group-{groupId}:download-llm-model-files',
  'group-{groupId}:read-gen-kb',
  'group-{groupId}:read-flow-playground',
  'group-{groupId}:read-llm-model-basic',
  'group-{groupId}:read-insight',
  'group-{groupId}:read-insight-report',
  'group-{groupId}:read-insight-template',
  'group-{groupId}:read-membership',
  'group-{groupId}:read-bot-tools',
  'group-{groupId}:read-scheduler-job',
  'group-{groupId}:write-scheduler-job',
];

const contributorPermissions: string[] = memberPermissions.concat([
  'group-{groupId}:role-group_visitor',
  'group-{groupId}:upload-chat-files',
  'group-{groupId}:read-membership',
  'group-{groupId}:write-membership',
  'group-{groupId}:read-insight',
  'group-{groupId}:read-insight-report',
  'group-{groupId}:read-insight-template',
  'group-{groupId}:write-llm-model-files-tags',
  'group-{groupId}:read-role',
  'group-{groupId}:read-bot-tools',
  'group-{groupId}:read-scheduler-job',
  'group-{groupId}:write-scheduler-job',
]);

export const botNominationPermissions: string[] = [
  'group-{groupId}:read-info',
  'group-{groupId}:read-llm-model-files',
  'group-{groupId}:process-llm-model-files',
  'group-{groupId}:delete-llm-model-files',
  'group-{groupId}:read-bot-tools',
];

const visitorPermissions: string[] = [
  'group-{groupId}:read-info',
  'group-{groupId}:read-playground',
  'group-{groupId}:read-flow-playground',
  'group-{groupId}:read-llm-model-basic',
  'group-{groupId}:read-bot-tools',
];

export const groupDefaultRoles: Role[] = [
  {
    name: 'OWNER',
    permissions: groupPermissionList.map((permission) => permission.permissionKey),
    systemName: SystemName.GROUP_OWNER,
    order: 5,
  },
  {
    name: 'ADMIN',
    permissions: [
      'group-{groupId}:role-group_member',
      'group-{groupId}:role-group_visitor',
      'group-{groupId}:role-group_custom',
      'group-{groupId}:read-ai-resource',
      'group-{groupId}:write-ai-resource',
      'group-{groupId}:write-api-key',
      'group-{groupId}:read-api-key',
      'group-{groupId}:read-api-key-logs',
      'group-{groupId}:read-audit-log',
      'group-{groupId}:read-info',
      'group-{groupId}:write-info',
      'group-{groupId}:write-membership',
      'group-{groupId}:read-membership',
      'group-{groupId}:write-llm-model',
      'group-{groupId}:read-llm-model',
      'group-{groupId}:read-playground',
      'group-{groupId}:read-chat-session',
      'group-{groupId}:write-chat-session',
      'group-{groupId}:upload-llm-model-files',
      'group-{groupId}:read-llm-model-files',
      'group-{groupId}:write-llm-model-files-tags',
      'group-{groupId}:download-llm-model-files',
      'group-{groupId}:approve-or-process-llm-model-files',
      'group-{groupId}:delete-llm-model-files',
      'group-{groupId}:read-role',
      'group-{groupId}:write-role',
      'group-{groupId}:write-message-template',
      'group-{groupId}:read-message-template',
      'group-{groupId}:delete-message-template',
      'group-{groupId}:write-playground',
      'group-{groupId}:read-dashboard',
      'group-{groupId}:export-logs',
      'group-{groupId}:read-gen-kb',
      'group-{groupId}:write-gen-kb',
      'group-{groupId}:read-entity-snapshot',
      'group-{groupId}:write-entity-snapshot',
      'group-{groupId}:delete-entity-snapshot',
      'group-{groupId}:read-data-promotion-request',
      'group-{groupId}:write-data-promotion-request',
      'group-{groupId}:write-flow',
      'group-{groupId}:read-flow',
      'group-{groupId}:read-flow-bot-request',
      'group-{groupId}:read-flow-playground',
      'group-{groupId}:write-api-resource',
      'group-{groupId}:read-api-resource',
      'group-{groupId}:delete-api-resource-file',
      'group-{groupId}:write-api-resource-file',
      'group-{groupId}:read-flow-debug',
      'group-{groupId}:read-feature-flag',
      'group-{groupId}:write-feature-flag',
      'group-{groupId}:upload-chat-files',
      'group-{groupId}:read-resource-plan',
      'group-{groupId}:write-resource-plan',
      'group-{groupId}:read-llm-model-basic',
      'group-{groupId}:read-insight',
      'group-{groupId}:read-insight-advanced',
      'group-{groupId}:read-insight-report',
      'group-{groupId}:read-insight-template',
      'group-{groupId}:write-insight',
      'group-{groupId}:write-insight-report',
      'group-{groupId}:write-insight-template',
      'group-{groupId}:write-test-case',
      'group-{groupId}:read-test-case',
      'group-{groupId}:read-group-price-report',
      'group-{groupId}:download-group-price-report',
      'group-{groupId}:read-notifications-config',
      'group-{groupId}:write-notifications-config',
      'group-{groupId}:write-global-data-source',
      'group-{groupId}:read-bot-tools',
      'group-{groupId}:launch-app',
      'group-{groupId}:read-scheduler-job',
      'group-{groupId}:write-scheduler-job',
    ],
    systemName: SystemName.GROUP_ADMIN,
    order: 4,
    isCustomRoleTemplateAllowed: false,
  },
  {
    name: 'CONTRIBUTOR',
    permissions: contributorPermissions,
    systemName: SystemName.GROUP_CONTRIBUTOR,
    order: 3,
    isCustomRoleTemplateAllowed: true,
  },
  {
    name: 'MEMBER',
    permissions: memberPermissions,
    systemName: SystemName.GROUP_MEMBER,
    order: 2,
    isCustomRoleTemplateAllowed: true,
  },
  {
    name: 'VISITOR',
    permissions: visitorPermissions,
    systemName: SystemName.GROUP_VISITOR,
    order: 1,
    isCustomRoleTemplateAllowed: true,
    groupTypes: [GroupType.BOT, GroupType.FLOW],
  },
];
