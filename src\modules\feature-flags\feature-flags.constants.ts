export const FEATURE_FLAG = 'featureFlag';

export const enum FeatureFlagKey {
  BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST = 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST',
  BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD = 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD',
  BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_TEST = 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_TEST',
  BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_PROD = 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_PROD',
  BOT_LEVEL_CONTROL_KEY = 'ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN',
  FLOW_LEVEL_CONTROL_KEY = 'ADMIN.CONFIG_FLOW_FEATURE_FLAGS_FOR_GROUP_ADMIN',
  ENABLE_SECONDARY_APPROVAL = 'BOT_FILE_UPLOAD.ENABLE_SECONDARY_APPROVAL',
  ENABLE_DELETE_BOT = 'ADMIN.ENABLE_DELETE_BOT',
  ENABLE_TRUE_DELETE_USER = 'ACCOUNT_MANAGEMENT.ENABLE_TRUE_DELETE_USER',
  ENABLE_USERNAME_PASSWORD_LOGIN = 'AUTH.ENABLE_USERNAME_PASSWORD_LOGIN',
  ENABLE_HKT_SSO_LOGIN = 'AUTH.ENABLE_HKT_SSO_LOGIN',
  AUTH_ENABLE_CROSS_APPS_SSO_LOGIN = 'AUTH.ENABLE_CROSS_APPS_SSO_LOGIN',
  ENABLE_GEN_KB = 'ADMIN.ENABLE_GENKB',
  MAX_UPLOADED_FILES_PER_USER = 'BOT_FILE_UPLOAD.CONFIG_MAX_UPLOADED_FILES_PER_USER',
  CONFIG_MAX_UPLOADED_FILES_PER_USER_PER_DAY = 'BOT_FILE_UPLOAD.CONFIG_MAX_UPLOADED_FILES_PER_USER_PER_DAY',
  CONFIG_FEATURE_FLAG_CACHE_TTL = 'ADMIN.CONFIG_FEATURE_FLAG_CACHE_TTL',
  USER_REPORT_CONFIG = 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST',
  BOT_REPORT_CONFIG = 'ADMIN.CONFIG_DAILY_EMAIL_BOT_REPORT',
  API_KEY_WHITELIST_CONFIG = 'ADMIN.CONFIG_API_KEY_IP_WHITELIST',
  BOT_MAX_CHAT_HISTORY_PER_CHAT_SESSION = 'BOT.CONFIG_MAX_CHAT_HISTORY_PER_CHAT_SESSION',
  DEFAULT_PLAN_SUBSCRIPTION = 'ADMIN.CONFIG_DEFAULT_PLAN_SUBSCRIPTION',
  BOT_PROMPT_ENABLE_PII_CHECK = 'BOT.ENABLE_PROMPE_PII_CHECK',
  ADMIN_CONFIG_NOTIFICATION_POP_UP = 'ADMIN.CONFIG_NOTIFICATION_POP_UP',
  ENABLE_API_KEY_IP_FILTERING = 'BOT.ENABLE_API_KEY_IP_FILTERING',
  INSIGHT_GENERATOR_DEFAULT_LLM_ENGINE_SLUG = 'BOT.INSIGHT_DEFAULT_LLM_ENGINE_SLUG',
  INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS = 'BOT.CONFIG_INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS',
  INSIGHT_GENERATOR_DEFAULT_INSIGHT_TEMPLATE = 'BOT.CONFIG_INSIGHT_GENERATOR_DEFAULT_INSIGHT_TEMPLATE',
  CONFIG_RATE_LIMIT_SEND_ALERT_TEMPLATE = 'ALERT.CONFIG_RATE_LIMIT_SEND_ALERT_TEMPLATE',
  BOT_CHAT_WITH_FILE_CONFIG = 'BOT.CONFIG_CHAT_WITH_FILE_CONFIG',
  BOT_CHAT_WITH_DATA_CONFIG = 'BOT.CONFIG_CHAT_WITH_DATA_CONFIG',
  BOT_FILE_TAGGING_CONFIG = 'BOT.CONFIG_FILE_TAGGING',
  BYPASS_SCAN_MALWARE = 'ADMIN.BYPASS_UPLOAD_FILE_SCAN_MALWARE',
  BYPASS_SCAN_PII = 'ADMIN.BYPASS_UPLOAD_FILE_SCAN_PII',
  PII_SCAN_SUPPORT_FORMAT = 'ADMIN.PII_SCAN_SUPPORT_FORMAT',
  TYPE_DEFINITION_PROMPT = 'BOT.CONFIG_SYSTEM_MESSAGE_TYPE_DEFINITION_PROMPT',
  DATA_SOURCE_AI_SEARCH_SUPPORT_CHAT_WITH_FILE_MAX_NUM = 'DATA_SOURCE.CONFIG_AI_SEARCH.SUPPORT_MAX_CHAT_WITH_FILE_NUM',
  ENABLE_GROUP_ROLE_PAGE = 'BOT.ENABLE_GROUP_ROLE_PAGE',
  LOGIN_TOKEN_EXPIRY_TIME = 'ADMIN.CONFIG_LOGIN_TOKEN_EXPIRY_TIME',
  BOT_MALWARE_RATING_WHITELIST = 'BOT.MALWARE_RATING_WHITELIST',
  ENABLE_SUMMARY_REPORT_CACHE_CHECK = 'SUMMARY.ENABLE_SUMMARY_REPORT_CACHE_CHECK',
  ENABLE_GCS_UPLOAD = 'BOT.ENABLE_GCS_UPLOAD',
  ENABLE_INSIGHT_GENERATOR_ENABLE_FILE_SOURCE = 'BOT.ENABLE_INSIGHT_GENERATOR_ENABLE_FILE_SOURCE',
  CONFIG_ROLES_SEE_ALL_SCHEDULER_JOB = 'ADMIN.CONFIG_ROLES_SEE_ALL_SCHEDULER_JOB',
}
