-- This is an empty migration.
UPDATE "LlmEngine" SET "type" = 'IMAGE' WHERE "slug" in ('vertexai-imagen-3.0-fast-generate-001', 'vertexai-imagen-3.0-generate-001', 'dalle-3', 'sd-txt2img'); 

DO $$
DECLARE slug TEXT;
llmName TEXT;
llmEngineRecord RECORD;
planOneIds INTEGER[];
planOneValueIds INTEGER[];
planTwoIds INTEGER[]; 
planTwoValueIds INTEGER[];
BEGIN

FOR llmEngineRecord IN 
SELECT * FROM "LlmEngine" llm WHERE "type" = 'IMAGE'
LOOP

slug:= llmEngineRecord."slug";
llmName:= llmEngineRecord."name";

WITH updated_planOnes AS (UPDATE "Plan" SET 
"planName" = '3000 Images Per Month',  
"description" = 'To allow maximum ' || llmName ||' 3000 images usage per month'
WHERE "planKey" LIKE  
'%' || slug || '-plan-1%' RETURNING id ) 
SELECT ARRAY_AGG(id) INTO planOneIds FROM updated_planOnes;

SELECT ARRAY_AGG("quotaValueId") INTO planOneValueIds FROM "PlanQuota" WHERE "planId" = ANY(planOneIds);

WITH updated_planTwos AS (UPDATE "Plan" SET 
"planName" = '1000 Images Per Month',  
"description" = 'To allow maximum ' || llmName ||' 1000 images usage per month'
WHERE "planKey" LIKE  
'%' || slug || '-plan-2%' RETURNING id)
SELECT ARRAY_AGG(id) INTO planTwoIds FROM updated_planTwos;

SELECT ARRAY_AGG("quotaValueId") INTO planTwoValueIds FROM "PlanQuota" WHERE "planId" = ANY(planTwoIds);

UPDATE "ResourceQuotaRule" SET "quotaType" = 'ImageNumberLimitPerMonth',
"description" = 'To declare maximum image number usage of '|| llmName || ' per month'
WHERE "ruleKey" LIKE '%'|| slug || '%';
UPDATE "ResourceQuotaValue" SET "value" = 3000 WHERE id = ANY(planOneValueIds);
UPDATE "ResourceQuotaValue" SET "value" = 1000 WHERE id = ANY(planTwoValueIds);
END LOOP;
end; $$;