/*
  Warnings:

  - You are about to drop the column `createdBy` on the `MessageTemplate` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[slug,groupId]` on the table `MessageTemplate` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `slug` to the `MessageTemplate` table without a default value. This is not possible if the table is not empty.
*/
-- AlterTable
ALTER TABLE "MessageTemplate" DROP COLUMN "createdBy",
ADD COLUMN     "slug" TEXT NOT NULL DEFAULT '';

-- Update the slug field based on the title column, converting it to lowercase and removing spaces and other symbols
UPDATE "MessageTemplate" SET "slug" = REGEXP_REPLACE(LOWER("title"), '[^a-z0-9]+', '', 'g') where "slug" = '';

-- CreateIndex
CREATE UNIQUE INDEX "MessageTemplate_slug_groupId_key" ON "MessageTemplate"("slug", "groupId");
