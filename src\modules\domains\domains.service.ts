import {
  Injectable,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Domain } from '@prisma/client';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { DnsService } from '../../providers/dns/dns.service';
import { Expose } from '../../providers/prisma/prisma.interface';
import { PrismaService } from '../../providers/prisma/prisma.service';
import { DOMAIN_VERIFICATION_HTML, DOMAIN_VERIFICATION_TXT } from './domains.constants';
import { DomainVerificationMethods } from './domains.interface';

@Injectable()
export class DomainsService {
  constructor(
    private prisma: PrismaService,
    private dnsService: DnsService,
    private configService: ConfigService,
  ) {}
  async verifyDomain(
    groupId: number,
    id: number,
    method?: DomainVerificationMethods,
  ): Promise<Expose<Domain>> {
    const domain = await this.prisma.domain.findUnique({
      where: { id },
    });
    if (!domain) throw new ApiException(ErrorCode.DOMAIN_NOT_FOUND);
    if (domain.groupId !== groupId) throw new ApiException(ErrorCode.UNAUTHORIZED_RESOURCE);

    if (method === DOMAIN_VERIFICATION_TXT || !method) {
      const txtRecords = await this.dnsService.lookup(domain.domain, 'TXT');
      if (JSON.stringify(txtRecords).includes(domain.verificationCode)) {
        await this.prisma.domain.update({
          where: { id },
          data: { isVerified: true },
        });
      } else if (method) throw new ApiException(ErrorCode.DOMAIN_NOT_VERIFIED);
    }

    if (method === DOMAIN_VERIFICATION_HTML || !method) {
      let verified = false;
      try {
        const response = await fetch(
          `http://${domain.domain}/.well-known/${this.configService.get<string>(
            'meta.domainVerificationFile',
          )}`,
        );
        const serverVerification = await response.text();
        verified = serverVerification.includes(domain.verificationCode);
      } catch (error) {}
      if (verified) {
        await this.prisma.domain.update({
          where: { id },
          data: { isVerified: true },
        });
      } else if (method) throw new ApiException(ErrorCode.DOMAIN_NOT_VERIFIED);
    }
    return domain;
  }
}
