-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "FeatureType" AS ENUM ('INSIGHT_GENERATOR');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SchedulerJobStatus" AS ENUM ('ACTIVE', 'PAUSED');

-- CreateTable
CREATE TABLE "SchedulerJob" (
    "id" SERIAL NOT NULL,
    "schedulerJobId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "featureId" INTEGER NOT NULL,
    "groupId" INTEGER NOT NULL,
    "featureType" "FeatureType" NOT NULL,
    "status" "SchedulerJobStatus" NOT NULL,
    "createdBy" INTEGER NOT NULL,
    "updatedBy" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "schedulerJobRRule" TEXT NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "SchedulerJob_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SchedulerJob_groupId_featureType_idx" ON "SchedulerJob"("groupId", "featureType");

-- CreateIndex
CREATE UNIQUE INDEX "SchedulerJob_featureId_featureType_key" ON "SchedulerJob"("featureId", "featureType");
