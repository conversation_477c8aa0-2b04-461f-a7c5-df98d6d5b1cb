import type { Request as NestRequest } from '@nestjs/common';
import { SystemName } from '@prisma/client';
import type { Request as ExpressRequest } from 'express';

export type MfaMethod = 'NONE' | 'SMS' | 'TOTP' | 'EMAIL';

export interface AccessTokenClaims {
  id: number;
  groupId?: number;
  role?: SystemName;
}

export interface RefreshTokenClaims {
  id: number;
  groupId?: number;
}

export interface TokenResponse {
  scopes: string[];
  accessToken: string;
  refreshToken: string;
}

export interface TotpTokenResponse {
  totpToken: string;
  type: MfaMethod;
  multiFactorRequired: true;
}

export interface AccessTokenParsed {
  id: number;
  scopes: string[];
  type: 'user' | 'api-key';
  sessionId?: number;
  role?: SystemName;
  groupId?: number;
}

export interface MfaTokenPayload {
  id: number;
  type: MfaMethod;
}

type CombinedRequest = ExpressRequest & typeof NestRequest;
export interface UserRequest extends CombinedRequest {
  user: AccessTokenParsed;
}

export interface WikijsAccessTokenPayload {
  email: string;
  groups?: number[];
  iss: string;
  iat?: number;
  exp?: number;
}

export interface OutLookResponse {
  accessToken: string;
  refreshToken: string;
  userExist: boolean;
  userId: number;
}
