import {
  <PERSON>,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  Environment,
  FileEntityType,
  FileHistoryStatus,
  FileType,
  GroupType,
  Prisma,
} from '@prisma/client';
import { Response } from 'express';
import moment from 'moment';
import { Configuration } from 'src/config/configuration.interface';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { QueueModule } from 'src/providers/queue/queue.module';
import { TokensService } from 'src/providers/tokens/tokens.service';
import { AuditLog } from '../audit-logs/audit-log.decorator';
import { UserRequest } from '../auth/auth.interface';
import { ScopeMap } from '../auth/scope-map.decorator';
import { getReportScopeMap, postReportScopeMap } from '../auth/scope-requirement-map.constants';
import { Scopes } from '../auth/scope.decorator';
import { FileHistoryService } from '../file-history/file-history.service';
import { GroupNotificationService } from '../group-notification/group-notification.service';
import { GroupsService } from '../groups/groups.service';
import {
  ActiveMember,
  AdminSummaryData,
  CALENDAR_INTERVAL,
  DashboardData,
  DepartmentBoardData,
  DepartmentBoardSortType,
  LeaderboardData,
  LeaderboardSoryType,
  LeaderboardType,
  SortDataType,
  SummaryData,
} from './summary.interface';
import { SummaryService } from './summary.service';
import { formatDateRange } from './summary.utils';
import { S3Service } from 'src/providers/s3/s3.service';
import { QueueService } from 'src/providers/queue/queue.service';
import { Readable } from 'stream';

@Controller('summary')
@ApiBearerAuth('bearer-auth')
@ApiTags('Summary')
export class SummaryController {
  private logger = new Logger(SummaryController.name);
  private fileProcessQueueUrl?: string;

  constructor(
    private summaryService: SummaryService,
    private sqsService: QueueService,
    private configService: ConfigService,
    private tokenService: TokensService,
    private fileHistoryService: FileHistoryService,
    private s3Service: S3Service,
    private groupsService: GroupsService,
    private readonly groupNotificationService: GroupNotificationService,
  ) {
    const config = this.configService.get<Configuration['queue']>('queue');
    this.fileProcessQueueUrl = config.fileQueueUrl;
  }

  @Get('/admin/dashboard/leaderboard')
  @Scopes('group-*:read-dashboard')
  async fetchLeaderboardByType(
    @Query('leaderboardType') leaderboardType: LeaderboardType,
    @Query('sort') sort: LeaderboardSoryType,
    @Query('fetchDataType')
    fetchDataType: SortDataType,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<LeaderboardData[]> {
    const { fromDate, toDate } = formatDateRange(from, to);
    const leaderboard = await this.summaryService.fetchLeaderboardByType(
      leaderboardType,
      fetchDataType,
      sort,
      fromDate,
      toDate,
    );
    return leaderboard;
  }

  @Get('/admin/dashboard/department')
  @Scopes('group-*:read-dashboard')
  async fetchDepartmentBoardByType(
    @Query('sort') sort: DepartmentBoardSortType,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<DepartmentBoardData[]> {
    const { fromDate, toDate } = formatDateRange(from, to);
    const leaderboard = await this.summaryService.getDepartmentBoardData(sort, fromDate, toDate);
    return leaderboard;
  }

  @Post('report/generate')
  @ScopeMap(postReportScopeMap)
  async getSummaryReport(
    @Req() req: UserRequest,
    @Body('fileType') fileType: FileType,
    @Body('from') from?: string,
    @Body('to') to?: string,
    @Body('entityType') entityType?: FileEntityType,
    @Body('entityId', OptionalIntPipe) entityId?: number,
    @Body('isSendEmail') isSendEmail?: boolean,
    @Body('selectedFields') selectedFields?: string[],
    @Body('notificationKey') notificationKey?: string,
  ) {
    const { fromDate, toDate } = formatDateRange(from, to);
    this.logger.log(
      `received generate report request, fileType: ${fileType}, isSendEmail: ${isSendEmail}`,
    );
    try {
      if (entityType && entityId) {
        await this.summaryService.checkIsEntityIdValid(entityType, entityId);
      }
      if (!entityId) {
        const generatedReport = await this.summaryService.checkIsGeneratedReport(
          req,
          fromDate,
          toDate,
          fileType,
          entityType,
          entityId,
        );
        if (generatedReport) {
          return generatedReport;
        }
      }

      const input: Prisma.FileHistoryUncheckedCreateInput = {
        entityType,
        entityId,
        fileType,
        fileId: await this.tokenService.generateRandomString(10),
        createdAt: moment().format('YYYY-MM-DD'),
        updatedAt: moment().format('YYYY-MM-DD'),
        dateFrom: fromDate,
        dateTo: toDate,
        requesterId: req?.user?.id ?? 0,
        status: FileHistoryStatus.PROCESSING,
      };
      await this.sqsService.sendMessage({
        QueueUrl: this.fileProcessQueueUrl,
        MessageBody: JSON.stringify({ ...input, isSendEmail, selectedFields, notificationKey }),
      });
      return await this.fileHistoryService.create(input);
    } catch (err) {
      this.logger.error(err, 'create summary file error: ');
      if (err instanceof ApiException) {
        throw err;
      }
      throw new ApiException(ErrorCode.CREATE_FILES_FAILED);
    }
  }

  @Post('report/callback')
  @Scopes('update-callback')
  async updateFileCallback(
    @Body('fileId') fileId: string,
    @Body('status') status: FileHistoryStatus,
    @Body('fileType') fileType: FileType,
    @Body('dateFrom') from: string,
    @Body('dateTo') to: string,
    @Body('errorMsg') errorMsg?: string,
    @Body('s3FilePath') s3FilePath?: string,
    @Body('isSendEmail') isSendEmail?: boolean,
    @Body('notificationKey') notificationKey?: string,
  ) {
    try {
      this.logger.log(`file callback input ${fileId} ${status}`);
      const fileHistory = await this.fileHistoryService.updateFileHistory(fileId, {
        s3FilePath,
        errorMsg,
        status,
      });
      // TODO:: isSendEmail will using isSendEmail notificationKey about this need data patch
      if (isSendEmail && s3FilePath) {
        this.logger.log(`send report via email -> ${s3FilePath}, dateFrom: ${from}, dateTo: ${to}`);
        await this.summaryService.sendSummaryReportEmail(fileType, s3FilePath, from, to);
      }
      // currently just the group level have notificationKey.
      if (notificationKey && s3FilePath && fileHistory.entityId) {
        this.groupNotificationService
          .sendGroupReportNotification(fileHistory, from, to, s3FilePath, notificationKey)
          .catch((error: Error) => {
            this.logger.error(
              `groupNotificationService.sendGroupReportNotification ${error.message}`,
              error.stack,
            );
          });
      }
      return fileHistory;
    } catch (err) {
      throw new ApiException(ErrorCode.UPDATE_FILES_FAILED);
    }
  }

  @Get('report/status')
  @ScopeMap(getReportScopeMap)
  async getLogFileStatus(
    @Query('fileId') fileId: string,
    @Query('fileType') fileType: FileType,
    @Query('entityId', OptionalIntPipe) entityId?: number,
  ) {
    try {
      const {
        status,
        errorMsg,
        fileType: fileHistoryType,
      } = await this.fileHistoryService.findByFileId(fileId);
      if (fileHistoryType !== fileType) {
        this.logger.error(
          `The file type is incorrect. Your input: ${fileType} , expected: ${fileHistoryType}`,
        );
        throw new Error();
      }
      return { status, errorMsg };
    } catch (err) {
      this.logger.error(err, 'get file status failed');
      throw new ApiException(ErrorCode.FILE_DOWNLOAD_FAILED);
    }
  }

  @Get('report/download')
  @ScopeMap(getReportScopeMap)
  @AuditLog('download-summary-report')
  async downloadLogFile(
    @Res() res: Response,
    @Query('fileId') fileId: string,
    @Query('fileType') fileType: FileType,
    @Query('entityId', OptionalIntPipe) entityId?: number,
  ) {
    try {
      const bucket = this.configService.get<string>(`s3.reportFilesBuckets`);
      const fileHistory = await this.fileHistoryService.findByFileId(fileId);
      this.logger.log(fileHistory, 'file history found');
      if (fileHistory.fileType !== fileType) {
        this.logger.error(
          `The file type is incorrect. Your input: ${fileType} , expected: ${fileHistory.fileType}`,
        );
        throw new Error();
      }
      if (fileHistory.status !== FileHistoryStatus.COMPLETED || !fileHistory.s3FilePath) {
        throw new ApiException(ErrorCode.DEFAULT);
      }

      res.setHeader('Content-disposition', `attachment; filename=${fileId}-xlsx`);
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      const contentLength = await this.s3Service.getContentLength(bucket, fileHistory.s3FilePath);
      res.setHeader('Content-Length', contentLength);

      const fileResponse = await this.s3Service.get(bucket, fileHistory.s3FilePath);
      (fileResponse.Body as Readable).on('error', (err) => {
        this.logger.error(err, 'report download file');
        res.status(404).send(ErrorCode.FILE_DOWNLOAD_FAILED);
      }).pipe(res);
      
    } catch (err) {
      this.logger.error(err, 'download file error');
      throw new ApiException(ErrorCode.FILE_DOWNLOAD_FAILED);
    }
  }

  @Get('report/list')
  @ScopeMap(getReportScopeMap)
  async listLogFile(
    @Query('entityId', OptionalIntPipe) entityId?: number,
    @Query('entityType') entityType?: FileEntityType,
    @Query('fileType') fileType?: FileType,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    try {
      if (entityType && entityId) {
        await this.summaryService.checkIsEntityIdValid(entityType, entityId);
      }
      const newWhere = { ...where, entityId, entityType, fileType };
      const list = await this.fileHistoryService.list(orderBy, skip, take, newWhere);
      const count = await this.fileHistoryService.getCount(newWhere);
      return {
        list,
        count,
      };
    } catch (err) {
      this.logger.error(err, 'list file history error ');
      if (err instanceof ApiException) {
        throw err;
      }
      throw new ApiException(ErrorCode.LIST_FILE_DOWNLOAD_FAILED);
    }
  }

  @Get('groups/:groupId/activeMember')
  @Scopes('group-{groupId}:read-dashboard')
  async fetchActiveMember(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<ActiveMember[]> {
    const { fromDate, toDate } = formatDateRange(from, to);
    const dashboard = await this.summaryService.fetchActiveMemberList(groupId, fromDate, toDate);
    return dashboard;
  }

  @Get('groups/:groupId/LLmEngines')
  @Scopes('group-{groupId}:read-dashboard')
  async fetchLLmEngines(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('from') from: string,
    @Query('to') to: string,
    @Query('sortType') sortDataType?: SortDataType,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ) {
    const { fromDate, toDate } = formatDateRange(from, to);
    const sortType = sortDataType ? sortDataType : SortDataType.TIEM_RANGE;
    const orderMap = orderBy ? orderBy : ({ CALL_TOTAL: 'desc' } as Record<string, 'asc' | 'desc'>);
    const group = await this.groupsService.getGroup(groupId, {});
    const llmEngines = await this.summaryService.getSummaryLLmEngineList(
      fromDate,
      toDate,
      sortType,
      orderMap,
      skip,
      take,
      group,
    );
    return llmEngines;
  }

  @Get('/admin/dashboard/LLmEngines')
  @Scopes('group-*:read-dashboard')
  async fetchAdminLLmEngines(
    @Query('from') from: string,
    @Query('to') to: string,
    @Query('sortType') sortDataType?: SortDataType,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
  ) {
    const { fromDate, toDate } = formatDateRange(from, to);
    const sortType = sortDataType ? sortDataType : SortDataType.TIEM_RANGE;
    const orderMap = orderBy ? orderBy : ({ CALL_TOTAL: 'desc' } as Record<string, 'asc' | 'desc'>);
    const llmEngines = await this.summaryService.getSummaryLLmEngineList(
      fromDate,
      toDate,
      sortType,
      orderMap,
      skip,
      take,
    );
    return llmEngines;
  }

  @Get('/admin/dashboard')
  @Scopes('group-*:read-dashboard')
  async fetchAdminDashboardChart(
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('interval') interval?: CALENDAR_INTERVAL,
    @Query('groupType') groupType?: GroupType,
    @Query('groupEnv') groupEnv?: Environment,
  ): Promise<{ [key: string]: DashboardData[] }> {
    const { fromDate, toDate } = formatDateRange(from, to);
    const calendarInterval = CALENDAR_INTERVAL[interval]
      ? CALENDAR_INTERVAL[interval]
      : CALENDAR_INTERVAL.day;
    const dashboard = await this.summaryService.fetchAdminDashboardDataByDashboardKeyMap(
      calendarInterval,
      fromDate,
      toDate,
      groupType,
      groupEnv,
    );
    return Object.fromEntries(dashboard);
  }

  @Get('groups/:groupId/dashboard')
  @Scopes('group-{groupId}:read-dashboard')
  async fetchGroupDashboard(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('interval') interval?: CALENDAR_INTERVAL,
  ): Promise<{ [key: string]: DashboardData[] }> {
    const { fromDate, toDate } = formatDateRange(from, to);
    const calendarInterval = CALENDAR_INTERVAL[interval]
      ? CALENDAR_INTERVAL[interval]
      : CALENDAR_INTERVAL.day;
    const dashboard = await this.summaryService.fetchDashboardDataByDashboardKeyMap(
      groupId,
      calendarInterval,
      fromDate,
      toDate,
    );
    return Object.fromEntries(dashboard);
  }

  @Get('groups/:groupId/dashboard/engineSlug')
  @Scopes('group-{groupId}:read-dashboard')
  async fetchGroupDashboardEngineSlug(
    @Param('groupId') groupId?: number,
  ): Promise<{ [key: string]: string[] }> {
    const dashboardEngineSlugs = await this.summaryService.getDashboardEngineSlug(groupId);
    return Object.fromEntries(dashboardEngineSlugs);
  }

  @Get('admin/dashboard/engineSlug')
  @Scopes('group-*:read-dashboard')
  async fetchAdminDashboardEngineSlug(): Promise<{ [key: string]: string[] }> {
    const dashboardEngineSlugs = await this.summaryService.getDashboardEngineSlug();
    return Object.fromEntries(dashboardEngineSlugs);
  }

  @Get('groups/:groupId/dashboard/summaryInfo')
  @Scopes('group-{groupId}:read-dashboard')
  public async getGroupSummaryInfo(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<SummaryData> {
    const { fromDate, toDate } = formatDateRange(from, to);
    return await this.summaryService.getGroupSummaryData(groupId, fromDate, toDate);
  }

  @Get('admin/dashboard/summaryInfo')
  @Scopes('group-*:read-dashboard')
  public async getAdminSummaryInfo(
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('groupType') groupType?: GroupType,
    @Query('groupEnv') groupEnv?: Environment,
  ): Promise<AdminSummaryData> {
    const { fromDate, toDate } = formatDateRange(from, to);
    return await this.summaryService.getAdminSummaryData(fromDate, toDate, groupType, groupEnv);
  }

  @Post('report/sendSecurityDetectionReport')
  @ScopeMap(postReportScopeMap)
  async sendSecurityDetectionReport(@Body() body) {
    this.logger.log(`prepare to send email, body: ${body}`);

    try {
      if (body) {
        await this.fileHistoryService.sendSecurityDetectionReport(body);
      }
      return { status: 200, msg: 'success' };
    } catch (err) {
      throw new ApiException(ErrorCode.UPDATE_FILES_FAILED);
    }
  }
}
