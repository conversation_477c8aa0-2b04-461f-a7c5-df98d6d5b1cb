import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config';
import { Configuration } from 'src/config/configuration.interface';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { InsightReportResDto, UpdateInsightReportDto } from './insight.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { Stream } from 'stream';

@Injectable()
export class InsightReportService {
  private logger = new Logger(InsightReportService.name);
  axios?: AxiosInstance;
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private featureFlagService: FeatureFlagService,
  ) {
    const config = this.configService.get<Configuration['insightGenerator']>('insightGenerator');
    if (config) {
      this.axios = axios.create({
        baseURL: config.host,
        timeout: config.timeout,
      });
    } else {
      this.logger.error('No LLM backend URL set.');
      throw new Error('No LLM Backend URL set.');
    }
  }
  async getInsightReports(groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight reports  - ${groupId}`);
      const res = await this.axios.get<{ list: InsightReportResDto[]; count: number }>(
        `/insight-generator/${groupId}/reports?${queryStr}`,
      );

      const getList = res.data.list.map(async (item) => {
        const user = await this.prisma.user.findUnique({
          where: { id: item.triggeredBy },
          select: { name: true, id: true },
        });
        return { ...item, triggeredBy: user };
      });
      const list = await Promise.all(getList);
      return { list, count: res.data.count };
    } catch (err) {
      this.logger.error(err, `get insight reports failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportCaseOptions(groupId: number) {
    try {
      this.logger.log(`getting insight report case options  - ${groupId}`);
      const res = await this.axios.get(`/insight-generator/${groupId}/reports/case-options`);

      return res.data;
    } catch (err) {
      this.logger.error(err, `getting insight report case options failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportById(insightReportId: number, groupId: number) {
    try {
      this.logger.log(`getting insight report , id  - ${insightReportId} `);
      const res = await this.axios.get<InsightReportResDto>(
        `/insight-generator/${groupId}/report/${insightReportId}`,
      );
      const user = await this.prisma.user.findUnique({
        where: { id: res.data.triggeredBy },
        select: { name: true, id: true },
      });
      return { ...res.data, triggeredBy: user };
    } catch (err) {
      this.logger.error(err, `getting insight report , id  - ${insightReportId} `);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async updateInsightReportById(
    insightReportId: number,
    groupId: number,
    data: UpdateInsightReportDto,
  ) {
    try {
      this.logger.log(`updating insight report , id  - ${insightReportId} `);
      const res = await this.axios.patch(
        `/insight-generator/${groupId}/report/${insightReportId}`,
        data,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `updating insight report , id  - ${insightReportId} `);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportHistoryVersion(groupId: number, insightReportId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight report history version  - ${insightReportId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/report/${insightReportId}/history-version?${queryStr}`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(err, `getting insight report history version failed - ${insightReportId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportArticleSummarizationTokenUsage(insightReportId: number, groupId: number) {
    try {
      this.logger.log(
        `getting insight report article summarization token usage  - ${insightReportId}`,
      );
      const res = await this.axios.get(
        `/insight-generator/${groupId}/report/${insightReportId}/tokens/article-summarization`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `getting insight report article summarization token usage failed - ${insightReportId}`,
      );
    }
  }

  async getInsightReportDetailListById(groupId: number, insightReportId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight report detail list by id  - ${insightReportId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/report/${insightReportId}/articles?${queryStr}`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `getting insight report detail list by id failed - ${insightReportId}`,
      );
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportStatusById(reportId: number, groupId: number) {
    try {
      this.logger.log(`getting insight report status by id  - ${reportId}`);
      const res = await this.axios.get(`/insight-generator/${groupId}/report/${reportId}/status`);

      return res.data;
    } catch (err) {
      this.logger.error(err, `getting insight report status by id failed - ${reportId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightReportOverallSummarizationTokenUsage(insightReportId: number, groupId: number) {
    try {
      this.logger.log(
        `getting insight report overall summarization token usage  - ${insightReportId}`,
      );
      const res = await this.axios.get(
        `/insight-generator/${groupId}/report/${insightReportId}/tokens/overall-summarization`,
      );

      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `getting insight report overall summarization token usage failed - ${insightReportId}`,
      );
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightArticleReportFile(insightReportId: number, groupId: number) {
    try {
      this.logger.log(`getting insight article report file  - ${insightReportId}`);
      const res = await this.axios.get<Stream>(
        `/insight-generator/${groupId}/report/${insightReportId}/article-report-file`,
        {
          responseType: 'stream',
        },
      );
      this.logger.log(`finished getting insight article report file  - ${insightReportId}`);
      return res.data;
    } catch (err) {
      this.logger.error(`failed to get insight report file  - ${insightReportId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightOverallReportFile(groupId: number, insightReportId: number) {
    try {
      this.logger.log(`getting insight overall report file  - ${insightReportId}`);
      const res = await this.axios.get<Stream>(
        `/insight-generator/${groupId}/report/${insightReportId}/overall-report-file`,
        {
          responseType: 'stream',
        },
      );
      this.logger.log(`finished getting insight overall report file  - ${insightReportId}`);
      return res.data;
    } catch (err) {
      this.logger.error(`failed to get insight overall report file  - ${insightReportId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }
}
