import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';
import { OrderByPipe } from 'src/pipes/order-by.pipe';
import { WherePipe } from 'src/pipes/where.pipe';
import { Public } from '../auth/public.decorator';
import { Scopes } from '../auth/scope.decorator';
import { CreateFeatureFlagDTO } from './feature-flags-model.dto';
import { FeatureFlagService } from './feature-flags.service';
import { UserRequest } from '../auth/auth.interface';

@Controller('feature-flags')
@ApiBearerAuth('bearer-auth')
@ApiTags('Feature Flags')
export class FeatureFlagController {
  constructor(private featureFlagService: FeatureFlagService) {}

  @Get()
  @Scopes('user-*:read-feat-flag', 'get-feat-key')
  //TODO will add scope "SUDO" after frontend v1 v2 update to use new api '/client-only' for client side
  async getFeatFlags(
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('where', WherePipe) where?: Record<string, number | string>,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
    @Query('categoryId', OptionalIntPipe) categoryId?: number,
  ) {
    const list = await this.featureFlagService.getAll(skip, take, where, orderBy, categoryId);
    const count = await this.featureFlagService.getCount(where, categoryId);
    return { list, count };
  }

  @Get('/public')
  @Public()
  async getPublicFeatFlags(
    @Query('isEnabledOnly') isEnabledOnly?: boolean,
    @Query('skip', OptionalIntPipe) skip?: number,
    @Query('take', OptionalIntPipe) take?: number,
    @Query('orderBy', OrderByPipe) orderBy?: Record<string, 'asc' | 'desc'>,
  ) {
    const where = {
      isEnabled: isEnabledOnly,
      isPublic: true,
    };
    const list = await this.featureFlagService.getAll(skip, take, where, orderBy);
    const count = await this.featureFlagService.getCount(where);
    return { list, count };
  }

  @Get('/client-only')
  @Scopes('user-*:read-feat-flag')
  async getClientSideFeatFlags(
    @Query('groupId') groupId?: number,
    @Query('isEnabledOnly') isEnabledOnly?: boolean,
  ) {
    const list = await this.featureFlagService.getClientSideFeatureFlags(groupId, isEnabledOnly);
    const count = list ? list.length : 0;
    return { list, count };
  }

  @Get('/:key')
  @Scopes('user-*:read-feat-flag', 'get-feat-key')
  async getFeatFlagByKey(@Param('key') key: string) {
    return await this.featureFlagService.getOne(key);
  }

  @Post()
  @Scopes('system:write-feat-flag')
  async createFeatFlag(@Body() body: CreateFeatureFlagDTO) {
    return await this.featureFlagService.create(body);
  }

  @Put(':id')
  @Scopes('system:write-feat-flag')
  async updateFeatFlag(
    @Req() req: UserRequest,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: CreateFeatureFlagDTO,
  ) {
    return await this.featureFlagService.update(id, body, req);
  }

  @Delete(':id')
  @Scopes('system:write-feat-flag')
  async deleteFeatFlag(@Param('id', ParseIntPipe) id: number) {
    return await this.featureFlagService.delete(id);
  }
}
