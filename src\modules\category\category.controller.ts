import { Controller, Get, Query, ParseIntPipe } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CategoryType } from '@prisma/client';
import { WherePipe } from 'src/pipes/where.pipe';
import { CategoryService } from './category.service';
import { OptionalParseEnumPipe } from '../../pipes/optional-parse-enum.pipe';
import { OptionalIntPipe } from 'src/pipes/optional-int.pipe';

@Controller('category')
@ApiBearerAuth('bearer-auth')
@ApiTags('Category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get()
  async getCategories(
    @Query('categoryType', new OptionalParseEnumPipe(CategoryType)) categoryType?: CategoryType,
    @Query('parentId', OptionalIntPipe) parentId?: number,
  ) {
    return this.categoryService.getCategories(parentId, categoryType);
  }
}
