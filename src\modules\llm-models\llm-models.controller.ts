import {
  Body,
  Controller,
  HttpCode,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Res,
  Get,
  UseInterceptors,
  Req,
} from '@nestjs/common';
import { ApiSecurity } from '@nestjs/swagger';
import { Response } from 'express';
import { ChatResponse } from 'src/providers/llm-backend/llm-backend.interface';
import { LLMBackendService } from '../..//providers/llm-backend/llm-backend.service';
import { ApiException, ErrorCode } from '../../errors/errors.constants';
import { UserRequest } from '../auth/auth.interface';
import { Scopes } from '../auth/scope.decorator';
import { LLMModelsService } from './llm-models.service';
import { PublicLLMModelWhereDto } from './dto/list-llm-model.dto';
import { Readable } from 'stream';
import { CronJobApiKeyScopes } from 'src/constants/cron-job';

@ApiSecurity('x-api-key')
@Controller('llm-models')
export class LLMModelsController {
  private logger = new Logger(LLMModelsController.name);
  constructor(
    private readonly llmModelsService: LLMModelsService,
    private readonly llmBackendService: LLMBackendService,
  ) {}

  @Post('/updateFullScanForLlmguard')
  @Scopes('update-callback')
  async updateFullScanForLlmguard(@Body() body) {
    try {
      return this.llmModelsService.updateFullScanForLlmguard(
        body.id,
        body.s3path,
        body.hasPromptInjection,
        body.hasPII,
        body.detectedPii,
        body.type,
        body.piiFileStatus,
      );
    } catch (error) {
      this.logger.error(error);
      return { status: 500, msg: error };
    }
  }

  @Post(':groupId/fullscan-pii-file/:id')
  @HttpCode(200)
  @Scopes('group-{groupId}:upload-llm-model-files')
  async securityScan(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('id', ParseIntPipe) id: number,
  ) {
    try {
      return this.llmModelsService.securityScan(id);
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  //download (call s3)
  @Get(':groupId/download-fullscan-pii-file/:id')
  @Scopes('group-{groupId}:download-llm-model-files')
  async downloadfullScanPiiFile(
    @Param('id', ParseIntPipe) id: number,
    @Res() response: Response,
    @Query('isChatFile') isChatFile?: boolean,
  ) {
    try {
      const fileResponse = await this.llmModelsService.downloadfullScanPiiFile(id, isChatFile);
      (fileResponse.Body as Readable)
        .on('error', (err) => {
          this.logger.error(err, 'report download failed');
          response.status(404).send(ErrorCode.FILE_DOWNLOAD_FAILED);
        })
        .pipe(response);
    } catch (error) {
      this.logger.error(error, 'download file error');
      throw new ApiException(ErrorCode.FILE_DOWNLOAD_FAILED);
    }
  }

  @Post('/updateFullScanByBullMq')
  @Scopes('update-callback')
  async updateFullScanByBullMq(@Body() body) {
    this.logger.log(`updateFullScanByBullMq body: ${JSON.stringify(body)}`);
    try {
      return this.llmModelsService.updateFilePiiScanResult('modelFile', body);
    } catch (error) {
      this.logger.error(error);
      return { status: 500, msg: error };
    }
  }

  @Post('/updateModelfileForMalwareScan')
  @Scopes('update-callback')
  async updateModelfileForMalwareScan(@Body() body) {
    try {
      this.logger.log(`updateModelfileForMalwareScan body: ${JSON.stringify(body)}`);
      return this.llmModelsService.updateModelfileForMalwareScan(body);
    } catch (error) {
      this.logger.error(error);
      return { status: 500, message: error };
    }
  }

  @Post('/updateChatFilePiiScan')
  @Scopes('update-callback')
  async updateChatFilePiiScanResult(@Body() body) {
    this.logger.log(`updateChatFilePiiScan body: ${JSON.stringify(body)}`);
    try {
      return this.llmModelsService.updateFilePiiScanResult('chatFile', body);
    } catch (error) {
      this.logger.error('updateChatFilePiiScan error', error);
      return { status: 500, msg: error };
    }
  }

  @Post('/updateChatfileForMalwareScan')
  @Scopes('update-callback')
  async updateChatfileForMalwareScan(@Body() body) {
    try {
      this.logger.log(`updateChatfileForMalwareScan body: ${JSON.stringify(body)}`);
      return this.llmModelsService.updateChatfileForMalwareScan(body);
    } catch (error) {
      this.logger.error(error);
      return { status: 500, message: error };
    }
  }

  //download (call s3)
  @Get(':groupId/download-specified-fullscan-pii-file')
  @Scopes('group-{groupId}:download-llm-model-files')
  async downloadSpecifiedfullScanPiiFile(
    @Query('s3Path') s3Path: string,
    @Param('groupId', ParseIntPipe) groupId: number,
    @Res() response: Response,
  ) {
    try {
      const fileResponse = await this.llmModelsService.downloadSpecifiedfullScanPiiFile(
        s3Path,
        groupId,
      );
      (fileResponse.Body as Readable)
        .on('error', (err) => {
          this.logger.error(err, 'report download Specified failed');
          response.status(404).send(ErrorCode.FILE_DOWNLOAD_FAILED);
        })
        .pipe(response);
    } catch (error) {
      this.logger.error(error, 'download Specified file error');
      throw new ApiException(ErrorCode.FILE_DOWNLOAD_FAILED);
    }
  }

  @Get('/public')
  @Scopes('user-*:read-public-bot')
  public async getPublicGroup(
    @Query() publicWhere: PublicLLMModelWhereDto,
    @Req() req: UserRequest,
  ) {
    const { where, skip, take } = publicWhere;
    const publicGroups = await this.llmModelsService.getPublicBots(req, where, null, skip, take);
    return publicGroups;
  }

  @Get('/llm-model-config')
  @Scopes('user-*:read-llm-model-config')
  public async getLLMModelConfig() {
    const config = await this.llmBackendService.getLLMModelConfig();
    return config;
  }

  @Get(':groupId/public/preview')
  @Scopes('group-{groupId}:preview-public-bot', 'group-*:read-info')
  public async getPreivcePublicGroup(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Req() req: UserRequest,
    @Query() publicWhere: PublicLLMModelWhereDto,
  ) {
    const publicGroups = await this.llmModelsService.getPublicBots(req, publicWhere.where, groupId);
    return publicGroups;
  }

  @Post('/usage-log')
  @Scopes('update-callback')
  async insertUsageLog(@Body() body: { usageLog: ChatResponse }) {
    try {
      this.logger.log(`insertUsageLog body: ${JSON.stringify(body)}`);
      return this.llmModelsService.insertUsageLog(body.usageLog);
    } catch (error) {
      this.logger.error(error);
      return { status: 500, message: error };
    }
  }

  @Post('/transferModelFileToGcs')
  @Scopes(CronJobApiKeyScopes.TRANSFER_S3_TO_GCS)
  public async transferModelFileToGcs(@Body() body) {
    try {
      this.logger.log(`transferModelFileToGcs body: ${JSON.stringify(body)}`);
      return await this.llmModelsService.transferModelFileToGcs(body);
    } catch (error) {
      this.logger.error(error);
      return { status: 500, message: `Failed to transferModelFileToGcs:${error}` };
    }
  }
}
