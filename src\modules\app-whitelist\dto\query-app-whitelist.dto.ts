import { AppWhitelist } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { PrismaPaginationDto, PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';

export class AppWhitelistWhere {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  search?: PrismaStringFilterDto;
}

export class QueryAppWhitelistDto extends PrismaPaginationDto<AppWhitelist> {
  @IsOptional()
  @ValidateNested()
  @Type(() => AppWhitelistWhere)
  where?: AppWhitelistWhere;
}
