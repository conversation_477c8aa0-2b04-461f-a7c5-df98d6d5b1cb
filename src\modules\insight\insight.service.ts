import { Injectable, Logger } from '@nestjs/common';
import {
  CreateInsightCaseBySourceSettingDto,
  InsightCaseHistoryArticleUpdateDto,
  InsightCaseResDto,
  navigateInsightCaseHistoryStepDto,
  UpdateInsightCaseBySummarizationSettingDto,
  UpdateInsightCaseHistoryBySourceSettingDto,
} from './insight.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { PrismaService } from 'src/providers/prisma/prisma.service';
import { FeatureFlagService } from '../feature-flags/feature-flags.service';
import { FeatureFlagKey } from '../feature-flags/feature-flags.constants';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { Configuration } from 'src/config/configuration.interface';
import { ConfigService } from '@nestjs/config';
import { FeatureType } from '@prisma/client';

@Injectable()
export class InsightService {
  private logger = new Logger(InsightService.name);
  axios?: AxiosInstance;
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private featureFlagService: FeatureFlagService,
  ) {
    const config = this.configService.get<Configuration['insightGenerator']>('insightGenerator');
    if (config) {
      this.axios = axios.create({
        baseURL: config.host,
        timeout: config.timeout,
      });
    } else {
      this.logger.error('No LLM backend URL set.');
      throw new Error('No LLM Backend URL set.');
    }
  }

  async getInsightCaseSearchEngines(groupId: number) {
    try {
      this.logger.log(`getting insight cases search engines`);
      const res = await this.axios.get<{ list: { key: string; value: string }[] }>(
        `/insight-generator/${groupId}/cases/search-engines`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight cases search engines failed`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseDefaultLLMEngine(groupId: number) {
    const defaultLLMFeatureFlag = await this.featureFlagService.getOverrideFeatureFlagOrDefault(
      groupId,
      FeatureFlagKey.INSIGHT_GENERATOR_DEFAULT_LLM_ENGINE_SLUG,
    );
    return defaultLLMFeatureFlag.metaData['value'];
  }

  async getInsightCaseLLMEngines(groupId: number) {
    const supportedLLMFeatureFlag = await this.featureFlagService.getOne(
      FeatureFlagKey.INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS,
    );
    const group = await this.prisma.group.findUnique({
      select: { env: true },
      where: { id: groupId },
    });
    if (!group) {
      throw new ApiException(ErrorCode.GROUP_NOT_FOUND);
    }
    const supportedLLmList: string[] = supportedLLMFeatureFlag.metaData['values'] ?? [];
    const llmEngines = await this.prisma.llmEngine.findMany({
      where: { slug: { in: supportedLLmList }, isActive: true },
      orderBy: {
        sequence: 'asc',
      },
    });
    return llmEngines;
  }

  async getInsightCases(groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight cases  - ${groupId}`);
      const res = await this.axios.get<{ list: InsightCaseResDto[]; count: number }>(
        `/insight-generator/${groupId}/cases?${queryStr}`,
      );
      const getList = res.data.list.map(async (item) => {
        const creator = await this.prisma.user.findUnique({
          where: { id: item.createdBy },
          select: { name: true, id: true },
        });
        let updatedBy = null;
        if (item.updatedBy) {
          updatedBy = await this.prisma.user.findUnique({
            where: { id: item.updatedBy },
            select: { name: true, id: true },
          });
        }
        const schedulerJob = await this.prisma.schedulerJob.findFirst({
          where: { featureId: item.id, featureType: FeatureType.INSIGHT_GENERATOR },
          select: {
            id: true,
            status: true,
            schedulerJobRRule: true,
          },
        });
        return {
          ...item,
          createdBy: creator,
          updatedBy,
          schedulerJobId: schedulerJob?.id,
          genReportSchedulerJob: schedulerJob ? true : false,
          genReportSchedulerJobStatus: schedulerJob?.status,
        };
      });
      const list = await Promise.all(getList);
      return { list, count: res.data.count };
    } catch (err) {
      this.logger.error(err, `get insight cases failed - ${groupId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseById(insightId: number, groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight case , id  - ${insightId} `);
      const res = await this.axios.get<InsightCaseResDto>(
        `/insight-generator/${groupId}/case/${insightId}?${queryStr}`,
      );
      const creator = await this.prisma.user.findUnique({
        where: { id: res.data.createdBy },
        select: { name: true, id: true },
      });
      let updatedBy = null;
      if (res.data.updatedBy) {
        updatedBy = await this.prisma.user.findUnique({
          where: { id: res.data.updatedBy },
          select: { name: true, id: true },
        });
      }
      return { ...res.data, createdBy: creator, updatedBy };
    } catch (err) {
      this.logger.error(err, `getting insight case , id  - ${insightId} `);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async createInsightCaseBySourceSetting(
    userId: number,
    groupId: number,
    data: CreateInsightCaseBySourceSettingDto,
  ) {
    try {
      this.logger.log(
        data,
        `creating insight case by source setting , groupId - ${groupId}, userId -${userId}`,
      );
      const res = await this.axios.post<InsightCaseResDto>(`/insight-generator/${groupId}/case`, {
        ...data,
        groupId,
        createdBy: userId,
      });

      return res.data;
    } catch (err) {
      this.logger.log(
        err,
        `creating insight case by source setting failed, groupId - ${groupId}, userId -${userId}`,
      );
      throw new ApiException(ErrorCode.CREATE_INSIGHT_FAILED);
    }
  }

  async updateInsightCaseHistoryBySourceSetting(
    id: number,
    data: UpdateInsightCaseHistoryBySourceSettingDto,
    groupId: number,
    userId: number,
  ) {
    try {
      this.logger.log(
        data,
        `updating insight case history by source setting, id: ${id}, groupId - ${groupId}, userId - ${userId}`,
      );
      const res = await this.axios.patch<InsightCaseResDto>(
        `/insight-generator/${groupId}/case/${id}/source-setting`,
        { ...data, updatedBy: userId },
      );

      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `updating insight case history by source setting, id: ${id}, groupId - ${groupId}, userId - ${userId}`,
      );
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async getInsightCaseHistoryStep(caseHistoryId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case history step  - ${caseHistoryId}`);
      const res = await this.axios.get<{ insightCaseStep: string }>(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/step`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight case history step failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseHistorySourceSetting(caseHistoryId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case history source setting  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/source-setting`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight case history source setting failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseClassificationInfo(caseHistoryId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case classification info  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/classification-info`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight case classification info failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async navigateInsightCaseHistoryStep(
    caseHistoryId: number,
    data: navigateInsightCaseHistoryStepDto,
    groupId: number,
    userId: number,
  ) {
    try {
      this.logger.log(`navigating insight case history step  - ${caseHistoryId}`);
      const res = await this.axios.patch(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/step`,
        { ...data, updatedBy: userId },
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `navigate insight case history step failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async extractArticlesFromSourceSetting(caseHistoryId: number, groupId: number, userId: number) {
    try {
      this.logger.log(`extracting articles from source setting  - ${caseHistoryId}`);
      const res = await this.axios.post(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/article-extraction`,
        { userId },
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `extract articles from source setting failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async syncArticleFromPreviousCaseHistory(
    caseHistoryId: number,
    originalCaseHistoryId: number,
    groupId: number,
  ) {
    try {
      this.logger.log(`syncing article from previous case history  - ${caseHistoryId}`);
      const res = await this.axios.post(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/article-sync`,
        { originalCaseHistoryId },
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `sync article from previous case history failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async getInsightCaseArticleExtractionTokenUsage(caseHistoryId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case article extraction token usage  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/article-extraction/tokens`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `get insight case article extraction token usage failed - ${caseHistoryId}`,
      );
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseArticleExtractionStatus(caseHistoryId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case article extraction status  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/article-extraction/status`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `get insight case article extraction status failed - ${caseHistoryId}`,
      );
    }
  }

  async getInsightCaseHistoryArticles(caseHistoryId: number, groupId: number, queryStr: string) {
    try {
      this.logger.log(`getting insight case history articles  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/articles?${queryStr}`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight case history articles failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async getInsightCaseHistoryClassifications(caseHistoryId: number, groupId: number) {
    try {
      this.logger.log(`getting insight case history classifications  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/classifications`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `get insight case history classifications failed - ${caseHistoryId}`);
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async updateInsightCaseHistoryArticle(
    data: InsightCaseHistoryArticleUpdateDto,
    id: number,
    groupId: number,
    caseHistoryId: number,
  ) {
    try {
      this.logger.log(`updating insight case history article  - ${id}`);
      const res = await this.axios.patch(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/article/${id}`,
        data,
      );
      return res.data;
    } catch (err) {
      this.logger.error(err, `update insight case history article failed - ${id}`);
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async getInsightCaseHistorySummarizationSetting(groupId: number, caseHistoryId: number) {
    try {
      this.logger.log(`getting insight case history summarization setting  - ${caseHistoryId}`);
      const res = await this.axios.get(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/summarization-setting`,
      );
      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `get insight case history summarization setting failed - ${caseHistoryId}`,
      );
      throw new ApiException(ErrorCode.GET_INSIGHT_FAILED);
    }
  }

  async updateInsightCaseHistorySummarizationSetting(
    data: UpdateInsightCaseBySummarizationSettingDto,
    caseHistoryId: number,
    groupId: number,
    userId: number,
  ) {
    try {
      this.logger.log(`updating insight case history summarization setting  - ${caseHistoryId}`);
      const res = await this.axios.patch(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/summarization-setting`,
        { ...data, updatedBy: userId },
      );
      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `update insight case history summarization setting failed - ${caseHistoryId}`,
      );
      throw new ApiException(ErrorCode.UPDATE_INSIGHT_FAILED);
    }
  }

  async createInsightReportFromInsightCaseHistory(
    caseHistoryId: number,
    groupId: number,
    userId: number,
  ) {
    try {
      this.logger.log(`creating insight report from insight case history  - ${caseHistoryId}`);
      const res = await this.axios.post(
        `/insight-generator/${groupId}/case-history/${caseHistoryId}/create-insight-report`,
        { userId },
      );
      return res.data;
    } catch (err) {
      this.logger.error(
        err,
        `create insight report from insight case history failed - ${caseHistoryId}`,
      );
      if ((err as unknown as AxiosError)?.response?.status === 403) {
        throw new ApiException(ErrorCode.BOT_MONTHLY_TOKEN_USAGE_EXCEEDED);
      }
      throw new ApiException(ErrorCode.CREATE_INSIGHT_FAILED);
    }
  }
}
