import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';

export class InsightCaseFileDto {
  @IsString()
  fileName: string;
  @IsNumber()
  fileSize: number;
  @IsString()
  @IsOptional()
  classification?: string | null;
  @IsString()
  @IsOptional()
  tagging?: string | null;
  @IsString()
  @IsOptional()
  docId?: string | null;
  @IsString()
  gcsPath: string;
}
export class InsightCaseWebSettingDto {
  @IsString()
  @IsOptional()
  query?: string | null;
  @IsNumber()
  @Max(100)
  @Min(1)
  @IsOptional()
  timeFrame?: number | null;
  @IsOptional()
  @IsNumber()
  @Max(100)
  @Min(1)
  maxSearchNum?: number | null;
  @IsString()
  @IsOptional()
  searchEngineSlug?: string | null;
}

export class InsightCaseFileSettingDto {
  @IsEnum(['FILE', 'LINK'])
  dataHandlingMethod: 'FILE' | 'LINK';
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InsightCaseFileDto)
  files: InsightCaseFileDto[];
}

export class CreateInsightCaseBySourceSettingDto {
  @IsString()
  name: string;
  @IsEnum(['WEB', 'FILE'])
  sourceType: 'WEB' | 'FILE';
  @IsOptional()
  @Type(() => InsightCaseWebSettingDto)
  webSetting?: InsightCaseWebSettingDto;
  @IsOptional()
  @Type(() => InsightCaseFileSettingDto)
  fileSetting?: InsightCaseFileSettingDto;
  @IsNumber()
  @IsOptional()
  selectedClassificationTemplateId?: number;
  @IsString()
  classificationInstruction: string;
}

export class UpdateInsightCaseHistoryBySourceSettingDto {
  @IsNumber()
  originalCaseHistoryId: number;
  @IsEnum(['WEB', 'FILE'])
  sourceType: 'WEB' | 'FILE';
  @IsOptional()
  @Type(() => InsightCaseWebSettingDto)
  webSetting?: InsightCaseWebSettingDto;
  @IsOptional()
  @Type(() => InsightCaseFileSettingDto)
  fileSetting?: InsightCaseFileSettingDto;
  @IsNumber()
  @IsOptional()
  selectedClassificationTemplateId?: number;
  @IsString()
  classificationInstruction: string;
}

export class navigateInsightCaseHistoryStepDto {
  @IsEnum(['FIRST_STEP', 'SECOND_STEP', 'FINISH'])
  step: 'FIRST_STEP' | 'SECOND_STEP' | 'FINISH';
}

export class SyncArticleFromPreviousCaseHistoryDto {
  @IsNumber()
  originalCaseHistoryId: number;
}

export class InsightCaseHistoryArticleUpdateDto {
  @IsString()
  classification: string;
}

export class UpdateInsightCaseBySummarizationSettingDto {
  @IsArray()
  @IsNumber({}, { each: true })
  exceptSummarizingArticleIds: number[];
  @IsString()
  llmEngineSlug: string;
  @IsNumber()
  selectedSummarizationTemplateId: number;
  @IsString()
  articleSummarizationInstruction: string;
  @IsString()
  overallSummarizationInstruction: string;
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  recipients?: string[];
  @IsBoolean()
  @IsOptional()
  isAutoSendNotification?: boolean;
}

export class InsightCaseResDto {
  id: number;
  name: string;
  query: string;
  searchEngineSlug: string;
  llmEngineSlug: string;
  domain: string | null;
  timeFrame: number;
  maxSearchNum: number;
  createdBy: number;
  createdAt: Date;
  updatedAt: Date;
  updatedBy: number | null;
  groupId: number;
  latestStatus: string | null;
  latestArticleDate: Date | null;
  latestTotalCompletionToken: number | null;
}

export class InsightReportResDto {
  id: number;
  insightId: number;
  completionTokenConsumed: number | null;
  promptTokenConsumed: number | null;
  recipientNum: number | null;
  triggeredBy: number;
  triggeredAt: Date;
  status: string;
  completedAt: Date | null;
  s3Path: string | null;
  summary: string | null;
  duration: number | null;
  errorMsg: string | null;
  groupId: number;
  createdAt: Date;
  updatedAt: Date;
  jobId: string;
}

export class InsightReportStatusResDto {
  status: string;
  completedCount: number;
  failedCount: number;
}
export class InsightReportDetailResDto {
  id: number;
  title: string;
  link: string;
  summary: string | null;
  relevantScore: number | null;
  date: Date | null;
  promptTokenConsumed: number | null;
  completionTokenConsumed: number | null;
  duration: number | null;
  insightReportId: number;
  status: string;
  jobId: string;
  errorMsg: string | number;
}

export class InsightTemplateDto {
  @IsString()
  name: string;
  @IsOptional()
  @IsNumber()
  order?: number;
  @IsOptional()
  @IsString()
  description?: string;
  @IsOptional()
  @IsString()
  classificationInstruction?: string;
  @IsOptional()
  @IsString()
  articleSummarizationInstruction?: string;
  @IsOptional()
  @IsString()
  overallSummarizationInstruction?: string;
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
  @IsOptional()
  @IsBoolean()
  isSharedToAll?: boolean;
}

export class ModelFileUploadDto {
  @IsArray()
  @IsNumber({}, { each: true })
  modelFileIds: number[];
}

export class UpdateInsightReportDto {
  @IsString()
  name: string;
}
