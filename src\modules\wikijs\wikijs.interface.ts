import { WikiJ<PERSON>GroupRole } from '@prisma/client';
import { IsEmail, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export interface EnableWikijsDTO {
  enabled: boolean;
}

export class CreateWikijsMembershipDTO {
  role: WikiJSGroupRole;
  @IsEmail()
  email: string;
}

export class DeleteWikiMembershipDTO {
  userId: number;
  roles: WikiJSGroupRole[];
}

export class WikiPeerDTO {
  @IsNumber()
  kbId: number;
  @IsString()
  token: string;
  @IsOptional()
  description?: string;
  @IsOptional()
  name?: string;
}

export class WikiPeerKbDTO {
  peerKbs: WikiPeerDTO[];
}

export class GenKBDTO {
  @IsNumber()
  kbId: number;
  @IsOptional()
  description?: string;
  @IsOptional()
  name?: string;
  @IsOptional()
  expiredAt?: Date;
  default: boolean;
}

export class GenKBPeerDTO {
  @IsOptional()
  genkb?: GenKBDTO;
  @IsOptional()
  peerKbs?: GenKBDTO[];
}

export interface WikiJsUserUpdateResponse {
  users: {
    update: {
      responseResult: {
        succeeded: boolean;
        errorCode?: number;
        slug?: string;
        message?: string;
      };
      user?: {
        id: number;
        name?: string;
        email?: string;
        staffId?: string;
      };
    };
  };
}
