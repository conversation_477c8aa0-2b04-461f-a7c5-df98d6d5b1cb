import {
  Controller,
  Delete,
  Get,
  HttpC<PERSON>,
  Header,
  HttpStatus,
  <PERSON><PERSON>,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  ParseIntPipe,
  Post,
  StreamableFile,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiBearerAuth,
  ApiParam,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';
import { StaartAuthGuard } from '../../auth/staart-auth.guard';
import { Scopes } from '../../auth/scope.decorator';
import { BatchProcessorService } from 'src/providers/batch-processor/batch-processor.service';
import { AxiosError, AxiosResponse } from 'axios';
import { ListBatchFilesDto } from '../dto/list-batch-files.dto';
import { ApiException, ErrorCode } from 'src/errors/errors.constants';
import { ExternalApi } from 'src/swagger-document';

@ApiTags('Groups - Batch API')
@ApiBearerAuth()
@Controller('groups/:groupId/batch-files')
@UseGuards(StaartAuthGuard)
export class GroupBatchFilesController {
  private logger = new Logger(GroupBatchFilesController.name);

  constructor(private readonly batchProcessorService: BatchProcessorService) {}

  @Post()
  @ExternalApi()
  @Scopes('group-{groupId}:create-batchfile')
  @ApiOperation({ summary: 'Upload a batch input file (.jsonl)' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The .jsonl file to upload.',
        },
      },
      required: ['file'],
    },
  })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({
    status: 201,
    description: 'Batch input file uploaded successfully.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        groupId: { type: 'number', example: 10452855 },
        s3Path: { type: 'string', example: '10452855/batchFiles/a5125a0f-67e2-4ac5-a314-28c375a51f2a.jsonl' },
        fileSize: { type: 'number', example: 490 },
        originalFilename: { type: 'string', example: 'test-input.jsonl' },
        createdAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:13:10.795Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:13:10.795Z' },
        deletedAt: { type: 'string', format: 'date-time', nullable: true, example: null },
      },
    },
  })
  async uploadBatchFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 200 * 1024 * 1024 }), // 200 MB limit
        ],
      }),
    )
    file: Express.Multer.File,
  ): Promise<unknown> {
    this.logger.log(`[${groupId}] Uploading batch file: ${file.originalname}`);
    // Basic check for .jsonl extension
    if (!file.originalname.toLowerCase().endsWith('.jsonl')) {
      throw new ApiException(ErrorCode.INVALID_FILE_EXT_FOR_BATCH_FILE);
    }
    const response: AxiosResponse<unknown> = await this.batchProcessorService.uploadBatchFile(
      groupId,
      file,
    );
    // Assuming the batch processor returns the created object in response.data
    return response.data;
  }

  @Get() // New List Endpoint
  @ExternalApi()
  @Scopes('group-{groupId}:read-batchfile')
  @ApiOperation({ summary: 'List batch files for a group' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiResponse({
    status: 200,
    description: 'List of batch files retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        batchFiles: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              groupId: { type: 'number', example: 10452855 },
              s3Path: { type: 'string', example: '10452855/batchFiles/a5125a0f-67e2-4ac5-a314-28c375a51f2a.jsonl' },
              fileSize: { type: 'number', example: 490 },
              originalFilename: { type: 'string', example: 'test-input.jsonl' },
              createdAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:13:10.795Z' },
              updatedAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:13:10.795Z' },
              deletedAt: { type: 'string', format: 'date-time', nullable: true, example: null },
            },
          },
        },
        total: { type: 'number', example: 1 },
      },
    },
  })
  async listBatchFiles(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query() queryParams: ListBatchFilesDto,
  ): Promise<unknown> {
    this.logger.log(
      `[${groupId}] Listing batch files with params: ${JSON.stringify(queryParams)}`,
    );
    try {
      const response: AxiosResponse<unknown> = await this.batchProcessorService.listBatchFiles(
        groupId,
        queryParams,
      );
      return response.data;
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error listing batch files: ${String(err)}`);
      this.handleError(err); // Added error handling
    }
  }

  @Get(':batchFileId') // New Get Metadata Endpoint
  @ExternalApi()
  @Scopes('group-{groupId}:read-batchfile')
  @ApiOperation({ summary: 'Get metadata for a specific batch file' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchFileId', description: 'ID of the batch file', type: String })
  @ApiResponse({
    status: 200,
    description: 'Metadata for the batch file retrieved successfully.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', example: 1 },
        groupId: { type: 'number', example: 10452855 },
        s3Path: { type: 'string', example: '10452855/batchFiles/a5125a0f-67e2-4ac5-a314-28c375a51f2a.jsonl' },
        fileSize: { type: 'number', example: 490 },
        originalFilename: { type: 'string', example: 'test-input.jsonl' },
        createdAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:13:10.795Z' },
        updatedAt: { type: 'string', format: 'date-time', example: '2025-04-24T10:13:10.795Z' },
        deletedAt: { type: 'string', format: 'date-time', nullable: true, example: null },
      },
    },
  })
  async getBatchFileMetadata(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchFileId', ParseIntPipe) batchFileId: number,
  ): Promise<unknown> {
    this.logger.log(`[${groupId}] Getting metadata for batch file ${batchFileId}`);
    try {
      const response = await this.batchProcessorService.getBatchFileMetadata(
        groupId,
        batchFileId,
      );
      return response.data;
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error getting batch file meta ${String(err)}`);
      this.handleError(err); // Added error handling
    }
  }

  @Get(':batchFileId/contents') // Renamed path for clarity
  @ExternalApi()
  @Scopes('group-{groupId}:read-batchfile')
  @ApiOperation({ summary: 'Download the content of a batch input file' }) // Updated summary
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchFileId', description: 'ID of the batch file', type: String })
  @Header('Content-Type', 'application/jsonl')
  @Header('Content-Disposition', 'attachment; filename="batch-file.jsonl"')
  @ApiResponse({ // Added ApiResponse decorator for 200 OK (File Download)
    status: 200,
    description: 'Batch file content downloaded successfully.',
    content: {
      'application/jsonl': {
        schema: {
          type: 'string',
          format: 'binary',
          example: '{"custom_id": "request-1", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gpt-4o-mini-batch", "messages": [{"role": "system", "content": "You are a helpful assistant."}, {"role": "user", "content": "What is 2+2?"}]}}\n{"custom_id": "request-2", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gpt-4o-mini-batch", "messages": [{"role": "system", "content": "You are a helpful assistant."}, {"role": "user", "content": "Translate hello to French."}]}}',
        },
      },
    },
  })
  async downloadBatchFileContent( // Renamed method
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchFileId', ParseIntPipe) batchFileId: number,
  ): Promise<StreamableFile> {
    this.logger.log(`[${groupId}] Downloading content for batch file ${batchFileId}`); // Updated log
    try {
      const response = await this.batchProcessorService.downloadBatchFile(groupId, batchFileId);
      return new StreamableFile(response.data);
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error downloading batch file content: ${String(err)}`);
      this.handleError(err); // Added error handling
    }
  }

  @Delete(':batchFileId')
  @ExternalApi()
  @Scopes('group-{groupId}:delete-batchfile')
  @ApiOperation({ summary: 'Delete a batch input file' })
  @ApiParam({ name: 'groupId', description: 'ID of the group', type: String })
  @ApiParam({ name: 'batchFileId', description: 'ID of the batch file', type: String })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({ status: 204, description: 'Batch file deleted successfully.' }) // Added ApiResponse for 204 No Content
  async deleteBatchFile(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Param('batchFileId', ParseIntPipe) batchFileId: number,
  ): Promise<void> {
    this.logger.log(`[${groupId}] Deleting batch file ${batchFileId}`);
    try {
      await this.batchProcessorService.deleteBatchFile(groupId, batchFileId);
    } catch (err: unknown) {
      this.logger.error(`[${groupId}] Error deleting batch file: ${String(err)}`);
      this.handleError(err); // Added error handling
    }
  }

  // Added error handler copied from GroupBatchProcessesController for consistency
  handleError(err: unknown) {
    if (err instanceof ApiException) throw err;
    if (
      typeof err === 'object' &&
      err !== null && // Added null check
      'response' in err &&
      typeof err.response === 'object' &&
      err.response !== null && // Added null check
      'status' in err.response
    ) {
      const axiosError = err as AxiosError;
      if (axiosError.response) {
        if (axiosError.response.status === 400) {
          throw new ApiException(ErrorCode.BAD_REQUEST_FROM_BATCH_PROCESSOR, {
            message: axiosError.message,
            // 
            error: (axiosError.response?.data as any)?.error,
          });
        } else if (axiosError.response.status === 404) {
          throw new ApiException(ErrorCode.NOT_FOUND_FROM_BATCH_PROCESSOR, {
            message: axiosError.message,
            // error: axiosError.response?.data?.error,
            error: (axiosError.response?.data as any)?.error,
          });
        } else if (axiosError.response.status === 409) {
          throw new ApiException(ErrorCode.INVALID_STATUS_FROM_BATCH_PROCESSOR, {
            message: axiosError.message,
            // error: axiosError.response?.data?.error,
            error: (axiosError.response?.data as any)?.error,
          });
        }
      }
    }
    throw new ApiException(ErrorCode.INTERNAL_SERVER_ERROR, {
      error: String(err),
    });
  }
}
