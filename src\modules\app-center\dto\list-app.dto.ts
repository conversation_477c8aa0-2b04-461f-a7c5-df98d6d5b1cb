import { AppResource } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { PrismaPaginationDto, PrismaStringFilterDto } from 'src/providers/prisma/prisma.interface';

export class BotToolsPaginationWhereDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => PrismaStringFilterDto)
  name?: PrismaStringFilterDto;
}

export class BotToolsPaginationDto extends PrismaPaginationDto<AppResource> {
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => BotToolsPaginationWhereDto)
  where: BotToolsPaginationWhereDto;
}
