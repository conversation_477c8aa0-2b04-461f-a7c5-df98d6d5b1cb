import { FeatureFlagSeedDTO } from 'src/modules/feature-flags/feature-flags-model.dto';
import { CategoryKey } from 'src/modules/category/category.constants';

export const defaultFeatureFlag: FeatureFlagSeedDTO[] = [
  {
    key: 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_TEST',
    value: '',
    metaData: { value: 'gpt-4o' },
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_SLUG_PROD',
    value: '',
    metaData: { value: 'gpt-4o' },
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'BOT.CONFIG_MAX_CHAT_HISTORY_PER_CHAT_SESSION',
    value: '',
    metaData: { value: 100 },
    description: 'Max chat history per chat session',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'CHAT_HISTORY_RETENTION_DAYS',
    value: '100',
    metaData: {},
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'ADMIN.CONFIG_BOT_FEATURE_FLAGS_FOR_GROUP_ADMIN',
    value: '',
    metaData: { value: ['CONNECT_API.ALLOWED_HOST_URL_MAP'] },
    description: 'Comma separated keys',
    isEnabled: false,
    isForClientSide: false,
    categoryKey: CategoryKey.BOT_CONNECT_API,
  },
  {
    key: 'ADMIN.CONFIG_NOTIFICATION_POP_UP',
    value: '',
    metaData: { weekdays: [], content: 'this is a default content' },
    description:
      'If the notification needs to be shown on Sunday and Friday, set `weekdays` to `[0, 5]`. Note that Sunday is represented by 0, and the settings for other days will remain unchanged.',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_ANNOUNCEMENT,
  },
  {
    key: 'ADMIN.CONFIG_FLOW_FEATURE_FLAGS_FOR_GROUP_ADMIN',
    value: '',
    metaData: {},
    description: 'Comma separated keys',
    isEnabled: false,
    isForClientSide: false,
    categoryKey: CategoryKey.FLOW_COMMON,
  },
  {
    key: 'BOT_FILE_UPLOAD.ENABLE_SECONDARY_APPROVAL',
    value: '',
    metaData: {},
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_UPLOAD_DATA,
  },
  {
    key: 'ADMIN.ENABLE_DELETE_BOT',
    value: '',
    metaData: {},
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_GROUP_MANAGEMENT,
  },
  {
    key: 'ACCOUNT_MANAGEMENT.ENABLE_TRUE_DELETE_USER',
    value: '',
    metaData: {},
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_ACCOUNT,
  },
  {
    key: 'AUTH.ENABLE_USERNAME_PASSWORD_LOGIN',
    value: '',
    metaData: {},
    description: 'To enable login login',
    isEnabled: true,
    isForClientSide: true,
    isPublic: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
  },
  {
    key: 'AUTH.ENABLE_HKT_SSO_LOGIN',
    value: '',
    metaData: {},
    description: 'Enable HKT SSO Login',
    isEnabled: true,
    isForClientSide: true,
    isPublic: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
  },
  {
    key: 'BOT_FILE_UPLOAD.CONFIG_MAX_UPLOADED_FILES_PER_USER_PER_DAY',
    metaData: { value: 10 },
    isEnabled: true,
    isForClientSide: true,
    description: 'this is the max uploaded files per user per day',
    categoryKey: CategoryKey.BOT_UPLOAD_DATA,
  },
  {
    key: 'ADMIN.CONFIG_SITE_ANNOUNCEMENT_TEXT',
    value: '',
    metaData: {},
    description: '',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_ANNOUNCEMENT,
  },
  {
    key: 'ACCOUNT_MANAGEMENT.CONFIG_DAILY_EMAIL_USER_LIST',
    value: '',
    metaData: {
      recipient: '',
      cc: '',
      bcc: '',
    },
    description: '',
    isEnabled: false,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_REPORT,
  },
  {
    key: 'ADMIN.CONFIG_DAILY_EMAIL_BOT_REPORT',
    value: '',
    metaData: {
      recipient: '',
      cc: '',
      bcc: '',
    },
    description: '',
    isEnabled: false,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_REPORT,
  },
  {
    key: 'ADMIN.CONFIG_DAILY_EMAIL_BOT_REPORT',
    value: 'N',
    metaData: {
      recipient: '',
      cc: '',
      bcc: '',
    },
    description: '',
    isEnabled: false,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_REPORT,
  },
  {
    key: 'CONNECT_API.ENABLE_CONNECT_API',
    value: '',
    metaData: {},
    description: '',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CONNECT_API,
  },
  {
    key: 'FLOW.ENABLE_FLOW_PLAYGROUND',
    value: '',
    metaData: {},
    description: '',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.FLOW_PLAYGROUND,
  },
  {
    key: 'FLOW.ENABLE_FLOW',
    value: '',
    metaData: {},
    description: '',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.FLOW_COMMON,
  },
  {
    key: 'ACCOUNT_MANAGEMENT.CONFIG_SSO_EMAIL_WHITELIST',
    value: '',
    metaData: {
      value: [
        'pccw.com',
        'hkcsl.com',
        'hkcsl.net',
        'viu.tv',
        'corphq.hk.pccw.com',
        'pccwglobal.com',
      ],
    },
    description: '',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_ACCOUNT,
  },
  {
    key: 'ADMIN.CONFIG_FEATURE_FLAG_CACHE_TTL',
    value: '',
    metaData: { value: 86400 },
    description: 'The seconds of feature flag cache TTL.',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_COMMON,
  },
  {
    key: 'ADMIN.ENABLE_GENKB',
    value: '',
    metaData: {},
    description: 'Enable GenKB',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_GENKB,
  },
  {
    key: 'FLOW.BOT_BUILDER_COMPONENT_LLM_LIST',
    value: '',
    metaData: { value: [] },
    description:
      'The available LLM engine options for flow components. Example value: "gpt-35-turbo", "gpt-4"',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.FLOW_PLAYGROUND,
  },
  {
    key: 'CONNECT_API.ALLOWED_HOST_URL_MAP',
    value: '',
    metaData: {},
    description: 'connect API Host Map',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CONNECT_API,
  },
  {
    key: 'BOT.CONFIG_BOT_CHAT_CALL_RATE_LIMIT',
    value: '',
    metaData: {
      TEAMS: { points: 50, duration: 3600 },
      API_KEY: { points: 50, duration: 3600 },
      PLAYGROUND: { points: 50, duration: 3600 },
    },
    description: 'Rate Limit of bot chat',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CALL_CHAT,
  },
  {
    key: 'ALERT.CONFIG_MONTHLY_TOKEN_LIMIT_SEND_ALERT_CONDITION',
    value: '',
    metaData: {
      percentage: [75, 90, 100],
      emailSubject: '[{groupName}] LLM Monthly Token Limit Alert in [{env}]',
      emailContent:
        'Dear Bot Owner and Admin,<br/><br/>' +
        'I hope this email captures your attention. ' +
        'We have monitored that the Actual Usage Ratio of your Bot [bot id:{groupId}, {groupName}] Monthly LLM Token in [{env}] is high as follows:<br/><br/> ' +
        '<table border="1" width="100%" cellspacing="0"><tr style="text-align: center">' +
        '<th>LLM Model</th>' +
        '<th>Env</th>' +
        '<th>Actual Usage Ratio</th>' +
        '<th>Model Plan Quota</th></tr>' +
        '{tr}' +
        '</table>' +
        'If it continues, it may cause some chat sessions or some functions to not respond normally. For more monthly token limit info, ' +
        'please see: <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot-Plan-and-Subscription.aspx?xsdata=MDV8MDJ8fDg0OTY5ZmE5NzBjZDQwMWYyNGU4MDhkY2NiZjY2Y2YxfGM1OTI0ZGE2ZGViMzQyMWJhYTk4NTdiY2JhMGJhMDUwfDB8MHw2Mzg2MDk1MDY0Mjk5MDQxMDR8VW5rbm93bnxWR1ZoYlhOVFpXTjFjbWwwZVZObGNuWnBZMlY4ZXlKV0lqb2lNQzR3TGpBd01EQWlMQ0pRSWpvaVYybHVNeklpTENKQlRpSTZJazkwYUdWeUlpd2lWMVFpT2pFeGZRPT18MXxMMk5vWVhSekx6RTVPamN3Wm1RM1lXRTNPRGs1TURRM09UZzRZMkl5T0RReE5UYzFNR1l5T1RVNFFIUm9jbVZoWkM1Mk1pOXRaWE56WVdkbGN5OHhOekkxTXpVek9EUXlOREU1fDIzMWI5NzcxNzBkYjQwNzkyNGU4MDhkY2NiZjY2Y2YxfDIxNzVlNTc0OTczOTQyODg4YzVlNDU2MzE5YmFlMmU1&sdata=TTA0ays2L25uS1JTa3JhM2VwZXZtMDh1NndZZkpKZUpWR0sxMDZkZHV4ST0%3D&ovuser=c5924da6-deb3-421b-aa98-57bcba0ba050%2C82007479%40corphq.hk.pccw.com&OR=Teams-HL&CT=1725354982326&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiI0OS8yNDA3MTEyODgyNSIsIkhhc0ZlZGVyYXRlZFVzZXIiOmZhbHNlfQ%3D%3D#how-to-upgrade-bot-plan"> Bot’s Monthly Token Limit </a><br/><br/><br/>' +
        '<h3>If it exceeds, </h3>' +
        '<p style="text-indent: 2em">Error Message: The system returns " [400-994-1] Bot monthly token limit exceeded." error message in playground.</p>' +
        '<h3>Recommended Action after exceeding: </h3>' +
        '<p style="text-indent: 2em">Promptly follow our <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot%20Token%20&%20Rate%20Limit.aspx#bot%E2%80%99s-monthly-token-limit"> user guide </a> to apply for monthly token increasing and solve this issue.</p><br/>' +
        'Thank you for your attention and understanding!<br/><br/>' +
        'Best regards,<br/><br/>' +
        'Gen AI Application Support <br/><br/>',
    },
    description: 'config the monthly token limit alert condition and alert content',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_ALERT,
  },
  {
    key: 'ADMIN.CONFIG_API_KEY_IP_WHITELIST',
    value: '',
    metaData: {
      subnetMask: 24,
    },
    description: 'config the setting of api key ip whitelist',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.BOT_API_KEY,
  },
  {
    key: 'ADMIN.DAILY_DETECTION_REPORT_EMAIL_SECURITY_TEAM_LIST',
    value: '',
    metaData: { recipient: [] },
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_REPORT,
  },
  {
    key: 'ADMIN.DAILY_DETECTION_REPORT_EMAIL_APPLICATION_SUPPORT_TEAM_LIST',
    value: '',
    metaData: { recipient: [] },
    description: '',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_REPORT,
  },
  {
    key: 'ADMIN.ENABLE_SEND_DAILY_DETECTION_REPORT_EMAIL',
    value: '',
    metaData: {},
    description: 'Controls whether mailboxes are distributed',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_NOTIFICATION,
  },
  {
    key: 'BOT.ENABLE_PROMPE_PII_CHECK',
    value: '',
    metaData: {},
    description: 'Is the bot prompt PII (Personal Identity Information) check enabled by default ',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_SECURITY,
  },
  {
    key: 'BOT.ENABLE_API_KEY_IP_FILTERING',
    value: '',
    metaData: {},
    description: 'Enable API key ip filtering',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.BOT_API_KEY,
  },
  {
    key: 'BOT.CONFIG_CHAT_WITH_DATA_SUPPORTED_LLM_MODELS',
    value: '',
    metaData: { values: ['gpt-4', 'gpt-4-32k'] },
    description: 'config chat with data supported llm models ',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.BOT_CHAT_WITH_DATA,
  },
  {
    key: 'BOT.INSIGHT_DEFAULT_LLM_ENGINE_SLUG',
    value: '',
    metaData: { value: 'gpt-4.1-mini' },
    description: 'The default llm selection for summarization of insight report',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_COMMON,
  },
  {
    key: 'BOT.CONFIG_INSIGHT_GENERATOR_DEFAULT_LLM_USAGE',
    value: '',
    metaData: {
      reformat: 'gpt-4o-mini',
      extraction: 'vertexai-gemini-2.0-flash-001',
    },
    description: 'config insight generator default llm engine',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_COMMON,
  },
  {
    key: 'BOT.CONFIG_INSIGHT_GENERATOR_SUPPORTED_LLM_MODELS',
    value: '',
    metaData: {
      values: [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-4.1',
        'gpt-4.1-mini',
        'vertexai-gemini-2.0-flash-001',
        'vertexai-gemini-2.0-flash-lite',
      ],
    },
    description: 'config insight generator supported llm models ',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_COMMON,
  },
  {
    key: 'BOT.CONFIG_INSIGHT_GENERATOR_ENABLED',
    value: '',
    metaData: {},
    description: 'Is insight generator enabled',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_COMMON,
  },
  {
    key: 'BOT.CONFIG_INSIGHT_GENERATOR_DEFAULT_INSIGHT_TEMPLATE',
    value: '',
    metaData: {
      classificationInstruction: `
        {"game" : "for example: marvel rival, league of legend" },
        {"education":  "it is related to primary or secondary school, but also university"}
        `,
      articleSummarizationInstruction: 'Please summarize the article within 150 words',
      overallSummarizationInstruction: 'display in markdown format, with link',
    },
    description: 'the default insight template for insight generator',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_INSTRUCTION_TEMPLATE,
  },
  {
    key: 'BOT.CONFIG_INSIGHT_GENERATOR_ENABLE_UPDATE_INSIGHT_CASE',
    value: '',
    metaData: {},
    description: 'Is update insight case enabled',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_INSIGHT_SETTINGS,
  },
  {
    key: 'ACCOUNT_MANAGEMENT.CONFIG_DEACTIVATE_EMAIL_REMINDER',
    value: '',
    metaData: { date: [1, 7, 30] },
    description:
      'This configuration indicates the days before deactivation when reminders should be sent (e.g., 1, 7, and 30 days before the membership deactivates). ',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_ACCOUNT,
  },
  {
    key: 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_TEST',
    value: '',
    metaData: { value: '-' },
    description:
      'This config will control the bot/chat create default instruction when the value is ""  or disabled the create bot will use gpt-service return value and the created chat will use system default',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'BOT.BOT_CREATE_DEFAULT_LLM_ENGINE_INSTRUCTION_PROD',
    value: '',
    metaData: { value: '-' },
    description:
      'This config will control the bot/chat create default instruction when the value is ""  or disabled the create bot will use gpt-service return value and the created chat will use system default',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'AUTO_TEST.CONFIG_ALLOWED_RUN_AUTO_TEST_MODELS',
    value: '',
    metaData: {
      values: ['gpt-4o', 'gpt-4o-mini'],
    },
    description: 'This configuration to filter the user on the test case select models ',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_TEST_AUTOMATION,
  },
  {
    key: 'ALERT.CONFIG_RATE_LIMIT_SEND_ALERT_TEMPLATE',
    value: '',
    metaData: {
      subject: '[{{botName}}] API Rate Limit Exceeded Maximum in [{{env}}]',
      emailContent:
        'Dear Bot Owner and Admin,<br/><br/>' +
        'I hope this email captures your attention. We have monitored that– your Bot [bot id:{{groupId}}, {{botName}}] API call in [ {{backendChannle}} ] has hit its preset Rate Limit, which caused some functions to not respond normally.' +
        '<h3>Issue Details: </h3>' +
        '<p style="text-indent: 2em">1.Error Message: The system returns rate limit exceeded error message in [ {{backendChannled}} ] backend while using. </p>' +
        '<p style="text-indent: 2em">2.Limit Threshold: Maximum allowed calls per hour is [ {{rateLimitPlanNumber}} ] </p>' +
        '<p style="text-indent: 2em">3.The service is expected to automatically resume after a [ {{minutes}} ] wait.  </p>' +
        '<p style="text-indent: 2em">4.For more rate limit info, please see: <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot%20Token%20%26%20Rate%20Limit.aspx#bot%e2%80%99s-api-rate-limit"> Bot’s API Rate Limit </a></p>' +
        '<h3>Recommended Actions: </h3>' +
        '<p style="text-indent: 2em">If feasible, promptly follow our <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Bot%20Token%20&%20Rate%20Limit.aspx#%E2%80%BB-kind-reminder"> user guide </a> to solve this issue. </p><br/>' +
        'Please take action as soon as you receive this email.<br/><br/>Thank you for your attention and understanding!<br/><br/>Best regards,<br/><br/>Gen AI Application Support <br/><br/>',
    },
    description: 'config the rate limit alert content',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_ALERT,
  },
  {
    key: 'BOT.CONFIG_CHAT_WITH_FILE_CONFIG',
    value: '',
    metaData: {
      maxSelectionNum: 15,
      maxFileNum: 15,
      maxFileSize: 7340032,
      warnFileSize: 7340032,
    },
    description: 'configuration of chat with file',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_WITH_FILE,
  },
  {
    key: 'BOT.CONFIG_CHAT_WITH_DATA_CONFIG',
    value: '',
    metaData: {
      maxSelectionNum: 5,
      maxFileNum: 15,
      maxFileSize: 7340032,
    },
    description: 'configuration of chat with data',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_WITH_DATA,
  },
  {
    key: 'AUTO_TEST.CONFIG_AUTO_TEST_EMAIL_NOTIFICATION_TEMPLATE',
    value: '',
    metaData: {
      nonBusy: '02:00',
      subject: '[{{botName}}]-[{{env}}] Test Automation Results Report Notification',
      html:
        'Dear Bot Manager, <br/><br/>' +
        'GenAI platform has automatically completed the latest round of the test case: [{{testCaseName}}] execution, and I am pleased to notify you of the results report. <br/><br/>' +
        'To review the details, please click on the link below.<br/><br/>' +
        '[ <a href="{{resultUrl}}">{{resultUrl1}}</a> ]<br/><br/>' +
        'This will direct you to the test result page where you can view the details. <br/><br/>' +
        '<h3>Key Results Summary: </h3>' +
        '<p style="text-indent: 2em">1.Total Test questions: A total of [{{specificNumber}}] test questions, each executed [{{numberOfLterations}}] times. </p>' +
        '<p style="text-indent: 2em">2.Successful records: Among them, [{{successNumber}}] records passed successfully. </p>' +
        '<p style="text-indent: 2em">3.Failed records: There were [{{failureNumber}}] records that failed. Detailed failure information please Click above test results link to review. </p><br/>' +
        "Please review the report carefully and timely. And take the necessary actions if needed to ensure that your test cases can be executed well and achieve the expected results. If you have any questions or need further assistance, please don't hesitate to contact our support team on [<EMAIL>]. <br/>" +
        'Best regards, <br/>' +
        'Gen AI Application Support',
    },
    description: 'This configuration to auto-test email notification template ',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_TEST_AUTOMATION,
  },
  {
    key: 'BOT.CONFIG_FILE_TAGGING',
    value: '',
    metaData: {
      maxTags: 30,
      maxTagsPerFile: 5,
      allowedModels: ['vertexai-gemini-2.0-flash-lite', 'vertexai-gemini-2.0-flash-001'],
      allowedMimeTypes: ['application/pdf'],
    },
    description:
      'The configuration of max tags and max tag per file for file tagging related features.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_UPLOAD_DATA,
  },
  {
    key: 'ALERT.CONFIG_INVALID_LLM_MODEL_ALERT_TEMPLATE',
    value: '',
    metaData: {
      subject:
        'Notice: Invalid LLM Model during [ {{productName}} ] Executing in [ {{botName}}, {{env}} ]',
      emailContent:
        'Dear Owner/Creator,<br/><br/>' +
        'I hope this email captures your attention. We have monitored that an exception occurred during [ {{productName}} ] executing, resulting in the inability to continue with its execution.<br/><br/>' +
        'Exception Details:<br/>' +
        'Bot: [ {{botName}}/{{botId}}] In [{{env}} ]<br/>' +
        'Product: [ {{productName1}} ]<br/>' +
        'Case Name: [ {{testCaseName}} in test automation ]<br/>' +
        'Exception: [ Invalid LLM Model, {{llmName2}} ]<br/><br/>' +
        'To resolve this issue as soon as possible, we recommend that you check and replace it with a valid LLM model. If you have any questions or require further assistance, please do not hesitate to contact [ <EMAIL> ]. <br/>' +
        'Thank you for your attention and understanding!<br/>' +
        'Best regards,<br/>Gen AI Application Support',
    },
    description: 'config Invalid LLM Model alert content',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.BOT_ALERT,
  },
  {
    key: 'ADMIN.CONFIG_MODEL_PRICE',
    value: '',
    metaData: {
      defaultMargin: 0.1,
    },
    description: 'The configuration of model price.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_BILLING,
  },
  {
    key: 'ADMIN.BYPASS_UPLOAD_FILE_SCAN_MALWARE',
    value: '',
    metaData: {},
    description: 'bypass upload file scan malware',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_SECURITY,
  },
  {
    key: 'ADMIN.BYPASS_UPLOAD_FILE_SCAN_PII',
    value: '',
    metaData: {},
    description: 'by pass upload file scan PII',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_SECURITY,
  },
  {
    key: 'BOT.MALWARE_RATING_WHITELIST',
    value: '',
    metaData: { values: ['CLEAN'] },
    description:
      'User defined whitelist malware level(Unknown, Clean, Malicious, High Risk, Medium Risk, Low Risk)',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_SECURITY,
  },
  {
    key: 'ADMIN.PII_SCAN_SUPPORT_FORMAT',
    value: '',
    metaData: {
      values: ['pdf', 'csv', 'xls', 'xlsx'],
    },
    description: 'The supported formats for PII scan',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_SECURITY,
  },
  {
    key: 'ACCOUNT_MANAGEMENT.CONFIG_DEACTIVATE_ALREADY_EMAIL_REMINDER',
    value: '',
    metaData: {
      date: [45, 90],
      emailSubject: '"GenAI Platform" Account Deactivation',
      emailContent:
        'Dear User,<br/><br/>' +
        "We have noticed that you haven't " +
        'logged into your {{env}} account on Gen AI Platform for [ {{inactiveDay}} ] days.<span style="color: red"> Your account has become inactive</span>. To ensure that your account remains accessible, kindly remind you:<br/><br/>' +
        '<div {{uat}}>For UAT, please follow the <a href="https://forms.office.com/pages/responsepage.aspx?id=pk2SxbPeG0KqmFe8ugugULjg6f5q_r9DmMa79zV9sy1UMDlYMkZUQ1pVV09POFY0TlhXWkxWN0tORSQlQCN0PWcu&route=shorturl">MS Form</a> on website and apply to reactivate your account with Customer Success Team [<EMAIL>]. The process will take 10-14 working days.</div><br/>' +
        '<div {{prod}}>For PRD, please follow our user guide to reactivate your account. For more information, please submit SAP by following <a href="https://pccw0.sharepoint.com/sites/GenerativeAI/SitePages/Gen-AI-Platform---PROD-SAP-Form.aspx">user guide</a>.</div><<br/>' +
        'Kindly Tips: If there is no login activity on your account within 90 days after the last login, the system will automatically consider the account as inactive. Once the account is inactive, access to Gen AI Platform will be restricted.<br/><br/>' +
        'If you have any questions or need assistance, please do not hesitate to contact <span {{uat}}>[<EMAIL>] </span><span {{prod}}>[<EMAIL>]</span>.Thank you.<br/><br/>' +
        'Gen AI Application Support<br/><br/>',
    },
    description: 'config the deactive account remind condition and alert content',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_ALERT,
  },
  {
    key: 'BOT.CONFIG_SYSTEM_MESSAGE_TYPE_DEFINITION_PROMPT',
    value: '',
    metaData: {
      value:
        '\n [Type Definition] Please check the type definition as below. If it matches of you user question, please convert it as needed. \n',
    },
    description: 'The AI type definition prompt',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.BOT_SECURITY,
  },
  {
    key: 'DATA_SOURCE.CONFIG_AI_SEARCH.SUPPORT_MAX_CHAT_WITH_FILE_NUM',
    value: '',
    metaData: {
      value: 3,
    },
    description: 'config the data source ai search can support chat with file max file number',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_WITH_FILE,
  },
  {
    key: 'BOT.ENABLE_GROUP_ROLE_PAGE',
    value: '',
    metaData: {},
    description: 'control whether to show the group role page',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CUSTOM_ROLE,
  },
  {
    key: 'LOGIN.ENABLE_LOGIN_PAGE_CHECKBOX',
    value: '',
    metaData: {},
    description: 'control display the login page checkbox in login page',
    isEnabled: false,
    isForClientSide: true,
    isPublic: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
  },
  {
    key: 'ADMIN.CONFIG_UI_THEME_RESOURCES',
    value: '',
    metaData: {
      logo: 'https://bot-builder-public-files-uat.s3.ap-east-1.amazonaws.com/theme/hkt_logo.svg',
      background:
        'https://bot-builder-public-files-uat.s3.ap-east-1.amazonaws.com/theme/hkt_background.png',
    },
    description: 'control the ui theme resources',
    isEnabled: true,
    isForClientSide: true,
    isPublic: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
  },
  {
    key: 'ADMIN.CONFIG_LOGIN_TOKEN_EXPIRY_TIME',
    value: '',
    metaData: {
      refreshToken: 86400,
    },
    description: 'The configuration of login token expiry time (in seconds) ',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
  },
  {
    key: 'BOT.CONFIG_ENABLE_CHAT_WITH_DATA',
    value: '',
    metaData: {},
    description: 'The configuration of enabling chat with data',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_WITH_DATA,
  },
  {
    key: 'SUMMARY.ENABLE_SUMMARY_REPORT_CACHE_CHECK',
    value: '',
    metaData: {},
    description: 'is to  control the report generator to check cache logic ',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_DASHBOARD,
  },
  {
    key: 'ADMIN.FAQ_BOT_ID',
    value: '',
    metaData: { value: '' },
    description: 'specify the bot id for FAQ bot',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_COMMON,
  },
  {
    key: 'ADMIN.PLAYGROUND_RATING_COMMENT_PRESUPPOSE',
    value: '',
    metaData: {
      one: ['❌ No Relevant Link Attached', '⚠️ Irrelevant Content Generated'],
      two: ['❌ No Relevant Link Attached', '⚠️ Irrelevant Content Generated'],
      three: ['❌ No Relevant Link Attached', '⚠️ Irrelevant Content Generated'],
      four: ['✨ Accurate Results', '😊 Good Tone and Manner'],
      five: ['✨ Accurate Results', '😊 Good Tone and Manner'],
    },
    description: 'for chat result rating and comment in playground',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_RATING_COMMENT,
  },
  {
    key: 'ADMIN.ENABLE_OUTLOOK_PRIVATE_BOT',
    value: '',
    metaData: {},
    description: 'Enables the bot to be used as a private bot in Outlook.',
    isEnabled: false,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_OUTLOOK_ADDIN,
  },
  {
    key: 'AUTH.ENABLE_CROSS_APPS_SSO_LOGIN',
    value: '',
    metaData: {},
    description: 'enable cross apps sso  login ',
    isEnabled: true,
    isForClientSide: true,
    isPublic: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
  },
  {
    key: 'BOT.ENABLE_GCS_UPLOAD',
    value: '',
    metaData: {},
    description: 'enable gcs upload',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_WITH_FILE,
  },
  {
    key: 'BOT.ENABLE_INSIGHT_GENERATOR_ENABLE_FILE_SOURCE',
    value: '',
    metaData: {},
    description: 'enable insight generator enable file source',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_INSIGHT_SETTINGS,
  },
  {
    key: 'AUTH.ENABLE_REQUEST_ACCESS',
    value: '',
    metaData: {},
    description: 'enable request access.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_LOGIN,
    isPublic: true,
  },
  {
    key: 'ACCOUNT_MANAGEMENT.ENABLE_USER_PROFILE',
    value: '',
    metaData: {},
    description: 'enable your profiles pop-up to show or hidden.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_ACCOUNT,
  },
  {
    key: 'ADMIN.ENABLE_USER_GUIDE',
    value: '',
    metaData: {},
    description: 'enable user guide.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.SYSTEM_COMMON,
  },
  {
    key: 'BOT.CONTROL_ALLOW_USER_UPLOAD_MAX_IMAGES',
    value: '',
    metaData: {
      max: 10,
    },
    description: 'control Allow user to copy & paste max screenshot/images.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
  {
    key: 'ADMIN.CONFIG_ROLES_SEE_ALL_SCHEDULER_JOB',
    value: '',
    metaData: {
      roles: ['SUDO'],
    },
    description: 'config who roles see all scheduler job',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.SYSTEM_SECURITY,
  },
  {
    key: 'INSIGHT_GENERATOR.CONFIG_REPORT_COMPLETION_NOTIFICATION_TEMPLATE',
    value: '',
    metaData: {
      subject: '[{{groupName}}] Latest Insight Report Completion Notification',
      emailContent:
        'Dear Recipient, <br/><br/>' +
        'I am pleased to notify you that there is an insight report generated from the insight generator [ {{insightSettingName}} ] and is ready for review. <br/><br/>' +
        'To review the details, please click on the link below.  <br/>' +
        '[ <a href="{{insightReportUrl}}">{{insightReportUrlDisplay}}</a> ]  <br/>' +
        'This will direct you to the insight report page where you can view the details.  <br/><br/>' +
        'Please note that the insights report will be saved for 1 month. Please review and download the report timely if needed. If you have any questions or need further assistance, please do not hesitate to contact our support team on [<EMAIL>].  <br/>' +
        'Best regards, <br/><br/>' +
        'GenAI Application Support',
    },
    description: 'Config the insight report completion notification content',
    isEnabled: true,
    isForClientSide: false,
    categoryKey: CategoryKey.INSIGHT_GENERATOR_NOTIFICATION,
  },
  {
    key: 'BOT.CONTROL_ALLOW_USER_UPLOAD_MAX_SIZE_IMAGE',
    value: '',
    metaData: {
      max: 7340032,
    },
    description: 'control Allow users the maximum size of the pictures.',
    isEnabled: true,
    isForClientSide: true,
    categoryKey: CategoryKey.BOT_CHAT_SETTING,
  },
];
