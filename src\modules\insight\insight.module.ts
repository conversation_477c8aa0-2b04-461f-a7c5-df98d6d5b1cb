import { Modu<PERSON> } from '@nestjs/common';
import { InsightController } from './insight.controller';
import { InsightService } from './insight.service';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from 'src/providers/prisma/prisma.module';
import { FeatureFlagModule } from '../feature-flags/feature-flags.module';
import { InsightInputFileController } from './inputFile.controller';
import { InsightReportController } from './insightReport.controller';
import { InsightInputFileService } from './inputFile.service';
import { InsightReportService } from './insightReport.service';
import { InsightTemplateService } from './insightTemplate.service';
import { InsightTemplateController } from './insightTemplate.controller';
@Module({
  imports: [ConfigModule, PrismaModule, FeatureFlagModule],
  controllers: [
    InsightController,
    InsightInputFileController,
    InsightReportController,
    InsightTemplateController,
  ],
  providers: [
    InsightService,
    InsightInputFileService,
    InsightReportService,
    InsightTemplateService,
  ],
  exports: [InsightService],
})
export class InsightModule {}
