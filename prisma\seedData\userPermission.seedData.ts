import { Environment } from '@prisma/client';
import { PermissionGroupSettingSeed } from './groupPermission.seedData';

export interface UserPermissionSeed {
  description: string;
  permissionKey: string;
  envs?: Environment[];
  groupSetting?: PermissionGroupSettingSeed;
}

export const userPermissionList: UserPermissionSeed[] = [
  {
    description: 'Create and update emails',
    permissionKey: 'user-{userId}:write-email',
  },
  {
    description: 'Read emails',
    permissionKey: 'user-{userId}:read-email',
  },
  {
    description: 'Delete emails',
    permissionKey: 'user-{userId}:delete-email',
  },
  {
    description: 'Read memberships',
    permissionKey: 'user-{userId}:read-membership',
  },
  {
    description: 'Delete memberships',
    permissionKey: 'user-{userId}:delete-membership',
  },
  {
    description: 'Read sessions',
    permissionKey: 'user-{userId}:read-session',
  },
  {
    description: 'Log out of sessions',
    permissionKey: 'user-{userId}:delete-session',
  },
  {
    description: 'Read user details',
    permissionKey: 'user-{userId}:read-info',
  },
  {
    description: 'Update user details',
    permissionKey: 'user-{userId}:write-info',
  },
  {
    description: 'Delete user account',
    permissionKey: 'user-{userId}:deactivate',
  },
  {
    description: 'Read User bookmarked',
    permissionKey: 'user-{userId}:read-bookmark',
  },
  {
    description: 'Write User bookmarked group',
    permissionKey: 'user-{userId}:write-bookmark',
  },
  {
    description: 'Read rate card',
    permissionKey: 'user-{userId}:read-ratecard',
  },
  {
    description: 'Clear user cache',
    permissionKey: 'user-{userId}:clear-cache',
  },
  {
    description: 'Read feature flags',
    permissionKey: 'user-*:read-feat-flag',
  },
  {
    description: 'Read Public Chat List',
    permissionKey: 'user-*:read-public-bot',
  },
  {
    description: 'Read Public Playground',
    permissionKey: 'user-*:read-playground',
  },
  {
    description: 'Use Share Chat',
    permissionKey: 'user-*:read-share-chat',
  },
  {
    description: 'read create apps options',
    permissionKey: 'user-*:read-app-options',
  },
  {
    description: 'Read llm model config',
    permissionKey: 'user-*:read-llm-model-config',
  },
];
